version: '3.8'

services:
  verazial-admin:
    container_name: verazial-admin-app
    image: verazial/admin:v2.0.16
    build:
      context: ..
      dockerfile: deployment-package/Dockerfile
    ports:
      - "${HOST_PORT:-80}:80"
    environment:
      # gRPC Configuration
      - GRPC_API_GATEWAY=${GRPC_API_GATEWAY:-https://dev-gatewaygrpc.verazial.com}
      - GRPC_TOKEN=${GRPC_TOKEN:-}
      - STATIC_API_GATEWAY_URL=${STATIC_API_GATEWAY_URL:-true}
      
      # Konektor Configuration
      - KONEKTOR_API=${KONEKTOR_API:-https://localhost:8443}
      
      # Credentials API Configuration
      - CREDENTIALS_API=${CREDENTIALS_API:-https://dev-pass.verazial.com/api/v1/}
      - CREDENTIAL_USER=${CREDENTIAL_USER:-verazial}
      - CREDENTIAL_PASSWORD=${CREDENTIAL_PASSWORD:-@4S9w&JMhg27}
    restart: unless-stopped
    networks:
      - verazial-network

networks:
  verazial-network:
    external: true
