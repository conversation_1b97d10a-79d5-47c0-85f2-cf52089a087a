import {Component, EventEmitter, Input, OnC<PERSON><PERSON>, On<PERSON><PERSON>roy, OnInit, Output, SimpleChanges} from "@angular/core";
import {<PERSON><PERSON>anitizer, SafeResourceUrl} from "@angular/platform-browser";
import {TranslateService} from "@ngx-translate/core";
import {ConfirmationService, MessageService} from "primeng/api";
import {WidgetResult} from "src/verazial-common-frontend/modules/shared/models/widget-response.model";
import {
    KonektorPropertiesEntity
} from "src/verazial-common-frontend/core/general/konektor/domain/entity/konektor-properties.entity";
import {GeneralSettings} from "src/verazial-common-frontend/core/general/manager/common/models/general-settings.model";
import {ConsoleLoggerService} from "src/verazial-common-frontend/core/services/console-logger.service";
import {LocalStorageService} from "src/verazial-common-frontend/core/services/local-storage.service";
import {getTechId} from "src/verazial-common-frontend/core/util/supporting-functions";
import {
    CheckPermissionsService,
    ValidTenantResult
} from "src/verazial-common-frontend/core/services/check-permissions-service";

@Component({
    selector: 'app-widget-search',
    templateUrl: './widget-search.component.html',
    styleUrl: './widget-search.component.css',
    providers: [MessageService, ConfirmationService]
})
export class WidgetSearchComponent implements OnInit, OnChanges, OnDestroy {

    widgetElementId: string = "widgetIframeSearch";

    /* Inputs */
    @Input() numId: string = "";
    @Input() widgetUrl: string = "";
    @Input() managerSettings?: GeneralSettings;
    @Input() konektorProperties?: KonektorPropertiesEntity;
    @Input() ready: boolean = false;
    @Input() technology: string = "";
    @Input() getToken: boolean = false;
    @Input() segmentedSearchAttributes: { name: string, value: string, secondSearch?: string }[] = [];
    @Input() serverOption: string = "server_biodata";
    @Input() isHidden: boolean = false;
    @Input() widgetMessage = undefined;
    /* Outputs */
    @Output() result = new EventEmitter<WidgetResult>();
    /* Widget */
    showSearchDialog: boolean = false; // Show Search Dialog when Widget is not in enroll mode
    showWidget: boolean = true; // Show Enroll Widget when user has biometric data
    widgetMatchURL: string = ""; // Widget Match URL
    pageWidgetURL: SafeResourceUrl = ""; // URL to use in iframe
    searchReady: boolean = false; // Search Widget is Ready to be used
    waitSelectTech: boolean = false; //
    searchStarted: boolean = false; // The verification process has started
    tech: string = ""; // Technology to search
    /* Biometric Technologies */
    print: boolean = false;
    iris: boolean = false;
    facial: boolean = false;
    tenantMessage: string = "";
    /* Tech Buttons */
    fingerprintActive: boolean = false;
    facialActive: boolean = false;
    irisActive: boolean = false;

    constructor(
        private loggerService: ConsoleLoggerService,
        private translateService: TranslateService,
        private localStorageService: LocalStorageService,
        private messageService: MessageService,
        public sanitizer: DomSanitizer,
        public checkPermissionsService: CheckPermissionsService,
    ) {
        window.addEventListener('beforeunload', this.handleBeforeUnload.bind(this));
        this.receiveWidgetSearchMessage = this.receiveWidgetSearchMessage.bind(this);
    }

    ngOnInit(): void {
        if (this.widgetUrl !== "" && this.ready) {
            this.widgetInitialized();
        }
    }

    ngOnChanges(changes: SimpleChanges): void {
        if (changes['ready'] && changes['ready'].currentValue) {
            this.showWidget = true;
            this.searchReady = false;
            this.waitSelectTech = false;
            this.searchStarted = false;
            this.widgetInitialized();
        }
    }

    widgetInitialized() {
        this.showSearchDialog = this.ready;
        this.setUrls();
        window.addEventListener("message", this.receiveWidgetSearchMessage);
        if (this.numId != "") {
            this.loadWidget(this.serverOption)
        } else {
            this.loadWidgetSearch(this.technology);
        }
    }

    ngOnDestroy(): void {
        window.removeEventListener("message", this.receiveWidgetSearchMessage);
        const iframeSearch = document.getElementById(this.widgetElementId) as HTMLIFrameElement
        if (iframeSearch != null) {
            iframeSearch.contentWindow?.postMessage({
                operation: "reset"
            }, this.widgetMatchURL);
        }
    }

    handleBeforeUnload(event: Event) {
        this.ngOnDestroy();
    }

    /* Widget Functions */

    setUrls() {
        this.widgetMatchURL = this.widgetUrl + "/VerazialIDMatch";
        this.pageWidgetURL = this.sanitizer.bypassSecurityTrustResourceUrl(this.widgetMatchURL + `?iduser=${this.localStorageService.getUser()?.numId ?? ''}&language=${this.localStorageService.getLanguage()}${this.getToken ? `&generateToken=true` : ''}`);
    }

    startWidget() {
        if (this.searchStarted) {
            this.loadWidgetSearch(this.technology);
        } else {
            if (this.numId != "") {
                this.loadWidget(this.serverOption)
            } else {
                this.loadWidgetSearch(this.technology);
            }
        }
    }

    async loadWidget(operation: string) {
        this.loggerService.debug("Load Widget: " + operation);
        const iframe = document.getElementById(this.widgetElementId) as HTMLIFrameElement
        if (iframe != null) {
            let postData: any = {
                operation: operation,
                readonly: false,
            }
            if (this.numId != "") {
                postData.nid = this.numId;
            }
            iframe.contentWindow?.postMessage(postData, this.widgetMatchURL);
        } else {
            // this.loggerService.error("Iframe not found");
        }
    }

    async loadWidgetSearch(tech: string) {
        const iframeSearch = document.getElementById(this.widgetElementId) as HTMLIFrameElement

        this.loggerService.debug("1:n BIOMETRIC SEARCH ATTRIBUTES:");
        this.loggerService.debug(this.segmentedSearchAttributes);

        const newMessage = this.widgetMessage !== undefined ? this.widgetMessage : {
            nid: this.numId,
            tech: tech,
            attributes: this.segmentedSearchAttributes
        }

        if (iframeSearch != null) {
            iframeSearch.contentWindow?.postMessage(newMessage, this.widgetMatchURL);
        }
    }

    closeSearchDialog() {
        window.removeEventListener("message", this.receiveWidgetSearchMessage);
        this.showSearchDialog = false;
        this.searchStarted = false;
        this.ready = false;
        let result = new WidgetResult();
        result = {
            action: "close_search",
            result: "success",
            message: "Widget closed successfully",
            data: {}
        }
        this.result.emit(result);
        this.ngOnDestroy();
    }

    async search(tech: string) {
        if (tech == "fingerprint" && !this.print) {
            this.messageService.add({
                severity: 'warn',
                summary: this.translateService.instant('titles.unable_to_perform_action'),
                detail: this.translateService.instant('messages.user_without_samples_tec_selected'),
                life: (this.localStorageService.getSessionSettings()?.timeoutNotification ?? 5) * 1000
            });
            return;
        } else if (tech == "iris" && !this.iris) {
            this.messageService.add({
                severity: 'warn',
                summary: this.translateService.instant('titles.unable_to_perform_action'),
                detail: this.translateService.instant('messages.user_without_samples_tec_selected'),
                life: (this.localStorageService.getSessionSettings()?.timeoutNotification ?? 5) * 1000
            });
            return;
        } else if (tech == "facial" && !this.facial) {
            this.messageService.add({
                severity: 'warn',
                summary: this.translateService.instant('titles.unable_to_perform_action'),
                detail: this.translateService.instant('messages.user_without_samples_tec_selected'),
                life: (this.localStorageService.getSessionSettings()?.timeoutNotification ?? 5) * 1000
            });
            return;
        }

        this.tech = tech;
        this.waitSelectTech = false;
        this.searchStarted = true;

        this.loadWidgetSearch(this.tech);

        window.addEventListener("message", this.receiveWidgetSearchMessage);
    }

    receiveWidgetSearchMessage(event: MessageEvent): void {
        this.loggerService.debug("Received Message:");
        this.loggerService.debug(event);
        if (event.origin === this.widgetUrl) {
            let result = new WidgetResult();
            switch (event.data.action) {
                case "load":
                    this.startWidget();
                    break;
                case "verify":
                    const verifyResult = event.data.result;
                    result = {
                        action: "verify",
                        result: verifyResult.ErrorCode == 'NO_ERROR' ? "success" : "error",
                        message: verifyResult.ErrorCode == 'NO_ERROR' ? "Verification successful" : verifyResult.ErrorCode,
                        data: {
                            isMatched: verifyResult.isMatched,
                            token: verifyResult.subjectToken,
                            tech: this.tech,
                        }
                    }
                    window.removeEventListener("message", this.receiveWidgetSearchMessage);
                    this.result.emit(result);
                    this.showSearchDialog = false;
                    break;
                case "search":
                    const responseData = event.data.result;
                    result = {
                        action: "search",
                        result: responseData.ErrorCode == 'NO_ERROR' ? "success" : "error",
                        message: responseData.ErrorCode == 'NO_ERROR' ? "Verification successful" : responseData.ErrorCode,
                        data: {
                            nId: responseData.nId,
                            token: responseData.subjectToken,
                            tech: this.tech,
                        }
                    }
                    window.removeEventListener("message", this.receiveWidgetSearchMessage);
                    this.result.emit(result);
                    this.showSearchDialog = false;
                    break;
                case "server_identify":
                    if (event.data.error) {
                        result = this.processError(event, result);
                        break;
                    }

                    const response = event.data.response;
                    const bioResponse = response.biomResponse;
                    let subjects: { nId: string, subjectId: string }[] = [];
                    let images: string[] = [];
                    let matches: { subjectId: string, score: number }[] = [];
                    const identifyResponse = response.identifyResponse;

                    if (bioResponse && bioResponse.samples && bioResponse.samples.length > 0) {
                        const samples = bioResponse.samples;
                        images = samples.map((sample: { contents: string }) => sample.contents)
                    }

                    if (identifyResponse && identifyResponse.samplesResults && identifyResponse.samplesResults.length > 0) {
                        const samplesResults = identifyResponse.samplesResults;
                        const sampleResult = samplesResults[0];
                        if (sampleResult.matchInfo && sampleResult.matchInfo.state && sampleResult.matchInfo.state.allMatches
                            && sampleResult.matchInfo.state.allMatches.length > 0) {
                            const allMatches = sampleResult.matchInfo.state.allMatches;
                            matches = allMatches.map((match: { subjectId: string, score: number }) => match)
                        }
                    }

                    if (response.subjects && response.subjects.length > 0) {
                        subjects = response.subjects.map((subject: {
                            success: {
                                nId: string,
                                subjectId: string
                            }
                        }) => subject.success);
                    }

                    let responseSubjects = [];

                    if (subjects && subjects.length > 0 && images && images.length > 0 && matches && matches.length > 0
                        && subjects.length === images.length && subjects.length === matches.length) {
                        responseSubjects = subjects.map((subject, index) => ({
                            subject,
                            image: images[index],
                            score: matches[index].score,
                        }))

                        result = {
                            action: event.data.action,
                            result: "success",
                            message: "Comparation successful",
                            data: {
                                responseSubjects
                            }
                        }
                    } else {
                        result = {
                            action: event.data.action,
                            result: "error",
                            message: response.ErrorCode,
                            data: {}
                        }
                    }
                    this.showSearchDialog = false;
                    this.result.emit(result);
                    break;
                case "server_biodata":
                    if (event.data.error) {
                        result = this.processError(event, result);
                        break;
                    }
                    const biodata = event.data.identity;
                    if (biodata.length > 0) {
                        biodata.forEach((bioTech: any) => {
                            if (bioTech.format != "NOT_AVAILABLE") {
                                if (bioTech.type == getTechId('fingerprint')) this.print = true;
                                if (bioTech.type == getTechId('iris')) this.iris = true;
                                if (bioTech.type == getTechId('facial')) this.facial = true;
                            }
                        });
                    }
                    if (!this.print && !this.iris && !this.facial) {
                        this.showWidget = false;
                        this.messageService.add({
                            severity: 'warn',
                            summary: this.translateService.instant('titles.unable_to_perform_action'),
                            detail: this.translateService.instant('messages.user_without_samples'),
                            life: (this.localStorageService.getSessionSettings()?.timeoutNotification ?? 5) * 1000
                        });
                    } else {
                        this.fingerprintActive = (this.managerSettings?.payedTechnology?.dactilar && this.konektorProperties?.enabledTech?.dactilar && this.managerSettings?.allowVerify?.dactilar && this.print) || false;
                        this.facialActive = (this.managerSettings?.payedTechnology?.facial && this.konektorProperties?.enabledTech?.facial && this.managerSettings?.allowVerify?.facial && this.facial) || false;
                        this.irisActive = (this.managerSettings?.payedTechnology?.iris && this.konektorProperties?.enabledTech?.iris && this.managerSettings?.allowVerify?.iris && this.iris) || false;
                        const techs = (this.print && this.fingerprintActive ? 1 : 0) + (this.iris && this.irisActive ? 1 : 0) + (this.facial && this.facialActive ? 1 : 0);
                        if (this.technology != "") {
                            switch (this.technology) {
                                case "fingerprint":
                                    this.tech = this.print ? "fingerprint" : "";
                                    break;
                                case "iris":
                                    this.tech = this.iris ? "iris" : "";
                                    break;
                                case "facial":
                                    this.tech = this.facial ? "facial" : "";
                                    break;
                            }
                            if (this.tech != "") {
                                this.searchReady = true;
                                this.search(this.tech);
                            } else {
                                this.messageService.add({
                                    severity: 'warn',
                                    summary: this.translateService.instant('titles.unable_to_perform_action'),
                                    detail: this.translateService.instant('messages.user_without_samples_tec_selected'),
                                    life: (this.localStorageService.getSessionSettings()?.timeoutNotification ?? 5) * 1000
                                });
                                this.closeSearchDialog();
                            }
                        } else if (techs == 0) {
                            result = {
                                action: "error",
                                result: "error",
                                message: "No tech Available",
                                data: {}
                            }
                            window.removeEventListener("message", this.receiveWidgetSearchMessage);
                            this.ready = false;
                            this.showSearchDialog = false;
                            this.messageService.add({
                                severity: 'error',
                                summary: this.translateService.instant('titles.access_denied'),
                                detail: this.translateService.instant('messages.no_techs_available'),
                                life: (this.localStorageService.getSessionSettings()?.timeoutNotification ?? 5) * 1000
                            });
                            this.result.emit(result);
                        } else if (techs == 1) {
                            let technology = "";
                            if (this.fingerprintActive) technology = "fingerprint";
                            else if (this.irisActive) technology = "iris";
                            else if (this.facialActive) technology = "facial";
                            this.tech = technology;
                            this.searchReady = true;
                            this.search(technology);
                        } else {
                            this.searchReady = true;
                            this.startWidget();
                        }
                    }
                    break;
                case "process":
                    result = {
                        action: "process",
                        result: event.data.result,
                        message: "Widget process",
                        data: event.data
                    }
                    this.result.emit(result);
                    break;
                case "error":
                    result = {
                        action: "error",
                        result: event.data.result,
                        message: "Error received from Widget",
                        data: event.data
                    }
                    // window.removeEventListener("message", this.receiveWidgetSearchMessage);
                    this.showSearchDialog = false;
                    this.result.emit(result);
                    break;
            }
        }
    }

    private processError(event: MessageEvent<any>, result: WidgetResult) {
        this.showWidget = false;
        switch (event.data.error) {
            case "ERROR_IDENTITY_DOES_NOT_EXIST":
                this.messageService.add({
                    severity: 'warn',
                    summary: this.translateService.instant('titles.unable_to_perform_action'),
                    detail: this.translateService.instant('messages.user_without_samples'),
                    life: (this.localStorageService.getSessionSettings()?.timeoutNotification ?? 5) * 1000
                });
                result = {
                    action: "error",
                    result: "error",
                    message: "User without biometric data",
                    data: {}
                }
                break;
            case "QUALITY_ERROR ":
                this.messageService.add({
                    severity: 'warn',
                    summary: this.translateService.instant('titles.unable_to_perform_action'),
                    detail: this.translateService.instant('messages.quality_error'),
                    life: (this.localStorageService.getSessionSettings()?.timeoutNotification ?? 5) * 1000
                });
                result = {
                    action: "error",
                    result: "error",
                    message: this.translateService.instant('messages.quality_error'),
                    data: {}
                }
                break;
            default:
                this.messageService.add({
                    severity: 'error',
                    summary: this.translateService.instant('titles.error_operation'),
                    detail: this.translateService.instant('messages.widget_error'),
                    life: (this.localStorageService.getSessionSettings()?.timeoutNotification ?? 5) * 1000
                });
                result = {
                    action: "error",
                    result: event.data.result,
                    message: "Error received from Widget",
                    data: event.data
                }
        }
        window.removeEventListener("message", this.receiveWidgetSearchMessage);
        this.ready = false;
        this.showSearchDialog = false;
        this.result.emit(result);
        return result;
    }

    isTenantValid(): boolean {
        const result: ValidTenantResult = this.checkPermissionsService.isTenantValid(this.konektorProperties);
        this.tenantMessage = result.message;
        return result.valid;
    }
}