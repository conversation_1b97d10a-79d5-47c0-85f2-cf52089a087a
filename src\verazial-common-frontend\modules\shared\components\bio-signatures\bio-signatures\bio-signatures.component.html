<app-loading-spinner [isLoading]="isLoading"></app-loading-spinner>
<!-- Authorization Signature Button -->
<div *ngIf="showSignatureButton" class="flex flex-column align-items-center gap-2">
    <label class="signature-title">{{ signatureTitle | translate }} </label>
    <div *ngIf="userIsVerified else mustVerifyUser" class="signature-container flex flex-column gap-2">
        <!-- NumId Input -->
        <label class="label-form">{{ signatureInputLabel | translate }}</label>
        <div *ngIf="userName != ''">
            <div class="namesLabel mb-2"> {{ userName }} </div>
        </div>
        <input
            type="text" pInputText
            placeholder="{{ 'content.enter_numId_to_verify' | translate }}"
            pTooltip="{{ 'content.enter_numId_to_verify' | translate }}" tooltipPosition="top"
            [(ngModel)]="subjectNumId" [ngModelOptions]="{standalone: true}"  [disabled]="isDisabled"
        />
        @if (this.showExtraFormFields) {
            <!-- Reason Dropdown -->
            <label class="label-form">{{ reasonsDropdownLabel | translate }} <span class="requiredStar">*</span></label>
            <p-dropdown [(ngModel)]="selectReason" [ngModelOptions]="{standalone: true}" appendTo="body" [options]="reasonOptions" optionLabel="value" placeholder="{{ 'content.select' | translate }}"
            />
            <!-- Observation Input -->
            <label class="label-form">{{ observationInputLabel | translate }}</label>
            <input type="text" pInputText [(ngModel)]="observation" [ngModelOptions]="{standalone: true}"/>
        }
        <div class="flex flex-column justify-content-center align-items-center">
            <app-bio-tech-buttons
                [fingerprintEnabled]="managerSettings?.payedTechnology?.dactilar == true && this.konektorProperties?.enabledTech?.dactilar == true && (konektorProperties?.verificationEnabled ? managerSettings?.allowVerify?.dactilar == true : managerSettings?.allowSearch?.dactilar == true)"
                [facialEnabled]="managerSettings?.payedTechnology?.facial == true && this.konektorProperties?.enabledTech?.facial == true && (konektorProperties?.verificationEnabled ? managerSettings?.allowVerify?.facial == true : managerSettings?.allowSearch?.facial == true)"
                [irisEnabled]="managerSettings?.payedTechnology?.iris == true && this.konektorProperties?.enabledTech?.iris == true && (konektorProperties?.verificationEnabled ? managerSettings?.allowVerify?.iris == true : managerSettings?.allowSearch?.iris == true)"
                [konektorProperties]="konektorProperties"
                (tech)="widgetSearch($event)"
            ></app-bio-tech-buttons>
        </div>
    </div>
</div>

<!-- Fixed user/subject for identification -->
<div *ngIf="showFixedSign" class="flex flex-column align-items-center gap-2">
    <label class="signature-title">{{ signatureTitle | translate }} </label>
    <div *ngIf="userIsVerified else mustVerifyUser" class="signature-container flex flex-column gap-2">
        <label class="label-form">{{ 'content.subject' | translate }}</label>
        <input type="text" pInputText value="{{ userName }}" [disabled]="true"/>
        <div class="flex flex-column justify-content-center align-items-center">
            <p-button
                *ngIf="userIsVerified"
                [disabled]="!userIsVerified"
                severity="secondary"
                [outlined]="true"
                label="{{ 'content.sign' | translate }}"
                icon="pi pi-pencil"
                iconPos="right"
                (onClick)="widgetVerify()"
            ></p-button>
        </div>
    </div>
</div>

<!-- Authorization Signature Information -->
<div *ngIf="showSignatureInfo" class="flex flex-column align-items-center justify-content-center gap-2">
    <div class="flex flex-column align-items-start gap-2">
        <label class="signature-title">{{ signatureTitle | translate }} </label>
        <div class="signature-container flex flex-column gap-2">
            <div class="w-20rem flex flex-column gap-4">
                <div class="flex justify-content-center align-items-center gap-3" style="padding-bottom: 0rem;">
                    @if (subjectImage != '') {
                        <img class="imgUser" src={{subjectImage}} />
                    }
                    @else {
                        <p-skeleton width="70px" height="70px" styleClass="imgUser" />
                    }
                    <div class="flex flex-column gap-1">
                        @if (subjectData) {
                            <div class="namesLabel mb-2"> {{ subjectData?.names! + ' ' + subjectData?.lastNames! }} </div>
                        }
                        @else {
                            <p-skeleton width="100%" height="1.5rem" styleClass="namesLabel" />
                        }
                        <div class="flex align-items-center gap-2">
                            <span class="dataKey">{{ 'table.numId' | translate }}: </span>
                            @if (subjectData) {
                                <span class="dataValue">{{ subjectData?.numId! }}</span>
                            }
                            @else {
                                <p-skeleton width="3rem" height="1.5rem" styleClass="dataValue" />
                            }
                        </div>
                        <div class="flex align-items-center gap-2">
                            <span class="dataKey">{{ 'content.technology' | translate }}: </span>
                            @if (isLoading || !signatureData) {
                                <p-skeleton width="20px" height="20px" />
                            }
                            @else {
                                <img style="width: 20px;" pTooltip="{{ ('content.authUserSignatureTech' | translate) + ': ' + ('content.' + signatureData.tech | translate) }}" [src]="getTechIcon(signatureData.tech)"/>
                            }
                        </div>
                        @if (showExtraFormFields) {
                            <div class="flex flex-column justify-content-start gap-1">
                                <span class="dataKey">{{ 'content.cancelReason' | translate }}: </span>
                                @if (isLoading || !signatureData) {
                                    <p-skeleton width="3rem" height="1.5rem" styleClass="dataValue ml-2" />
                                }
                                @else {
                                    <span class="dataValue ml-2">{{ signatureData.reason }}</span>
                                }
                            </div>
                            <div *ngIf="signatureData.observation != null && signatureData.observation != undefined && signatureData.observation != ''" class="flex flex-column align-items-start gap-1">
                                <span class="dataKey">{{ 'content.cancelObservation' | translate }}: </span>
                                @if (isLoading || !signatureData) {
                                    <p-skeleton width="3rem" height="1.5rem" styleClass="dataValue ml-2" />
                                }
                                @else {
                                    <span class="dataValue ml-2">{{ signatureData.observation }}</span>
                                }
                            </div>
                        }
                    </div>
                </div>
                <div style="padding-bottom: 0rem;">
                    <div class="signatureDateLabel">{{ ('content.signedOn' | translate) + ' ' + (signatureData.date  | date: 'short':'':language) }}</div>
                </div>
            </div>
        </div>
    </div>
</div>

<ng-template #mustVerifyUser>
    <div style="width: 200px;" class="signature-container flex flex-column justify-content-center align-items-center">
        {{ 'messages.verification_required_data' | translate }}
    </div>
</ng-template>

<!-- Widget Verify -->
<app-widget-search
    [numId]="subjectNumId"
    [widgetUrl]="widgetUrl"
    [managerSettings]="managerSettings"
    [ready]="searchReady"
    [technology]="tech"
    [segmentedSearchAttributes]="segmentedSearchAttributes"
    (result)="onWidgetSearchResult($event)"
></app-widget-search>

<app-widget-match
        [numId]="subjectNumId"
        [subject]="userSubject"
        [widgetUrl]="widgetUrl"
        [verified]="userIsVerified"
        [managerSettings]="managerSettings"
        [konektorProperties]="konektorProperties"
        [ready]="verifyReady"
        [getToken]="true"
        [isLoggedUserVerification]="true"
        (result)="onWidgetMatchResult($event)"
></app-widget-match>

<p-dialog [(visible)]="showEnterNumId" [modal]="true" [style]="{ background: '#FFFFFF', 'border-radius': '6px', width: '25rem' }" [draggable]="false" [resizable]="false">
    <ng-template pTemplate="headless">
        <div class="dialogHeader mt-3 ml-3">{{ 'content.verification_1to1_enabled' | translate }}</div>
        <div class="flex flex-column align-items-center justify-content-center gap-3 my-3" [formGroup]="formNumId">
            <p-floatLabel style="width: 90%;" class="mt-6 mb-4">
                <input id="numId" type="text" pInputText formControlName="numId" class="flex-auto" autocomplete="off" style="width: 100%;"/>
                <label for="numId">{{ 'content.id_number' | translate}}</label>
            </p-floatLabel>
            <div class="flex justify-content-center gap-2">
                <button pButton label="{{ 'cancel' | translate }}" class="p-button-text" severity="secondary" (click)="closeNumIdDialog()"></button>
                <p-button label="{{ 'verify' | translate}}" severity="secondary" (onClick)="onNumIdDialogSubmit()" />
            </div>
        </div>
    </ng-template>
</p-dialog>