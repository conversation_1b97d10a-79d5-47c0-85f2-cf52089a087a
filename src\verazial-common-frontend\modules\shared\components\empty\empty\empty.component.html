<div class="container">
    <div class="content-no-flow" [style]="{'height': contentHeight, 'width': contentWidth}">
        <img src="verazial-common-frontend/assets/images/all/EmptyImage.svg" alt="empty" width="100" height="100">
        <label class="empty-text"> {{ titleLabel | translate }} </label>
        <label *ngIf="secondaryLabel!==''" class="empty-text"> {{ secondaryLabel | translate }} </label>
        <p-button *ngIf="showButton"
                  [style]="{'width':'180px','height':'36px', 'color': '#FFFFFF' , 'border': 'none', 'background': '#0AB4BA', 'font-family': 'Open Sans', 'font-size': '14px'}"
                  label="{{ buttonLabel | translate}}"
                  icon="pi pi-plus"
                  iconPos="right"
                  [rounded]="true"
                  [disabled]="!readAndWritePermissions"
                  (onClick)="onClickNew()"
        ></p-button>
    </div>
</div>