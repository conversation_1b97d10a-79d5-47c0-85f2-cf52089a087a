/**
 * Parse a custom date string into an ISO date string
 * @param dateStr Date String Recieved from Server
 * @returns an ISO date string
 */
export function parseCustomDate(dateStr: string): string {
    // Split the date and time parts
    const [datePart, timePart, period] = dateStr.split(' ');
    // Split the date into day, month, and year
    const [day, month, year] = datePart.split("/");
    // Split the time into hours, minutes, and seconds
    let [hours, minutes, seconds] = timePart.split(":");
    // Convert the 12-hour format to 24-hour format
    if (period === "PM" && hours !== "12") {
        console
        hours = String(Number(hours) + 12);
    } else if (period === "AM" && hours === "12") {
        hours = "00";
    }
    // Construct a new date string in ISO format and return the ISO string
    return `${year}-${month}-${day}T${hours}:${minutes}:${seconds}.000Z`;
}

/**
 * Adjust a date string by the user's local timezone offset
 * @param isoString ISO Date String
 * @returns ISO Date String adjusted by the user's local timezone offset
 */
export function adjustDateByTimeZoneOffset(isoString: string, timeOffset: number): string {
    // Parse the ISO string to a Date object
    let date = new Date(isoString);
    // Adjust the date by the offset (in milliseconds)
    date.setHours(date.getHours() + getGMTOffsetHours(timeOffset));
    // Return the adjusted date as an ISO string (without the timezone offset)
    return date.toISOString();
}

/**
 * Get the user's local timezone offset in hours
 * @returns GMT Timezone Offset in Hours
 */
export function getGMTOffsetHours(timeOffset: number): number {
    // Get the timezone offset in minutes
    const offsetMinutes = new Date().getTimezoneOffset();
    // Convert the offset to hours and minutes
    const offsetHours = Math.floor(Math.abs(offsetMinutes) / 60);
    // Determine if the offset is positive or negative
    const sign = offsetMinutes <= 0;
    // Return the offset hours
    timeOffset = offsetHours * (sign ? 1 : -1);
    return timeOffset;
}

/**
 * Capitalize the first letter of a string
 * @param str String to capitalize
 * @returns Capitalized String
 */
export function capitalizeFirstLetter(str: string): string {
    if (!str) return str; // Return if the string is empty or undefined
    return str.charAt(0).toUpperCase() + str.slice(1);
}

/**
 * Convert a string to camel case
 * @param str String to convert to camel case
 * @returns Camel case string
 */
export function toCamelCase(str: string) {
    return str
        .replace(/(?:^\w|[A-Z]|\b\w|\s+)/g, (match, index) =>
            index === 0 ? match.toLowerCase() : match.toUpperCase()
        )
        .replace(/\s+/g, '');
}

/**
 * Get the technology ID from the technology name
 * @param tech Technology
 * @returns Technology ID
 */
export function getTechId(tech: string): string {
    switch (tech) {
        case "fingerprint":
            return "FINGER";
        case "iris":
            return "IRIS";
        case "facial":
            return "FACE";
        default:
            throw new Error(`Unknown technology: ${tech}`);
    }
}

/**
 * Is the email valid?
 * @param email Email Address
 * @returns True if the email is valid, false otherwise
 */
export function isValidEmail(email: string): boolean {
    const regex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return regex.test(email);
}

/**
 * Get the authentication error from the error code
 * @param code Error Code
 * @returns Authentication Error
 */
export function getAuthError(code: string): string {
    let result = 'ERROR';
    switch (code) {
      case '':
      case '561':
        console.log("561")
        result = 'FAILED';
        break;
      case '2002':
        console.log("2002")
        // SessionsExceeded
        result = 'SESSIONS_EXCEEDED';
        break;
      case '2003':
        console.log("2003")
        // UserBlocked
        result = 'USER_BLOCKED';
        break;
      default:
        console.log("default")
        // Other errors
        result = 'ERROR';
        break;
    }
    return result;
}

/**
 * Get the mime type from base64 image
 * @param base64 base64 image
 * @returns MymeType
 */
export function getMimeTypeFromBase64(base64: string): string | null {
    if (base64.startsWith('iVBORw0KGgo')) return 'image/png';
    if (base64.startsWith('/9j/')) return 'image/jpeg';
    if (base64.startsWith('R0lGOD')) return 'image/gif';
    if (base64.startsWith('UklGR')) return 'image/webp';
    if (base64.startsWith('PHN2Zy')) return 'image/svg+xml';
    return null;
}

/**
 * Get the file extension from the mime type
 * @param mimeType Mime Type
 * @returns File Extension
 */
export function getFileExtensionFromMimeType(mimeType: string): string | null {
    const mimeToExtension: { [key: string]: string } = {
        // Images
        'image/jpeg': 'jpg',
        'image/jpg': 'jpg',
        'image/png': 'png',
        'image/gif': 'gif',
        'image/bmp': 'bmp',
        'image/webp': 'webp',
        'image/svg+xml': 'svg',
        'image/tiff': 'tiff',
        'image/x-icon': 'ico',

        // Documents
        'application/pdf': 'pdf',
        'application/msword': 'doc',
        'application/vnd.openxmlformats-officedocument.wordprocessingml.document': 'docx',
        'application/vnd.ms-excel': 'xls',
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': 'xlsx',
        'application/vnd.ms-powerpoint': 'ppt',
        'application/vnd.openxmlformats-officedocument.presentationml.presentation': 'pptx',
        'text/plain': 'txt',
        'text/csv': 'csv',
        'application/rtf': 'rtf',

        // Archives
        'application/zip': 'zip',
        'application/x-rar-compressed': 'rar',
        'application/x-7z-compressed': '7z',
        'application/x-tar': 'tar',
        'application/gzip': 'gz',

        // Audio
        'audio/mpeg': 'mp3',
        'audio/wav': 'wav',
        'audio/ogg': 'ogg',
        'audio/mp4': 'm4a',
        'audio/aac': 'aac',

        // Video
        'video/mp4': 'mp4',
        'video/mpeg': 'mpeg',
        'video/quicktime': 'mov',
        'video/x-msvideo': 'avi',
        'video/x-ms-wmv': 'wmv',
        'video/webm': 'webm',

        // Other
        'application/json': 'json',
        'application/xml': 'xml',
        'text/xml': 'xml',
        'text/html': 'html',
        'text/css': 'css',
        'application/javascript': 'js',
        'text/javascript': 'js'
    };

    return mimeToExtension[mimeType.toLowerCase()] || null;
}