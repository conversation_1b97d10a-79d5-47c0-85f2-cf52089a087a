import * as jspb from 'google-protobuf'

import * as google_protobuf_timestamp_pb from 'google-protobuf/google/protobuf/timestamp_pb'; // proto import: "google/protobuf/timestamp.proto"
import * as google_protobuf_empty_pb from 'google-protobuf/google/protobuf/empty_pb'; // proto import: "google/protobuf/empty.proto"
import * as failure_pb from '../failure_pb'; // proto import: "failure.proto"
import * as util_pb from '../util_pb'; // proto import: "util.proto"


export class CaseGrpcModel extends jspb.Message {
  getId(): string;
  setId(value: string): CaseGrpcModel;
  hasId(): boolean;
  clearId(): CaseGrpcModel;

  getName(): string;
  setName(value: string): CaseGrpcModel;
  hasName(): boolean;
  clearName(): CaseGrpcModel;

  getNumber(): string;
  setNumber(value: string): CaseGrpcModel;
  hasNumber(): boolean;
  clearNumber(): CaseGrpcModel;

  getDescription(): string;
  setDescription(value: string): CaseGrpcModel;
  hasDescription(): boolean;
  clearDescription(): CaseGrpcModel;

  getStatus(): string;
  setStatus(value: string): CaseGrpcModel;
  hasStatus(): boolean;
  clearStatus(): CaseGrpcModel;

  getCreatedby(): string;
  setCreatedby(value: string): CaseGrpcModel;
  hasCreatedby(): boolean;
  clearCreatedby(): CaseGrpcModel;

  getUpdatedby(): string;
  setUpdatedby(value: string): CaseGrpcModel;
  hasUpdatedby(): boolean;
  clearUpdatedby(): CaseGrpcModel;

  getCrimedate(): google_protobuf_timestamp_pb.Timestamp | undefined;
  setCrimedate(value?: google_protobuf_timestamp_pb.Timestamp): CaseGrpcModel;
  hasCrimedate(): boolean;
  clearCrimedate(): CaseGrpcModel;

  getOpendate(): google_protobuf_timestamp_pb.Timestamp | undefined;
  setOpendate(value?: google_protobuf_timestamp_pb.Timestamp): CaseGrpcModel;
  hasOpendate(): boolean;
  clearOpendate(): CaseGrpcModel;

  getClosedate(): google_protobuf_timestamp_pb.Timestamp | undefined;
  setClosedate(value?: google_protobuf_timestamp_pb.Timestamp): CaseGrpcModel;
  hasClosedate(): boolean;
  clearClosedate(): CaseGrpcModel;

  getCasedetail(): ArrayOfCaseDetailGrpcModel | undefined;
  setCasedetail(value?: ArrayOfCaseDetailGrpcModel): CaseGrpcModel;
  hasCasedetail(): boolean;
  clearCasedetail(): CaseGrpcModel;

  getRelatedcases(): ArrayOfRelatedCaseGrpcModel | undefined;
  setRelatedcases(value?: ArrayOfRelatedCaseGrpcModel): CaseGrpcModel;
  hasRelatedcases(): boolean;
  clearRelatedcases(): CaseGrpcModel;

  getRelatedsubjects(): ArrayOfSubjectRelatedCaseGrpcModel | undefined;
  setRelatedsubjects(value?: ArrayOfSubjectRelatedCaseGrpcModel): CaseGrpcModel;
  hasRelatedsubjects(): boolean;
  clearRelatedsubjects(): CaseGrpcModel;

  getLocations(): ArrayOfCaseLocationGrpcModel | undefined;
  setLocations(value?: ArrayOfCaseLocationGrpcModel): CaseGrpcModel;
  hasLocations(): boolean;
  clearLocations(): CaseGrpcModel;

  getCreatedat(): google_protobuf_timestamp_pb.Timestamp | undefined;
  setCreatedat(value?: google_protobuf_timestamp_pb.Timestamp): CaseGrpcModel;
  hasCreatedat(): boolean;
  clearCreatedat(): CaseGrpcModel;

  getUpdatedat(): google_protobuf_timestamp_pb.Timestamp | undefined;
  setUpdatedat(value?: google_protobuf_timestamp_pb.Timestamp): CaseGrpcModel;
  hasUpdatedat(): boolean;
  clearUpdatedat(): CaseGrpcModel;

  serializeBinary(): Uint8Array;
  toObject(includeInstance?: boolean): CaseGrpcModel.AsObject;
  static toObject(includeInstance: boolean, msg: CaseGrpcModel): CaseGrpcModel.AsObject;
  static serializeBinaryToWriter(message: CaseGrpcModel, writer: jspb.BinaryWriter): void;
  static deserializeBinary(bytes: Uint8Array): CaseGrpcModel;
  static deserializeBinaryFromReader(message: CaseGrpcModel, reader: jspb.BinaryReader): CaseGrpcModel;
}

export namespace CaseGrpcModel {
  export type AsObject = {
    id?: string,
    name?: string,
    number?: string,
    description?: string,
    status?: string,
    createdby?: string,
    updatedby?: string,
    crimedate?: google_protobuf_timestamp_pb.Timestamp.AsObject,
    opendate?: google_protobuf_timestamp_pb.Timestamp.AsObject,
    closedate?: google_protobuf_timestamp_pb.Timestamp.AsObject,
    casedetail?: ArrayOfCaseDetailGrpcModel.AsObject,
    relatedcases?: ArrayOfRelatedCaseGrpcModel.AsObject,
    relatedsubjects?: ArrayOfSubjectRelatedCaseGrpcModel.AsObject,
    locations?: ArrayOfCaseLocationGrpcModel.AsObject,
    createdat?: google_protobuf_timestamp_pb.Timestamp.AsObject,
    updatedat?: google_protobuf_timestamp_pb.Timestamp.AsObject,
  }

  export enum IdCase { 
    _ID_NOT_SET = 0,
    ID = 1,
  }

  export enum NameCase { 
    _NAME_NOT_SET = 0,
    NAME = 2,
  }

  export enum NumberCase { 
    _NUMBER_NOT_SET = 0,
    NUMBER = 3,
  }

  export enum DescriptionCase { 
    _DESCRIPTION_NOT_SET = 0,
    DESCRIPTION = 4,
  }

  export enum StatusCase { 
    _STATUS_NOT_SET = 0,
    STATUS = 5,
  }

  export enum CreatedbyCase { 
    _CREATEDBY_NOT_SET = 0,
    CREATEDBY = 6,
  }

  export enum UpdatedbyCase { 
    _UPDATEDBY_NOT_SET = 0,
    UPDATEDBY = 7,
  }

  export enum CrimedateCase { 
    _CRIMEDATE_NOT_SET = 0,
    CRIMEDATE = 8,
  }

  export enum OpendateCase { 
    _OPENDATE_NOT_SET = 0,
    OPENDATE = 9,
  }

  export enum ClosedateCase { 
    _CLOSEDATE_NOT_SET = 0,
    CLOSEDATE = 10,
  }

  export enum CasedetailCase { 
    _CASEDETAIL_NOT_SET = 0,
    CASEDETAIL = 11,
  }

  export enum RelatedcasesCase { 
    _RELATEDCASES_NOT_SET = 0,
    RELATEDCASES = 12,
  }

  export enum RelatedsubjectsCase { 
    _RELATEDSUBJECTS_NOT_SET = 0,
    RELATEDSUBJECTS = 13,
  }

  export enum LocationsCase { 
    _LOCATIONS_NOT_SET = 0,
    LOCATIONS = 14,
  }

  export enum CreatedatCase { 
    _CREATEDAT_NOT_SET = 0,
    CREATEDAT = 15,
  }

  export enum UpdatedatCase { 
    _UPDATEDAT_NOT_SET = 0,
    UPDATEDAT = 16,
  }
}

export class ArrayOfCase extends jspb.Message {
  getElementList(): Array<CaseGrpcModel>;
  setElementList(value: Array<CaseGrpcModel>): ArrayOfCase;
  clearElementList(): ArrayOfCase;
  addElement(value?: CaseGrpcModel, index?: number): CaseGrpcModel;

  serializeBinary(): Uint8Array;
  toObject(includeInstance?: boolean): ArrayOfCase.AsObject;
  static toObject(includeInstance: boolean, msg: ArrayOfCase): ArrayOfCase.AsObject;
  static serializeBinaryToWriter(message: ArrayOfCase, writer: jspb.BinaryWriter): void;
  static deserializeBinary(bytes: Uint8Array): ArrayOfCase;
  static deserializeBinaryFromReader(message: ArrayOfCase, reader: jspb.BinaryReader): ArrayOfCase;
}

export namespace ArrayOfCase {
  export type AsObject = {
    elementList: Array<CaseGrpcModel.AsObject>,
  }
}

export class CaseGeneralInfoGrpcModel extends jspb.Message {
  getId(): string;
  setId(value: string): CaseGeneralInfoGrpcModel;
  hasId(): boolean;
  clearId(): CaseGeneralInfoGrpcModel;

  getName(): string;
  setName(value: string): CaseGeneralInfoGrpcModel;
  hasName(): boolean;
  clearName(): CaseGeneralInfoGrpcModel;

  getNumber(): string;
  setNumber(value: string): CaseGeneralInfoGrpcModel;
  hasNumber(): boolean;
  clearNumber(): CaseGeneralInfoGrpcModel;

  getDescription(): string;
  setDescription(value: string): CaseGeneralInfoGrpcModel;
  hasDescription(): boolean;
  clearDescription(): CaseGeneralInfoGrpcModel;

  getStatus(): string;
  setStatus(value: string): CaseGeneralInfoGrpcModel;
  hasStatus(): boolean;
  clearStatus(): CaseGeneralInfoGrpcModel;

  getCreatedby(): string;
  setCreatedby(value: string): CaseGeneralInfoGrpcModel;
  hasCreatedby(): boolean;
  clearCreatedby(): CaseGeneralInfoGrpcModel;

  getUpdatedby(): string;
  setUpdatedby(value: string): CaseGeneralInfoGrpcModel;
  hasUpdatedby(): boolean;
  clearUpdatedby(): CaseGeneralInfoGrpcModel;

  getCrimedate(): google_protobuf_timestamp_pb.Timestamp | undefined;
  setCrimedate(value?: google_protobuf_timestamp_pb.Timestamp): CaseGeneralInfoGrpcModel;
  hasCrimedate(): boolean;
  clearCrimedate(): CaseGeneralInfoGrpcModel;

  getOpendate(): google_protobuf_timestamp_pb.Timestamp | undefined;
  setOpendate(value?: google_protobuf_timestamp_pb.Timestamp): CaseGeneralInfoGrpcModel;
  hasOpendate(): boolean;
  clearOpendate(): CaseGeneralInfoGrpcModel;

  getClosedate(): google_protobuf_timestamp_pb.Timestamp | undefined;
  setClosedate(value?: google_protobuf_timestamp_pb.Timestamp): CaseGeneralInfoGrpcModel;
  hasClosedate(): boolean;
  clearClosedate(): CaseGeneralInfoGrpcModel;

  getCreatedat(): google_protobuf_timestamp_pb.Timestamp | undefined;
  setCreatedat(value?: google_protobuf_timestamp_pb.Timestamp): CaseGeneralInfoGrpcModel;
  hasCreatedat(): boolean;
  clearCreatedat(): CaseGeneralInfoGrpcModel;

  getUpdatedat(): google_protobuf_timestamp_pb.Timestamp | undefined;
  setUpdatedat(value?: google_protobuf_timestamp_pb.Timestamp): CaseGeneralInfoGrpcModel;
  hasUpdatedat(): boolean;
  clearUpdatedat(): CaseGeneralInfoGrpcModel;

  serializeBinary(): Uint8Array;
  toObject(includeInstance?: boolean): CaseGeneralInfoGrpcModel.AsObject;
  static toObject(includeInstance: boolean, msg: CaseGeneralInfoGrpcModel): CaseGeneralInfoGrpcModel.AsObject;
  static serializeBinaryToWriter(message: CaseGeneralInfoGrpcModel, writer: jspb.BinaryWriter): void;
  static deserializeBinary(bytes: Uint8Array): CaseGeneralInfoGrpcModel;
  static deserializeBinaryFromReader(message: CaseGeneralInfoGrpcModel, reader: jspb.BinaryReader): CaseGeneralInfoGrpcModel;
}

export namespace CaseGeneralInfoGrpcModel {
  export type AsObject = {
    id?: string,
    name?: string,
    number?: string,
    description?: string,
    status?: string,
    createdby?: string,
    updatedby?: string,
    crimedate?: google_protobuf_timestamp_pb.Timestamp.AsObject,
    opendate?: google_protobuf_timestamp_pb.Timestamp.AsObject,
    closedate?: google_protobuf_timestamp_pb.Timestamp.AsObject,
    createdat?: google_protobuf_timestamp_pb.Timestamp.AsObject,
    updatedat?: google_protobuf_timestamp_pb.Timestamp.AsObject,
  }

  export enum IdCase { 
    _ID_NOT_SET = 0,
    ID = 1,
  }

  export enum NameCase { 
    _NAME_NOT_SET = 0,
    NAME = 2,
  }

  export enum NumberCase { 
    _NUMBER_NOT_SET = 0,
    NUMBER = 3,
  }

  export enum DescriptionCase { 
    _DESCRIPTION_NOT_SET = 0,
    DESCRIPTION = 4,
  }

  export enum StatusCase { 
    _STATUS_NOT_SET = 0,
    STATUS = 5,
  }

  export enum CreatedbyCase { 
    _CREATEDBY_NOT_SET = 0,
    CREATEDBY = 6,
  }

  export enum UpdatedbyCase { 
    _UPDATEDBY_NOT_SET = 0,
    UPDATEDBY = 7,
  }

  export enum CrimedateCase { 
    _CRIMEDATE_NOT_SET = 0,
    CRIMEDATE = 8,
  }

  export enum OpendateCase { 
    _OPENDATE_NOT_SET = 0,
    OPENDATE = 9,
  }

  export enum ClosedateCase { 
    _CLOSEDATE_NOT_SET = 0,
    CLOSEDATE = 10,
  }

  export enum CreatedatCase { 
    _CREATEDAT_NOT_SET = 0,
    CREATEDAT = 11,
  }

  export enum UpdatedatCase { 
    _UPDATEDAT_NOT_SET = 0,
    UPDATEDAT = 12,
  }
}

export class CriminalisticCaseResponse extends jspb.Message {
  getFailure(): failure_pb.FailureGrpc | undefined;
  setFailure(value?: failure_pb.FailureGrpc): CriminalisticCaseResponse;
  hasFailure(): boolean;
  clearFailure(): CriminalisticCaseResponse;

  getCase(): CaseGrpcModel | undefined;
  setCase(value?: CaseGrpcModel): CriminalisticCaseResponse;
  hasCase(): boolean;
  clearCase(): CriminalisticCaseResponse;

  getResponseCase(): CriminalisticCaseResponse.ResponseCase;

  serializeBinary(): Uint8Array;
  toObject(includeInstance?: boolean): CriminalisticCaseResponse.AsObject;
  static toObject(includeInstance: boolean, msg: CriminalisticCaseResponse): CriminalisticCaseResponse.AsObject;
  static serializeBinaryToWriter(message: CriminalisticCaseResponse, writer: jspb.BinaryWriter): void;
  static deserializeBinary(bytes: Uint8Array): CriminalisticCaseResponse;
  static deserializeBinaryFromReader(message: CriminalisticCaseResponse, reader: jspb.BinaryReader): CriminalisticCaseResponse;
}

export namespace CriminalisticCaseResponse {
  export type AsObject = {
    failure?: failure_pb.FailureGrpc.AsObject,
    pb_case?: CaseGrpcModel.AsObject,
  }

  export enum ResponseCase { 
    RESPONSE_NOT_SET = 0,
    FAILURE = 1,
    CASE = 2,
  }
}

export class CriminalisticCaseGeneralInfoResponse extends jspb.Message {
  getFailure(): failure_pb.FailureGrpc | undefined;
  setFailure(value?: failure_pb.FailureGrpc): CriminalisticCaseGeneralInfoResponse;
  hasFailure(): boolean;
  clearFailure(): CriminalisticCaseGeneralInfoResponse;

  getCase(): CaseGeneralInfoGrpcModel | undefined;
  setCase(value?: CaseGeneralInfoGrpcModel): CriminalisticCaseGeneralInfoResponse;
  hasCase(): boolean;
  clearCase(): CriminalisticCaseGeneralInfoResponse;

  getResponseCase(): CriminalisticCaseGeneralInfoResponse.ResponseCase;

  serializeBinary(): Uint8Array;
  toObject(includeInstance?: boolean): CriminalisticCaseGeneralInfoResponse.AsObject;
  static toObject(includeInstance: boolean, msg: CriminalisticCaseGeneralInfoResponse): CriminalisticCaseGeneralInfoResponse.AsObject;
  static serializeBinaryToWriter(message: CriminalisticCaseGeneralInfoResponse, writer: jspb.BinaryWriter): void;
  static deserializeBinary(bytes: Uint8Array): CriminalisticCaseGeneralInfoResponse;
  static deserializeBinaryFromReader(message: CriminalisticCaseGeneralInfoResponse, reader: jspb.BinaryReader): CriminalisticCaseGeneralInfoResponse;
}

export namespace CriminalisticCaseGeneralInfoResponse {
  export type AsObject = {
    failure?: failure_pb.FailureGrpc.AsObject,
    pb_case?: CaseGeneralInfoGrpcModel.AsObject,
  }

  export enum ResponseCase { 
    RESPONSE_NOT_SET = 0,
    FAILURE = 1,
    CASE = 2,
  }
}

export class CaseLocationGrpcModel extends jspb.Message {
  getId(): string;
  setId(value: string): CaseLocationGrpcModel;
  hasId(): boolean;
  clearId(): CaseLocationGrpcModel;

  getCaseid(): string;
  setCaseid(value: string): CaseLocationGrpcModel;
  hasCaseid(): boolean;
  clearCaseid(): CaseLocationGrpcModel;

  getLocation(): string;
  setLocation(value: string): CaseLocationGrpcModel;
  hasLocation(): boolean;
  clearLocation(): CaseLocationGrpcModel;

  getName(): string;
  setName(value: string): CaseLocationGrpcModel;
  hasName(): boolean;
  clearName(): CaseLocationGrpcModel;

  getType(): string;
  setType(value: string): CaseLocationGrpcModel;
  hasType(): boolean;
  clearType(): CaseLocationGrpcModel;

  getComment(): string;
  setComment(value: string): CaseLocationGrpcModel;
  hasComment(): boolean;
  clearComment(): CaseLocationGrpcModel;

  getDescription(): string;
  setDescription(value: string): CaseLocationGrpcModel;
  hasDescription(): boolean;
  clearDescription(): CaseLocationGrpcModel;

  getLatitude(): string;
  setLatitude(value: string): CaseLocationGrpcModel;
  hasLatitude(): boolean;
  clearLatitude(): CaseLocationGrpcModel;

  getLongitude(): string;
  setLongitude(value: string): CaseLocationGrpcModel;
  hasLongitude(): boolean;
  clearLongitude(): CaseLocationGrpcModel;

  getCreatedat(): google_protobuf_timestamp_pb.Timestamp | undefined;
  setCreatedat(value?: google_protobuf_timestamp_pb.Timestamp): CaseLocationGrpcModel;
  hasCreatedat(): boolean;
  clearCreatedat(): CaseLocationGrpcModel;

  getUpdatedat(): google_protobuf_timestamp_pb.Timestamp | undefined;
  setUpdatedat(value?: google_protobuf_timestamp_pb.Timestamp): CaseLocationGrpcModel;
  hasUpdatedat(): boolean;
  clearUpdatedat(): CaseLocationGrpcModel;

  serializeBinary(): Uint8Array;
  toObject(includeInstance?: boolean): CaseLocationGrpcModel.AsObject;
  static toObject(includeInstance: boolean, msg: CaseLocationGrpcModel): CaseLocationGrpcModel.AsObject;
  static serializeBinaryToWriter(message: CaseLocationGrpcModel, writer: jspb.BinaryWriter): void;
  static deserializeBinary(bytes: Uint8Array): CaseLocationGrpcModel;
  static deserializeBinaryFromReader(message: CaseLocationGrpcModel, reader: jspb.BinaryReader): CaseLocationGrpcModel;
}

export namespace CaseLocationGrpcModel {
  export type AsObject = {
    id?: string,
    caseid?: string,
    location?: string,
    name?: string,
    type?: string,
    comment?: string,
    description?: string,
    latitude?: string,
    longitude?: string,
    createdat?: google_protobuf_timestamp_pb.Timestamp.AsObject,
    updatedat?: google_protobuf_timestamp_pb.Timestamp.AsObject,
  }

  export enum IdCase { 
    _ID_NOT_SET = 0,
    ID = 1,
  }

  export enum CaseidCase { 
    _CASEID_NOT_SET = 0,
    CASEID = 2,
  }

  export enum LocationCase { 
    _LOCATION_NOT_SET = 0,
    LOCATION = 3,
  }

  export enum NameCase { 
    _NAME_NOT_SET = 0,
    NAME = 4,
  }

  export enum TypeCase { 
    _TYPE_NOT_SET = 0,
    TYPE = 5,
  }

  export enum CommentCase { 
    _COMMENT_NOT_SET = 0,
    COMMENT = 6,
  }

  export enum DescriptionCase { 
    _DESCRIPTION_NOT_SET = 0,
    DESCRIPTION = 7,
  }

  export enum LatitudeCase { 
    _LATITUDE_NOT_SET = 0,
    LATITUDE = 8,
  }

  export enum LongitudeCase { 
    _LONGITUDE_NOT_SET = 0,
    LONGITUDE = 9,
  }

  export enum CreatedatCase { 
    _CREATEDAT_NOT_SET = 0,
    CREATEDAT = 10,
  }

  export enum UpdatedatCase { 
    _UPDATEDAT_NOT_SET = 0,
    UPDATEDAT = 11,
  }
}

export class ArrayOfCaseLocationGrpcModel extends jspb.Message {
  getElementList(): Array<CaseLocationGrpcModel>;
  setElementList(value: Array<CaseLocationGrpcModel>): ArrayOfCaseLocationGrpcModel;
  clearElementList(): ArrayOfCaseLocationGrpcModel;
  addElement(value?: CaseLocationGrpcModel, index?: number): CaseLocationGrpcModel;

  serializeBinary(): Uint8Array;
  toObject(includeInstance?: boolean): ArrayOfCaseLocationGrpcModel.AsObject;
  static toObject(includeInstance: boolean, msg: ArrayOfCaseLocationGrpcModel): ArrayOfCaseLocationGrpcModel.AsObject;
  static serializeBinaryToWriter(message: ArrayOfCaseLocationGrpcModel, writer: jspb.BinaryWriter): void;
  static deserializeBinary(bytes: Uint8Array): ArrayOfCaseLocationGrpcModel;
  static deserializeBinaryFromReader(message: ArrayOfCaseLocationGrpcModel, reader: jspb.BinaryReader): ArrayOfCaseLocationGrpcModel;
}

export namespace ArrayOfCaseLocationGrpcModel {
  export type AsObject = {
    elementList: Array<CaseLocationGrpcModel.AsObject>,
  }
}

export class ResponseCaseLocation extends jspb.Message {
  getFailure(): failure_pb.FailureGrpc | undefined;
  setFailure(value?: failure_pb.FailureGrpc): ResponseCaseLocation;
  hasFailure(): boolean;
  clearFailure(): ResponseCaseLocation;

  getCaselocation(): CaseLocationGrpcModel | undefined;
  setCaselocation(value?: CaseLocationGrpcModel): ResponseCaseLocation;
  hasCaselocation(): boolean;
  clearCaselocation(): ResponseCaseLocation;

  getResponseCase(): ResponseCaseLocation.ResponseCase;

  serializeBinary(): Uint8Array;
  toObject(includeInstance?: boolean): ResponseCaseLocation.AsObject;
  static toObject(includeInstance: boolean, msg: ResponseCaseLocation): ResponseCaseLocation.AsObject;
  static serializeBinaryToWriter(message: ResponseCaseLocation, writer: jspb.BinaryWriter): void;
  static deserializeBinary(bytes: Uint8Array): ResponseCaseLocation;
  static deserializeBinaryFromReader(message: ResponseCaseLocation, reader: jspb.BinaryReader): ResponseCaseLocation;
}

export namespace ResponseCaseLocation {
  export type AsObject = {
    failure?: failure_pb.FailureGrpc.AsObject,
    caselocation?: CaseLocationGrpcModel.AsObject,
  }

  export enum ResponseCase { 
    RESPONSE_NOT_SET = 0,
    FAILURE = 1,
    CASELOCATION = 2,
  }
}

export class RelatedCaseGrpcModel extends jspb.Message {
  getCaseid(): string;
  setCaseid(value: string): RelatedCaseGrpcModel;
  hasCaseid(): boolean;
  clearCaseid(): RelatedCaseGrpcModel;

  getRelatedcaseid(): string;
  setRelatedcaseid(value: string): RelatedCaseGrpcModel;
  hasRelatedcaseid(): boolean;
  clearRelatedcaseid(): RelatedCaseGrpcModel;

  getRelation(): string;
  setRelation(value: string): RelatedCaseGrpcModel;
  hasRelation(): boolean;
  clearRelation(): RelatedCaseGrpcModel;

  getCreatedat(): google_protobuf_timestamp_pb.Timestamp | undefined;
  setCreatedat(value?: google_protobuf_timestamp_pb.Timestamp): RelatedCaseGrpcModel;
  hasCreatedat(): boolean;
  clearCreatedat(): RelatedCaseGrpcModel;

  getUpdatedat(): google_protobuf_timestamp_pb.Timestamp | undefined;
  setUpdatedat(value?: google_protobuf_timestamp_pb.Timestamp): RelatedCaseGrpcModel;
  hasUpdatedat(): boolean;
  clearUpdatedat(): RelatedCaseGrpcModel;

  serializeBinary(): Uint8Array;
  toObject(includeInstance?: boolean): RelatedCaseGrpcModel.AsObject;
  static toObject(includeInstance: boolean, msg: RelatedCaseGrpcModel): RelatedCaseGrpcModel.AsObject;
  static serializeBinaryToWriter(message: RelatedCaseGrpcModel, writer: jspb.BinaryWriter): void;
  static deserializeBinary(bytes: Uint8Array): RelatedCaseGrpcModel;
  static deserializeBinaryFromReader(message: RelatedCaseGrpcModel, reader: jspb.BinaryReader): RelatedCaseGrpcModel;
}

export namespace RelatedCaseGrpcModel {
  export type AsObject = {
    caseid?: string,
    relatedcaseid?: string,
    relation?: string,
    createdat?: google_protobuf_timestamp_pb.Timestamp.AsObject,
    updatedat?: google_protobuf_timestamp_pb.Timestamp.AsObject,
  }

  export enum CaseidCase { 
    _CASEID_NOT_SET = 0,
    CASEID = 1,
  }

  export enum RelatedcaseidCase { 
    _RELATEDCASEID_NOT_SET = 0,
    RELATEDCASEID = 2,
  }

  export enum RelationCase { 
    _RELATION_NOT_SET = 0,
    RELATION = 3,
  }

  export enum CreatedatCase { 
    _CREATEDAT_NOT_SET = 0,
    CREATEDAT = 4,
  }

  export enum UpdatedatCase { 
    _UPDATEDAT_NOT_SET = 0,
    UPDATEDAT = 5,
  }
}

export class ArrayOfRelatedCaseGrpcModel extends jspb.Message {
  getElementList(): Array<RelatedCaseGrpcModel>;
  setElementList(value: Array<RelatedCaseGrpcModel>): ArrayOfRelatedCaseGrpcModel;
  clearElementList(): ArrayOfRelatedCaseGrpcModel;
  addElement(value?: RelatedCaseGrpcModel, index?: number): RelatedCaseGrpcModel;

  serializeBinary(): Uint8Array;
  toObject(includeInstance?: boolean): ArrayOfRelatedCaseGrpcModel.AsObject;
  static toObject(includeInstance: boolean, msg: ArrayOfRelatedCaseGrpcModel): ArrayOfRelatedCaseGrpcModel.AsObject;
  static serializeBinaryToWriter(message: ArrayOfRelatedCaseGrpcModel, writer: jspb.BinaryWriter): void;
  static deserializeBinary(bytes: Uint8Array): ArrayOfRelatedCaseGrpcModel;
  static deserializeBinaryFromReader(message: ArrayOfRelatedCaseGrpcModel, reader: jspb.BinaryReader): ArrayOfRelatedCaseGrpcModel;
}

export namespace ArrayOfRelatedCaseGrpcModel {
  export type AsObject = {
    elementList: Array<RelatedCaseGrpcModel.AsObject>,
  }
}

export class CaseDetailGrpcModel extends jspb.Message {
  getId(): string;
  setId(value: string): CaseDetailGrpcModel;
  hasId(): boolean;
  clearId(): CaseDetailGrpcModel;

  getCaseid(): string;
  setCaseid(value: string): CaseDetailGrpcModel;
  hasCaseid(): boolean;
  clearCaseid(): CaseDetailGrpcModel;

  getKey(): string;
  setKey(value: string): CaseDetailGrpcModel;
  hasKey(): boolean;
  clearKey(): CaseDetailGrpcModel;

  getValue(): string;
  setValue(value: string): CaseDetailGrpcModel;
  hasValue(): boolean;
  clearValue(): CaseDetailGrpcModel;

  getCreatedby(): string;
  setCreatedby(value: string): CaseDetailGrpcModel;
  hasCreatedby(): boolean;
  clearCreatedby(): CaseDetailGrpcModel;

  getUpdatedby(): string;
  setUpdatedby(value: string): CaseDetailGrpcModel;
  hasUpdatedby(): boolean;
  clearUpdatedby(): CaseDetailGrpcModel;

  getCreatedat(): google_protobuf_timestamp_pb.Timestamp | undefined;
  setCreatedat(value?: google_protobuf_timestamp_pb.Timestamp): CaseDetailGrpcModel;
  hasCreatedat(): boolean;
  clearCreatedat(): CaseDetailGrpcModel;

  getUpdatedat(): google_protobuf_timestamp_pb.Timestamp | undefined;
  setUpdatedat(value?: google_protobuf_timestamp_pb.Timestamp): CaseDetailGrpcModel;
  hasUpdatedat(): boolean;
  clearUpdatedat(): CaseDetailGrpcModel;

  serializeBinary(): Uint8Array;
  toObject(includeInstance?: boolean): CaseDetailGrpcModel.AsObject;
  static toObject(includeInstance: boolean, msg: CaseDetailGrpcModel): CaseDetailGrpcModel.AsObject;
  static serializeBinaryToWriter(message: CaseDetailGrpcModel, writer: jspb.BinaryWriter): void;
  static deserializeBinary(bytes: Uint8Array): CaseDetailGrpcModel;
  static deserializeBinaryFromReader(message: CaseDetailGrpcModel, reader: jspb.BinaryReader): CaseDetailGrpcModel;
}

export namespace CaseDetailGrpcModel {
  export type AsObject = {
    id?: string,
    caseid?: string,
    key?: string,
    value?: string,
    createdby?: string,
    updatedby?: string,
    createdat?: google_protobuf_timestamp_pb.Timestamp.AsObject,
    updatedat?: google_protobuf_timestamp_pb.Timestamp.AsObject,
  }

  export enum IdCase { 
    _ID_NOT_SET = 0,
    ID = 1,
  }

  export enum CaseidCase { 
    _CASEID_NOT_SET = 0,
    CASEID = 2,
  }

  export enum KeyCase { 
    _KEY_NOT_SET = 0,
    KEY = 3,
  }

  export enum ValueCase { 
    _VALUE_NOT_SET = 0,
    VALUE = 4,
  }

  export enum CreatedbyCase { 
    _CREATEDBY_NOT_SET = 0,
    CREATEDBY = 5,
  }

  export enum UpdatedbyCase { 
    _UPDATEDBY_NOT_SET = 0,
    UPDATEDBY = 6,
  }

  export enum CreatedatCase { 
    _CREATEDAT_NOT_SET = 0,
    CREATEDAT = 7,
  }

  export enum UpdatedatCase { 
    _UPDATEDAT_NOT_SET = 0,
    UPDATEDAT = 8,
  }
}

export class ArrayOfCaseDetailGrpcModel extends jspb.Message {
  getElementList(): Array<CaseDetailGrpcModel>;
  setElementList(value: Array<CaseDetailGrpcModel>): ArrayOfCaseDetailGrpcModel;
  clearElementList(): ArrayOfCaseDetailGrpcModel;
  addElement(value?: CaseDetailGrpcModel, index?: number): CaseDetailGrpcModel;

  serializeBinary(): Uint8Array;
  toObject(includeInstance?: boolean): ArrayOfCaseDetailGrpcModel.AsObject;
  static toObject(includeInstance: boolean, msg: ArrayOfCaseDetailGrpcModel): ArrayOfCaseDetailGrpcModel.AsObject;
  static serializeBinaryToWriter(message: ArrayOfCaseDetailGrpcModel, writer: jspb.BinaryWriter): void;
  static deserializeBinary(bytes: Uint8Array): ArrayOfCaseDetailGrpcModel;
  static deserializeBinaryFromReader(message: ArrayOfCaseDetailGrpcModel, reader: jspb.BinaryReader): ArrayOfCaseDetailGrpcModel;
}

export namespace ArrayOfCaseDetailGrpcModel {
  export type AsObject = {
    elementList: Array<CaseDetailGrpcModel.AsObject>,
  }
}

export class ResponseRelatedCase extends jspb.Message {
  getFailure(): failure_pb.FailureGrpc | undefined;
  setFailure(value?: failure_pb.FailureGrpc): ResponseRelatedCase;
  hasFailure(): boolean;
  clearFailure(): ResponseRelatedCase;

  getRelatedcase(): RelatedCaseGrpcModel | undefined;
  setRelatedcase(value?: RelatedCaseGrpcModel): ResponseRelatedCase;
  hasRelatedcase(): boolean;
  clearRelatedcase(): ResponseRelatedCase;

  getResponseCase(): ResponseRelatedCase.ResponseCase;

  serializeBinary(): Uint8Array;
  toObject(includeInstance?: boolean): ResponseRelatedCase.AsObject;
  static toObject(includeInstance: boolean, msg: ResponseRelatedCase): ResponseRelatedCase.AsObject;
  static serializeBinaryToWriter(message: ResponseRelatedCase, writer: jspb.BinaryWriter): void;
  static deserializeBinary(bytes: Uint8Array): ResponseRelatedCase;
  static deserializeBinaryFromReader(message: ResponseRelatedCase, reader: jspb.BinaryReader): ResponseRelatedCase;
}

export namespace ResponseRelatedCase {
  export type AsObject = {
    failure?: failure_pb.FailureGrpc.AsObject,
    relatedcase?: RelatedCaseGrpcModel.AsObject,
  }

  export enum ResponseCase { 
    RESPONSE_NOT_SET = 0,
    FAILURE = 1,
    RELATEDCASE = 2,
  }
}

export class SubjectRelatedCaseGrpcModel extends jspb.Message {
  getCaseid(): string;
  setCaseid(value: string): SubjectRelatedCaseGrpcModel;
  hasCaseid(): boolean;
  clearCaseid(): SubjectRelatedCaseGrpcModel;

  getRelatedsubjectid(): string;
  setRelatedsubjectid(value: string): SubjectRelatedCaseGrpcModel;
  hasRelatedsubjectid(): boolean;
  clearRelatedsubjectid(): SubjectRelatedCaseGrpcModel;

  getRelation(): string;
  setRelation(value: string): SubjectRelatedCaseGrpcModel;
  hasRelation(): boolean;
  clearRelation(): SubjectRelatedCaseGrpcModel;

  getCreatedat(): google_protobuf_timestamp_pb.Timestamp | undefined;
  setCreatedat(value?: google_protobuf_timestamp_pb.Timestamp): SubjectRelatedCaseGrpcModel;
  hasCreatedat(): boolean;
  clearCreatedat(): SubjectRelatedCaseGrpcModel;

  getUpdatedat(): google_protobuf_timestamp_pb.Timestamp | undefined;
  setUpdatedat(value?: google_protobuf_timestamp_pb.Timestamp): SubjectRelatedCaseGrpcModel;
  hasUpdatedat(): boolean;
  clearUpdatedat(): SubjectRelatedCaseGrpcModel;

  serializeBinary(): Uint8Array;
  toObject(includeInstance?: boolean): SubjectRelatedCaseGrpcModel.AsObject;
  static toObject(includeInstance: boolean, msg: SubjectRelatedCaseGrpcModel): SubjectRelatedCaseGrpcModel.AsObject;
  static serializeBinaryToWriter(message: SubjectRelatedCaseGrpcModel, writer: jspb.BinaryWriter): void;
  static deserializeBinary(bytes: Uint8Array): SubjectRelatedCaseGrpcModel;
  static deserializeBinaryFromReader(message: SubjectRelatedCaseGrpcModel, reader: jspb.BinaryReader): SubjectRelatedCaseGrpcModel;
}

export namespace SubjectRelatedCaseGrpcModel {
  export type AsObject = {
    caseid?: string,
    relatedsubjectid?: string,
    relation?: string,
    createdat?: google_protobuf_timestamp_pb.Timestamp.AsObject,
    updatedat?: google_protobuf_timestamp_pb.Timestamp.AsObject,
  }

  export enum CaseidCase { 
    _CASEID_NOT_SET = 0,
    CASEID = 1,
  }

  export enum RelatedsubjectidCase { 
    _RELATEDSUBJECTID_NOT_SET = 0,
    RELATEDSUBJECTID = 2,
  }

  export enum RelationCase { 
    _RELATION_NOT_SET = 0,
    RELATION = 3,
  }

  export enum CreatedatCase { 
    _CREATEDAT_NOT_SET = 0,
    CREATEDAT = 4,
  }

  export enum UpdatedatCase { 
    _UPDATEDAT_NOT_SET = 0,
    UPDATEDAT = 5,
  }
}

export class ArrayOfSubjectRelatedCaseGrpcModel extends jspb.Message {
  getElementList(): Array<SubjectRelatedCaseGrpcModel>;
  setElementList(value: Array<SubjectRelatedCaseGrpcModel>): ArrayOfSubjectRelatedCaseGrpcModel;
  clearElementList(): ArrayOfSubjectRelatedCaseGrpcModel;
  addElement(value?: SubjectRelatedCaseGrpcModel, index?: number): SubjectRelatedCaseGrpcModel;

  serializeBinary(): Uint8Array;
  toObject(includeInstance?: boolean): ArrayOfSubjectRelatedCaseGrpcModel.AsObject;
  static toObject(includeInstance: boolean, msg: ArrayOfSubjectRelatedCaseGrpcModel): ArrayOfSubjectRelatedCaseGrpcModel.AsObject;
  static serializeBinaryToWriter(message: ArrayOfSubjectRelatedCaseGrpcModel, writer: jspb.BinaryWriter): void;
  static deserializeBinary(bytes: Uint8Array): ArrayOfSubjectRelatedCaseGrpcModel;
  static deserializeBinaryFromReader(message: ArrayOfSubjectRelatedCaseGrpcModel, reader: jspb.BinaryReader): ArrayOfSubjectRelatedCaseGrpcModel;
}

export namespace ArrayOfSubjectRelatedCaseGrpcModel {
  export type AsObject = {
    elementList: Array<SubjectRelatedCaseGrpcModel.AsObject>,
  }
}

export class ResponseSubjectRelatedCase extends jspb.Message {
  getFailure(): failure_pb.FailureGrpc | undefined;
  setFailure(value?: failure_pb.FailureGrpc): ResponseSubjectRelatedCase;
  hasFailure(): boolean;
  clearFailure(): ResponseSubjectRelatedCase;

  getSubjectrelatedcase(): SubjectRelatedCaseGrpcModel | undefined;
  setSubjectrelatedcase(value?: SubjectRelatedCaseGrpcModel): ResponseSubjectRelatedCase;
  hasSubjectrelatedcase(): boolean;
  clearSubjectrelatedcase(): ResponseSubjectRelatedCase;

  getResponseCase(): ResponseSubjectRelatedCase.ResponseCase;

  serializeBinary(): Uint8Array;
  toObject(includeInstance?: boolean): ResponseSubjectRelatedCase.AsObject;
  static toObject(includeInstance: boolean, msg: ResponseSubjectRelatedCase): ResponseSubjectRelatedCase.AsObject;
  static serializeBinaryToWriter(message: ResponseSubjectRelatedCase, writer: jspb.BinaryWriter): void;
  static deserializeBinary(bytes: Uint8Array): ResponseSubjectRelatedCase;
  static deserializeBinaryFromReader(message: ResponseSubjectRelatedCase, reader: jspb.BinaryReader): ResponseSubjectRelatedCase;
}

export namespace ResponseSubjectRelatedCase {
  export type AsObject = {
    failure?: failure_pb.FailureGrpc.AsObject,
    subjectrelatedcase?: SubjectRelatedCaseGrpcModel.AsObject,
  }

  export enum ResponseCase { 
    RESPONSE_NOT_SET = 0,
    FAILURE = 1,
    SUBJECTRELATEDCASE = 2,
  }
}

export class CaseEvidenceGrpcModel extends jspb.Message {
  getId(): string;
  setId(value: string): CaseEvidenceGrpcModel;
  hasId(): boolean;
  clearId(): CaseEvidenceGrpcModel;

  getCaseid(): string;
  setCaseid(value: string): CaseEvidenceGrpcModel;
  hasCaseid(): boolean;
  clearCaseid(): CaseEvidenceGrpcModel;

  getRawid(): string;
  setRawid(value: string): CaseEvidenceGrpcModel;
  hasRawid(): boolean;
  clearRawid(): CaseEvidenceGrpcModel;

  getName(): string;
  setName(value: string): CaseEvidenceGrpcModel;
  hasName(): boolean;
  clearName(): CaseEvidenceGrpcModel;

  getType(): string;
  setType(value: string): CaseEvidenceGrpcModel;
  hasType(): boolean;
  clearType(): CaseEvidenceGrpcModel;

  getLocation(): string;
  setLocation(value: string): CaseEvidenceGrpcModel;
  hasLocation(): boolean;
  clearLocation(): CaseEvidenceGrpcModel;

  getStatus(): string;
  setStatus(value: string): CaseEvidenceGrpcModel;
  hasStatus(): boolean;
  clearStatus(): CaseEvidenceGrpcModel;

  getContent(): Uint8Array | string;
  getContent_asU8(): Uint8Array;
  getContent_asB64(): string;
  setContent(value: Uint8Array | string): CaseEvidenceGrpcModel;

  getMimetype(): string;
  setMimetype(value: string): CaseEvidenceGrpcModel;
  hasMimetype(): boolean;
  clearMimetype(): CaseEvidenceGrpcModel;

  getObtainedat(): google_protobuf_timestamp_pb.Timestamp | undefined;
  setObtainedat(value?: google_protobuf_timestamp_pb.Timestamp): CaseEvidenceGrpcModel;
  hasObtainedat(): boolean;
  clearObtainedat(): CaseEvidenceGrpcModel;

  getCreatedat(): google_protobuf_timestamp_pb.Timestamp | undefined;
  setCreatedat(value?: google_protobuf_timestamp_pb.Timestamp): CaseEvidenceGrpcModel;
  hasCreatedat(): boolean;
  clearCreatedat(): CaseEvidenceGrpcModel;

  getUpdatedat(): google_protobuf_timestamp_pb.Timestamp | undefined;
  setUpdatedat(value?: google_protobuf_timestamp_pb.Timestamp): CaseEvidenceGrpcModel;
  hasUpdatedat(): boolean;
  clearUpdatedat(): CaseEvidenceGrpcModel;

  serializeBinary(): Uint8Array;
  toObject(includeInstance?: boolean): CaseEvidenceGrpcModel.AsObject;
  static toObject(includeInstance: boolean, msg: CaseEvidenceGrpcModel): CaseEvidenceGrpcModel.AsObject;
  static serializeBinaryToWriter(message: CaseEvidenceGrpcModel, writer: jspb.BinaryWriter): void;
  static deserializeBinary(bytes: Uint8Array): CaseEvidenceGrpcModel;
  static deserializeBinaryFromReader(message: CaseEvidenceGrpcModel, reader: jspb.BinaryReader): CaseEvidenceGrpcModel;
}

export namespace CaseEvidenceGrpcModel {
  export type AsObject = {
    id?: string,
    caseid?: string,
    rawid?: string,
    name?: string,
    type?: string,
    location?: string,
    status?: string,
    content: Uint8Array | string,
    mimetype?: string,
    obtainedat?: google_protobuf_timestamp_pb.Timestamp.AsObject,
    createdat?: google_protobuf_timestamp_pb.Timestamp.AsObject,
    updatedat?: google_protobuf_timestamp_pb.Timestamp.AsObject,
  }

  export enum IdCase { 
    _ID_NOT_SET = 0,
    ID = 1,
  }

  export enum CaseidCase { 
    _CASEID_NOT_SET = 0,
    CASEID = 2,
  }

  export enum RawidCase { 
    _RAWID_NOT_SET = 0,
    RAWID = 3,
  }

  export enum NameCase { 
    _NAME_NOT_SET = 0,
    NAME = 4,
  }

  export enum TypeCase { 
    _TYPE_NOT_SET = 0,
    TYPE = 5,
  }

  export enum LocationCase { 
    _LOCATION_NOT_SET = 0,
    LOCATION = 6,
  }

  export enum StatusCase { 
    _STATUS_NOT_SET = 0,
    STATUS = 7,
  }

  export enum MimetypeCase { 
    _MIMETYPE_NOT_SET = 0,
    MIMETYPE = 9,
  }

  export enum ObtainedatCase { 
    _OBTAINEDAT_NOT_SET = 0,
    OBTAINEDAT = 10,
  }

  export enum CreatedatCase { 
    _CREATEDAT_NOT_SET = 0,
    CREATEDAT = 11,
  }

  export enum UpdatedatCase { 
    _UPDATEDAT_NOT_SET = 0,
    UPDATEDAT = 12,
  }
}

export class CaseCommentGrpcModel extends jspb.Message {
  getId(): string;
  setId(value: string): CaseCommentGrpcModel;
  hasId(): boolean;
  clearId(): CaseCommentGrpcModel;

  getCaseid(): string;
  setCaseid(value: string): CaseCommentGrpcModel;
  hasCaseid(): boolean;
  clearCaseid(): CaseCommentGrpcModel;

  getComment(): string;
  setComment(value: string): CaseCommentGrpcModel;
  hasComment(): boolean;
  clearComment(): CaseCommentGrpcModel;

  getCreatedby(): string;
  setCreatedby(value: string): CaseCommentGrpcModel;
  hasCreatedby(): boolean;
  clearCreatedby(): CaseCommentGrpcModel;

  getCreatedat(): google_protobuf_timestamp_pb.Timestamp | undefined;
  setCreatedat(value?: google_protobuf_timestamp_pb.Timestamp): CaseCommentGrpcModel;
  hasCreatedat(): boolean;
  clearCreatedat(): CaseCommentGrpcModel;

  serializeBinary(): Uint8Array;
  toObject(includeInstance?: boolean): CaseCommentGrpcModel.AsObject;
  static toObject(includeInstance: boolean, msg: CaseCommentGrpcModel): CaseCommentGrpcModel.AsObject;
  static serializeBinaryToWriter(message: CaseCommentGrpcModel, writer: jspb.BinaryWriter): void;
  static deserializeBinary(bytes: Uint8Array): CaseCommentGrpcModel;
  static deserializeBinaryFromReader(message: CaseCommentGrpcModel, reader: jspb.BinaryReader): CaseCommentGrpcModel;
}

export namespace CaseCommentGrpcModel {
  export type AsObject = {
    id?: string,
    caseid?: string,
    comment?: string,
    createdby?: string,
    createdat?: google_protobuf_timestamp_pb.Timestamp.AsObject,
  }

  export enum IdCase { 
    _ID_NOT_SET = 0,
    ID = 1,
  }

  export enum CaseidCase { 
    _CASEID_NOT_SET = 0,
    CASEID = 2,
  }

  export enum CommentCase { 
    _COMMENT_NOT_SET = 0,
    COMMENT = 3,
  }

  export enum CreatedbyCase { 
    _CREATEDBY_NOT_SET = 0,
    CREATEDBY = 4,
  }

  export enum CreatedatCase { 
    _CREATEDAT_NOT_SET = 0,
    CREATEDAT = 5,
  }
}

export class CaseCoincidenceGrpcModel extends jspb.Message {
  getId(): string;
  setId(value: string): CaseCoincidenceGrpcModel;
  hasId(): boolean;
  clearId(): CaseCoincidenceGrpcModel;

  getCaseid(): string;
  setCaseid(value: string): CaseCoincidenceGrpcModel;
  hasCaseid(): boolean;
  clearCaseid(): CaseCoincidenceGrpcModel;

  getEvidenceid(): string;
  setEvidenceid(value: string): CaseCoincidenceGrpcModel;
  hasEvidenceid(): boolean;
  clearEvidenceid(): CaseCoincidenceGrpcModel;

  getCoincidencesubjectid(): string;
  setCoincidencesubjectid(value: string): CaseCoincidenceGrpcModel;
  hasCoincidencesubjectid(): boolean;
  clearCoincidencesubjectid(): CaseCoincidenceGrpcModel;

  getType(): string;
  setType(value: string): CaseCoincidenceGrpcModel;
  hasType(): boolean;
  clearType(): CaseCoincidenceGrpcModel;

  getStatus(): string;
  setStatus(value: string): CaseCoincidenceGrpcModel;
  hasStatus(): boolean;
  clearStatus(): CaseCoincidenceGrpcModel;

  getScore(): number;
  setScore(value: number): CaseCoincidenceGrpcModel;
  hasScore(): boolean;
  clearScore(): CaseCoincidenceGrpcModel;

  getCreatedby(): string;
  setCreatedby(value: string): CaseCoincidenceGrpcModel;
  hasCreatedby(): boolean;
  clearCreatedby(): CaseCoincidenceGrpcModel;

  getUpdatedby(): string;
  setUpdatedby(value: string): CaseCoincidenceGrpcModel;
  hasUpdatedby(): boolean;
  clearUpdatedby(): CaseCoincidenceGrpcModel;

  getCreatedat(): google_protobuf_timestamp_pb.Timestamp | undefined;
  setCreatedat(value?: google_protobuf_timestamp_pb.Timestamp): CaseCoincidenceGrpcModel;
  hasCreatedat(): boolean;
  clearCreatedat(): CaseCoincidenceGrpcModel;

  getUpdatedat(): google_protobuf_timestamp_pb.Timestamp | undefined;
  setUpdatedat(value?: google_protobuf_timestamp_pb.Timestamp): CaseCoincidenceGrpcModel;
  hasUpdatedat(): boolean;
  clearUpdatedat(): CaseCoincidenceGrpcModel;

  serializeBinary(): Uint8Array;
  toObject(includeInstance?: boolean): CaseCoincidenceGrpcModel.AsObject;
  static toObject(includeInstance: boolean, msg: CaseCoincidenceGrpcModel): CaseCoincidenceGrpcModel.AsObject;
  static serializeBinaryToWriter(message: CaseCoincidenceGrpcModel, writer: jspb.BinaryWriter): void;
  static deserializeBinary(bytes: Uint8Array): CaseCoincidenceGrpcModel;
  static deserializeBinaryFromReader(message: CaseCoincidenceGrpcModel, reader: jspb.BinaryReader): CaseCoincidenceGrpcModel;
}

export namespace CaseCoincidenceGrpcModel {
  export type AsObject = {
    id?: string,
    caseid?: string,
    evidenceid?: string,
    coincidencesubjectid?: string,
    type?: string,
    status?: string,
    score?: number,
    createdby?: string,
    updatedby?: string,
    createdat?: google_protobuf_timestamp_pb.Timestamp.AsObject,
    updatedat?: google_protobuf_timestamp_pb.Timestamp.AsObject,
  }

  export enum IdCase { 
    _ID_NOT_SET = 0,
    ID = 1,
  }

  export enum CaseidCase { 
    _CASEID_NOT_SET = 0,
    CASEID = 2,
  }

  export enum EvidenceidCase { 
    _EVIDENCEID_NOT_SET = 0,
    EVIDENCEID = 3,
  }

  export enum CoincidencesubjectidCase { 
    _COINCIDENCESUBJECTID_NOT_SET = 0,
    COINCIDENCESUBJECTID = 4,
  }

  export enum TypeCase { 
    _TYPE_NOT_SET = 0,
    TYPE = 5,
  }

  export enum StatusCase { 
    _STATUS_NOT_SET = 0,
    STATUS = 6,
  }

  export enum ScoreCase { 
    _SCORE_NOT_SET = 0,
    SCORE = 7,
  }

  export enum CreatedbyCase { 
    _CREATEDBY_NOT_SET = 0,
    CREATEDBY = 8,
  }

  export enum UpdatedbyCase { 
    _UPDATEDBY_NOT_SET = 0,
    UPDATEDBY = 9,
  }

  export enum CreatedatCase { 
    _CREATEDAT_NOT_SET = 0,
    CREATEDAT = 10,
  }

  export enum UpdatedatCase { 
    _UPDATEDAT_NOT_SET = 0,
    UPDATEDAT = 11,
  }
}

export class CriminalisticCaseCommentResponse extends jspb.Message {
  getFailure(): failure_pb.FailureGrpc | undefined;
  setFailure(value?: failure_pb.FailureGrpc): CriminalisticCaseCommentResponse;
  hasFailure(): boolean;
  clearFailure(): CriminalisticCaseCommentResponse;

  getCasecomment(): CaseCommentGrpcModel | undefined;
  setCasecomment(value?: CaseCommentGrpcModel): CriminalisticCaseCommentResponse;
  hasCasecomment(): boolean;
  clearCasecomment(): CriminalisticCaseCommentResponse;

  getResponseCase(): CriminalisticCaseCommentResponse.ResponseCase;

  serializeBinary(): Uint8Array;
  toObject(includeInstance?: boolean): CriminalisticCaseCommentResponse.AsObject;
  static toObject(includeInstance: boolean, msg: CriminalisticCaseCommentResponse): CriminalisticCaseCommentResponse.AsObject;
  static serializeBinaryToWriter(message: CriminalisticCaseCommentResponse, writer: jspb.BinaryWriter): void;
  static deserializeBinary(bytes: Uint8Array): CriminalisticCaseCommentResponse;
  static deserializeBinaryFromReader(message: CriminalisticCaseCommentResponse, reader: jspb.BinaryReader): CriminalisticCaseCommentResponse;
}

export namespace CriminalisticCaseCommentResponse {
  export type AsObject = {
    failure?: failure_pb.FailureGrpc.AsObject,
    casecomment?: CaseCommentGrpcModel.AsObject,
  }

  export enum ResponseCase { 
    RESPONSE_NOT_SET = 0,
    FAILURE = 1,
    CASECOMMENT = 2,
  }
}

export class CriminalisticCaseEvidenceResponse extends jspb.Message {
  getFailure(): failure_pb.FailureGrpc | undefined;
  setFailure(value?: failure_pb.FailureGrpc): CriminalisticCaseEvidenceResponse;
  hasFailure(): boolean;
  clearFailure(): CriminalisticCaseEvidenceResponse;

  getCaseevidence(): CaseEvidenceGrpcModel | undefined;
  setCaseevidence(value?: CaseEvidenceGrpcModel): CriminalisticCaseEvidenceResponse;
  hasCaseevidence(): boolean;
  clearCaseevidence(): CriminalisticCaseEvidenceResponse;

  getResponseCase(): CriminalisticCaseEvidenceResponse.ResponseCase;

  serializeBinary(): Uint8Array;
  toObject(includeInstance?: boolean): CriminalisticCaseEvidenceResponse.AsObject;
  static toObject(includeInstance: boolean, msg: CriminalisticCaseEvidenceResponse): CriminalisticCaseEvidenceResponse.AsObject;
  static serializeBinaryToWriter(message: CriminalisticCaseEvidenceResponse, writer: jspb.BinaryWriter): void;
  static deserializeBinary(bytes: Uint8Array): CriminalisticCaseEvidenceResponse;
  static deserializeBinaryFromReader(message: CriminalisticCaseEvidenceResponse, reader: jspb.BinaryReader): CriminalisticCaseEvidenceResponse;
}

export namespace CriminalisticCaseEvidenceResponse {
  export type AsObject = {
    failure?: failure_pb.FailureGrpc.AsObject,
    caseevidence?: CaseEvidenceGrpcModel.AsObject,
  }

  export enum ResponseCase { 
    RESPONSE_NOT_SET = 0,
    FAILURE = 1,
    CASEEVIDENCE = 2,
  }
}

export class CriminalisticCaseDetailResponse extends jspb.Message {
  getFailure(): failure_pb.FailureGrpc | undefined;
  setFailure(value?: failure_pb.FailureGrpc): CriminalisticCaseDetailResponse;
  hasFailure(): boolean;
  clearFailure(): CriminalisticCaseDetailResponse;

  getCasedetails(): ArrayOfCaseDetailGrpcModel | undefined;
  setCasedetails(value?: ArrayOfCaseDetailGrpcModel): CriminalisticCaseDetailResponse;
  hasCasedetails(): boolean;
  clearCasedetails(): CriminalisticCaseDetailResponse;

  getResponseCase(): CriminalisticCaseDetailResponse.ResponseCase;

  serializeBinary(): Uint8Array;
  toObject(includeInstance?: boolean): CriminalisticCaseDetailResponse.AsObject;
  static toObject(includeInstance: boolean, msg: CriminalisticCaseDetailResponse): CriminalisticCaseDetailResponse.AsObject;
  static serializeBinaryToWriter(message: CriminalisticCaseDetailResponse, writer: jspb.BinaryWriter): void;
  static deserializeBinary(bytes: Uint8Array): CriminalisticCaseDetailResponse;
  static deserializeBinaryFromReader(message: CriminalisticCaseDetailResponse, reader: jspb.BinaryReader): CriminalisticCaseDetailResponse;
}

export namespace CriminalisticCaseDetailResponse {
  export type AsObject = {
    failure?: failure_pb.FailureGrpc.AsObject,
    casedetails?: ArrayOfCaseDetailGrpcModel.AsObject,
  }

  export enum ResponseCase { 
    RESPONSE_NOT_SET = 0,
    FAILURE = 1,
    CASEDETAILS = 2,
  }
}

export class CriminalisticRelatedCasesResponse extends jspb.Message {
  getFailure(): failure_pb.FailureGrpc | undefined;
  setFailure(value?: failure_pb.FailureGrpc): CriminalisticRelatedCasesResponse;
  hasFailure(): boolean;
  clearFailure(): CriminalisticRelatedCasesResponse;

  getRelatedcases(): ArrayOfRelatedCaseGrpcModel | undefined;
  setRelatedcases(value?: ArrayOfRelatedCaseGrpcModel): CriminalisticRelatedCasesResponse;
  hasRelatedcases(): boolean;
  clearRelatedcases(): CriminalisticRelatedCasesResponse;

  getResponseCase(): CriminalisticRelatedCasesResponse.ResponseCase;

  serializeBinary(): Uint8Array;
  toObject(includeInstance?: boolean): CriminalisticRelatedCasesResponse.AsObject;
  static toObject(includeInstance: boolean, msg: CriminalisticRelatedCasesResponse): CriminalisticRelatedCasesResponse.AsObject;
  static serializeBinaryToWriter(message: CriminalisticRelatedCasesResponse, writer: jspb.BinaryWriter): void;
  static deserializeBinary(bytes: Uint8Array): CriminalisticRelatedCasesResponse;
  static deserializeBinaryFromReader(message: CriminalisticRelatedCasesResponse, reader: jspb.BinaryReader): CriminalisticRelatedCasesResponse;
}

export namespace CriminalisticRelatedCasesResponse {
  export type AsObject = {
    failure?: failure_pb.FailureGrpc.AsObject,
    relatedcases?: ArrayOfRelatedCaseGrpcModel.AsObject,
  }

  export enum ResponseCase { 
    RESPONSE_NOT_SET = 0,
    FAILURE = 1,
    RELATEDCASES = 2,
  }
}

export class CriminalisticSubjectRelatedCasesResponse extends jspb.Message {
  getFailure(): failure_pb.FailureGrpc | undefined;
  setFailure(value?: failure_pb.FailureGrpc): CriminalisticSubjectRelatedCasesResponse;
  hasFailure(): boolean;
  clearFailure(): CriminalisticSubjectRelatedCasesResponse;

  getCaserelatedsubjects(): ArrayOfSubjectRelatedCaseGrpcModel | undefined;
  setCaserelatedsubjects(value?: ArrayOfSubjectRelatedCaseGrpcModel): CriminalisticSubjectRelatedCasesResponse;
  hasCaserelatedsubjects(): boolean;
  clearCaserelatedsubjects(): CriminalisticSubjectRelatedCasesResponse;

  getResponseCase(): CriminalisticSubjectRelatedCasesResponse.ResponseCase;

  serializeBinary(): Uint8Array;
  toObject(includeInstance?: boolean): CriminalisticSubjectRelatedCasesResponse.AsObject;
  static toObject(includeInstance: boolean, msg: CriminalisticSubjectRelatedCasesResponse): CriminalisticSubjectRelatedCasesResponse.AsObject;
  static serializeBinaryToWriter(message: CriminalisticSubjectRelatedCasesResponse, writer: jspb.BinaryWriter): void;
  static deserializeBinary(bytes: Uint8Array): CriminalisticSubjectRelatedCasesResponse;
  static deserializeBinaryFromReader(message: CriminalisticSubjectRelatedCasesResponse, reader: jspb.BinaryReader): CriminalisticSubjectRelatedCasesResponse;
}

export namespace CriminalisticSubjectRelatedCasesResponse {
  export type AsObject = {
    failure?: failure_pb.FailureGrpc.AsObject,
    caserelatedsubjects?: ArrayOfSubjectRelatedCaseGrpcModel.AsObject,
  }

  export enum ResponseCase { 
    RESPONSE_NOT_SET = 0,
    FAILURE = 1,
    CASERELATEDSUBJECTS = 2,
  }
}

export class CriminalisticLocationCasesResponse extends jspb.Message {
  getFailure(): failure_pb.FailureGrpc | undefined;
  setFailure(value?: failure_pb.FailureGrpc): CriminalisticLocationCasesResponse;
  hasFailure(): boolean;
  clearFailure(): CriminalisticLocationCasesResponse;

  getLocationcases(): ArrayOfCaseLocationGrpcModel | undefined;
  setLocationcases(value?: ArrayOfCaseLocationGrpcModel): CriminalisticLocationCasesResponse;
  hasLocationcases(): boolean;
  clearLocationcases(): CriminalisticLocationCasesResponse;

  getResponseCase(): CriminalisticLocationCasesResponse.ResponseCase;

  serializeBinary(): Uint8Array;
  toObject(includeInstance?: boolean): CriminalisticLocationCasesResponse.AsObject;
  static toObject(includeInstance: boolean, msg: CriminalisticLocationCasesResponse): CriminalisticLocationCasesResponse.AsObject;
  static serializeBinaryToWriter(message: CriminalisticLocationCasesResponse, writer: jspb.BinaryWriter): void;
  static deserializeBinary(bytes: Uint8Array): CriminalisticLocationCasesResponse;
  static deserializeBinaryFromReader(message: CriminalisticLocationCasesResponse, reader: jspb.BinaryReader): CriminalisticLocationCasesResponse;
}

export namespace CriminalisticLocationCasesResponse {
  export type AsObject = {
    failure?: failure_pb.FailureGrpc.AsObject,
    locationcases?: ArrayOfCaseLocationGrpcModel.AsObject,
  }

  export enum ResponseCase { 
    RESPONSE_NOT_SET = 0,
    FAILURE = 1,
    LOCATIONCASES = 2,
  }
}

export class NumberResponse extends jspb.Message {
  getFailure(): failure_pb.FailureGrpc | undefined;
  setFailure(value?: failure_pb.FailureGrpc): NumberResponse;
  hasFailure(): boolean;
  clearFailure(): NumberResponse;

  getValue(): number;
  setValue(value: number): NumberResponse;

  getResponseCase(): NumberResponse.ResponseCase;

  serializeBinary(): Uint8Array;
  toObject(includeInstance?: boolean): NumberResponse.AsObject;
  static toObject(includeInstance: boolean, msg: NumberResponse): NumberResponse.AsObject;
  static serializeBinaryToWriter(message: NumberResponse, writer: jspb.BinaryWriter): void;
  static deserializeBinary(bytes: Uint8Array): NumberResponse;
  static deserializeBinaryFromReader(message: NumberResponse, reader: jspb.BinaryReader): NumberResponse;
}

export namespace NumberResponse {
  export type AsObject = {
    failure?: failure_pb.FailureGrpc.AsObject,
    value: number,
  }

  export enum ResponseCase { 
    RESPONSE_NOT_SET = 0,
    FAILURE = 1,
    VALUE = 2,
  }
}

export class CriminalisticCaseCoincidenceResponse extends jspb.Message {
  getFailure(): failure_pb.FailureGrpc | undefined;
  setFailure(value?: failure_pb.FailureGrpc): CriminalisticCaseCoincidenceResponse;
  hasFailure(): boolean;
  clearFailure(): CriminalisticCaseCoincidenceResponse;

  getCasecoincidence(): CaseCoincidenceGrpcModel | undefined;
  setCasecoincidence(value?: CaseCoincidenceGrpcModel): CriminalisticCaseCoincidenceResponse;
  hasCasecoincidence(): boolean;
  clearCasecoincidence(): CriminalisticCaseCoincidenceResponse;

  getResponseCase(): CriminalisticCaseCoincidenceResponse.ResponseCase;

  serializeBinary(): Uint8Array;
  toObject(includeInstance?: boolean): CriminalisticCaseCoincidenceResponse.AsObject;
  static toObject(includeInstance: boolean, msg: CriminalisticCaseCoincidenceResponse): CriminalisticCaseCoincidenceResponse.AsObject;
  static serializeBinaryToWriter(message: CriminalisticCaseCoincidenceResponse, writer: jspb.BinaryWriter): void;
  static deserializeBinary(bytes: Uint8Array): CriminalisticCaseCoincidenceResponse;
  static deserializeBinaryFromReader(message: CriminalisticCaseCoincidenceResponse, reader: jspb.BinaryReader): CriminalisticCaseCoincidenceResponse;
}

export namespace CriminalisticCaseCoincidenceResponse {
  export type AsObject = {
    failure?: failure_pb.FailureGrpc.AsObject,
    casecoincidence?: CaseCoincidenceGrpcModel.AsObject,
  }

  export enum ResponseCase { 
    RESPONSE_NOT_SET = 0,
    FAILURE = 1,
    CASECOINCIDENCE = 2,
  }
}

