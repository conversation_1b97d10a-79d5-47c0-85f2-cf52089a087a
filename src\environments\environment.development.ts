export const environment = {
  version: '2.0.15',
  languages: [
    { code: 'en', label: 'english', flag: 'verazial-common-frontend/assets/images/all/united-kingdom.png' },
    { code: 'es', label: 'spanish', flag: 'verazial-common-frontend/assets/images/all/spain.png' },
    // { code: 'pt', label: 'portuguese', flag: 'verazial-common-frontend/assets/images/all/portugal.png' },
  ],
  production: false,
  defaultLanguage: "en",
  title: 'Development environment',
  application: 'ADMIN',
  generalSettings: 'GENERAL_SETTINGS',
  token: 'user',
  // Encription
  SECRET_KEY: 'KB0NsJxDCf@Y$Trr]BmU(1BTM*[mwxRgf]jneNzH)YjY7B%9A#>sh7SK%ysEWSA3',
  SECRET_IV: '^.syfebH(Tzr$KfRp(JpG+jpa*Bve)c3*4bP2jC%Uz)Wt=[q(844Fc]#NNgb^CK(',

  // gRPC - Microservices
  staticApiGatewayURL: true,
  grpcApiGateway: 'https://dev-gatewaygrpc.verazial.com',
  grpcToken: '',
  // Konektor
  konektorAPI: 'https://localhost:8443',

  // Verázial Credentials API
  credentialsAPI: 'https://dev2-pass.verazial.com:10029/api/v1/',
  credential_user: 'verazial',
  credential_password: '@4S9w&JMhg27',


  /*applicationNames: [
    { "name": "menu.all" },
    { "name": "Widget 4.1.0" },
    { "name": "Admin 2.0.3" },
    { "name": "Manager 2.1.7" },
    { "name": "Clock 2.0.1" },
  ],*/
  profileNames: [
    { "name": "I" }, // Interno
    { "name": "Ex" }, // Externo
    { "name": "Admin" }, // Administrador
    { "name": "En" }, // Enrolador
  ],
  /*actionNames: [
    { "name": "menu.all" }, // Todas las acciones
    { "name": "MOD_BIO" }, // Modificación biométrica
    //{ "name": "ID_SAM" }, // Identificación biométrica
    { "name": "MCH_IDN" }, // Identificación biométrica
    { "name": "ID_PIN" }, // Identificación no biométrica (cédula)
    { "name": "MCH_VRF" }, // Verificación biométrica
    //{ "name": "VER_INI" }, // Login con Verificación biométrica
    //{ "name": "VER_COMP" }, // Verificación biométrica complementaria
    { "name": "REM_SAM" }, // Registrar muestra
    { "name": "NEW_SAM" }, // Eliminar muestra
    { "name": "NEW_SUB" }, // Registrar sujeto
    { "name": "REM_SUB_BT" }, // Eliminar sujeto
    { "name": "REM_SUB_SAM" }, // Eliminar sujeto
    //{ "name": "ID_PASS" }, // Login con Cédula/Password
    //{ "name": "ADD_PIC" }, // Registrar foto de perfil
    //{ "name": "REM_PIC" }, // Eliminar foto de perfil
    //{ "name": "ADD_PIC_EX" }, // Registrar foto física
    //{ "name": "REM_PIC_EX" }, // Eliminar foto física
    { "name": "NO_DEV" }, // Dispositivo no detectado
  ],*/

  techNames: [
    { "name": "menu.all" },
    { "name": "menu.fingerprint" }, // Finger
    { "name": "menu.facial" }, // Facial
    { "name": "menu.iris" }, // Iris
  ],


  applicationDefault: "menu.all",
  customerName: "Verázial",
  profileDefault: "Admin",
  actionDefault: "menu.all",
  techDefault: "menu.all",

  sensorBrandsFinger: [{ "brand": "KOJAK_v3.1.2" }, { "brand": "WATSON_v1.0.2" }],
  sensorBrandsIris: [{ "brand": "R100 Iris" }],
  sensorBrandsFacial: [{ "brand": "" }],
  rangeDaysBefore: 30,
  rangeMaxMonths: 6,
  colorVerified: "#0AB4BA",
  colorNotVerified: "#F97316",
  colorQualityError: "#EAB308",
  colorTimeoutError: "#9FA9B7",
  colorServerError: "#EA5455",
  colorOther: "#763CAD",
  colorProfile1: "#0295A3",
  colorProfile2: "#42BDC9",
  colorProfile3: "#68D1DB",
  colorProfile4: "#9BE6ED",
  colorProfile5: "#A7F7FF",
  colorRequest: "#3B82F6",
  colorActionVerification: "#65D2E4",
  colorActionIdentification: "#6366F1",
  colorActionAddSample: "#119C8D",
  colorActionDeleteSample: "#FFA352",
  colorDisabled: "#cccccc",
  colorDisabledA: "#ececec",
  colorDisabledB: "#acacac",
  barThickness: 15,

  maxTable: 10000

};