import {
    GetCasesRequestEntity
} from "src/verazial-common-frontend/core/general/criminalistic-cases/domain/entity/get-cases-request.entity";
import {CaseEntity} from "src/verazial-common-frontend/core/general/criminalistic-cases/domain/entity/case.entity";
import {
    GetCaseByIdRequestEntity
} from "src/verazial-common-frontend/core/general/criminalistic-cases/domain/entity/get-case-by-id-request.entity";
import {
    CaseEvidenceEntity
} from "src/verazial-common-frontend/core/general/criminalistic-cases/domain/entity/caseEvidence.entity";
import {
    GetEvidenceByCaseIdRequestEntity
} from "src/verazial-common-frontend/core/general/criminalistic-cases/domain/entity/get-evidence-by-case-id-request.entity";
import {SuccessResponse} from "src/verazial-common-frontend/core/models/success-response.interface";
import {
    CaseDetailEntity
} from "src/verazial-common-frontend/core/general/criminalistic-cases/domain/entity/caseDetail.entity";
import {
    RelatedCaseEntity
} from "src/verazial-common-frontend/core/general/criminalistic-cases/domain/entity/relatedCase.entity";
import {
    RelatedSubjectEntity
} from "src/verazial-common-frontend/core/general/criminalistic-cases/domain/entity/relatedSubject.entity";
import {
    LocationEntity
} from "src/verazial-common-frontend/core/general/criminalistic-cases/domain/entity/location.entity";
import {
    CaseGeneralInfoEntity
} from "src/verazial-common-frontend/core/general/criminalistic-cases/domain/entity/caseGeneralInfo.entity";
import {
    CaseCommentEntity
} from "src/verazial-common-frontend/core/general/criminalistic-cases/domain/entity/caseComment.entity";
import {
    GetCommentByCaseIdRequestEntity
} from "src/verazial-common-frontend/core/general/criminalistic-cases/domain/entity/get-comment-by-case-id-request.entity";
import {
    CaseCoincidenceEntity
} from "src/verazial-common-frontend/core/general/criminalistic-cases/domain/entity/caseCoincidence.entity";
import {
    GetCoincidenceByCaseIdRequestEntity
} from "src/verazial-common-frontend/core/general/criminalistic-cases/domain/entity/get-coincidence-by-case-id-request.entity";


export abstract class CaseRepository {
    abstract getAllCases(params: GetCasesRequestEntity): Promise<CaseEntity[]>;

    abstract getNumberOfCases(): Promise<number>;

    abstract addCase(params: CaseEntity): Promise<CaseEntity>;

    abstract updateCase(params: CaseEntity): Promise<CaseEntity>;

    abstract updateCaseGeneralInfo(params: CaseGeneralInfoEntity): Promise<CaseGeneralInfoEntity>;

    abstract updateCaseDetails(params: CaseDetailEntity[]): Promise<CaseDetailEntity[]>;

    abstract updateRelatedCases(params: RelatedCaseEntity[]): Promise<RelatedCaseEntity[]>;

    abstract updateRelatedSubjects(params: RelatedSubjectEntity[]): Promise<RelatedSubjectEntity[]>;

    abstract updateCaseLocations(params: LocationEntity[]): Promise<LocationEntity[]>;

    abstract getCaseById(params: GetCaseByIdRequestEntity): Promise<CaseEntity>;

    abstract addEvidenceCase(params: CaseEvidenceEntity): Promise<CaseEvidenceEntity>;

    abstract updateEvidenceCase(params: CaseEvidenceEntity): Promise<CaseEvidenceEntity>;

    abstract getEvidenceByCaseId(params: GetEvidenceByCaseIdRequestEntity): Promise<CaseEvidenceEntity[]>;

    abstract deleteEvidenceById(params: GetCaseByIdRequestEntity): Promise<SuccessResponse>;

    abstract deleteCaseById(params: GetCaseByIdRequestEntity): Promise<SuccessResponse>;

    abstract deleteAllCaseLocationsById(params: GetCaseByIdRequestEntity): Promise<SuccessResponse>;

    abstract deleteAllRelatedCasesById(params: GetCaseByIdRequestEntity): Promise<SuccessResponse>;

    abstract deleteAllRelatedSubjectsById(params: GetCaseByIdRequestEntity): Promise<SuccessResponse>;

    abstract addCaseComment(params: CaseCommentEntity): Promise<CaseCommentEntity>;

    abstract getCommentByCaseId(params: GetCommentByCaseIdRequestEntity): Promise<CaseCommentEntity[]>;

    abstract addCaseCoincidence(params: CaseCoincidenceEntity): Promise<CaseCoincidenceEntity>

    abstract updateCaseCoincidence(params: CaseCoincidenceEntity): Promise<CaseCoincidenceEntity>

    abstract deleteCaseCoincidenceById(params: GetCaseByIdRequestEntity): Promise<SuccessResponse>

    abstract getAllCaseCoincidenceByCaseId(params: GetCoincidenceByCaseIdRequestEntity): Promise<CaseCoincidenceEntity[]>
}