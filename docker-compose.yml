version: '3.8'

services:
  angular-app:
    container_name: verazial-admin-app
    image: verazial/admin:v2.0.15
    build:
      context: .
      dockerfile: Dockerfile
    ports:
      - "20002:80" # DEV2
      # - "9093:80" # DEV & QA & DEMO
    restart: unless-stopped
    networks:
      - verazial-network # DEV2
      # - backend_biowidget-net # DEV & QA & DEMO

networks:
  verazial-network: # DEV2
  # backend_biowidget-net: # DEV & QA & DEMO
    external: true
