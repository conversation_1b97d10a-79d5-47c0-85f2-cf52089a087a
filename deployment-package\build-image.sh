#!/bin/bash

# Verazial System Admin Frontend - Docker Image Builder
# This script builds the Docker image and exports it as a .tar file for distribution

set -e

echo "=========================================="
echo "Verazial Admin Frontend Image Builder"
echo "=========================================="

# Check if Docker is running
if ! docker info > /dev/null 2>&1; then
    echo "Error: Docker is not running. Please start Docker and try again."
    exit 1
fi

# Navigate to the deployment directory
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
cd "$SCRIPT_DIR"

# Get version from package.json or use default
VERSION="v2.0.16"
IMAGE_NAME="verazial/admin"
IMAGE_TAG="${IMAGE_NAME}:${VERSION}"
TAR_FILE="verazial-admin-${VERSION}.tar"

echo "Building Docker image: ${IMAGE_TAG}"
echo "Output file: ${TAR_FILE}"
echo ""

# Build the Docker image
echo "Step 1: Building Docker image..."
docker build -f Dockerfile -t "${IMAGE_TAG}" ..

echo ""
echo "Step 2: Exporting Docker image to tar file..."
docker save "${IMAGE_TAG}" -o "${TAR_FILE}"

# Get file size
FILE_SIZE=$(du -h "${TAR_FILE}" | cut -f1)

echo ""
echo "=========================================="
echo "Image build completed successfully!"
echo "=========================================="
echo ""
echo "Docker image: ${IMAGE_TAG}"
echo "Exported file: ${TAR_FILE}"
echo "File size: ${FILE_SIZE}"
echo ""
echo "To load this image on another system:"
echo "  docker load -i ${TAR_FILE}"
echo ""
echo "To verify the loaded image:"
echo "  docker images | grep verazial/admin"
echo ""

# Optional: Show image details
echo "Image details:"
docker images "${IMAGE_TAG}" --format "table {{.Repository}}\t{{.Tag}}\t{{.ID}}\t{{.CreatedAt}}\t{{.Size}}"
echo ""

# Create a simple load script
cat > load-image.sh << EOF
#!/bin/bash
# Script to load the Verazial Admin Docker image

echo "Loading Verazial Admin Docker image..."
docker load -i ${TAR_FILE}

echo "Image loaded successfully!"
echo "Available images:"
docker images | grep verazial/admin

echo ""
echo "To deploy the application:"
echo "1. Edit the .env file with your configuration"
echo "2. Run: docker-compose up -d"
EOF

chmod +x load-image.sh

echo "Created load-image.sh script for easy image loading."
echo ""
echo "Deployment package is ready!"
echo "Files created:"
echo "  - ${TAR_FILE} (Docker image)"
echo "  - load-image.sh (Image loading script)"
echo "  - docker-compose.yml (Deployment configuration)"
echo "  - .env (Environment variables)"
echo "  - README.md (Documentation)"
