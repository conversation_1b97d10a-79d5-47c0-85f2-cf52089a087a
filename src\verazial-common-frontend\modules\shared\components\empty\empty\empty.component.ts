import {Component, EventEmitter, Input, Output} from '@angular/core';
import {ConsoleLoggerService} from 'src/verazial-common-frontend/core/services/console-logger.service';
import {LocalStorageService} from 'src/verazial-common-frontend/core/services/local-storage.service';

@Component({
  selector: 'app-empty',
  templateUrl: './empty.component.html',
  styleUrl: './empty.component.css'
})
export class EmptyComponent {
  // Outputs
  @Output() clicked = new EventEmitter<boolean>();
  @Input() buttonLabel: string = "";
  @Input() titleLabel: string = "";
  @Input() secondaryLabel: string = "";
  @Input() contentHeight: string = "800px";
  @Input() contentWidth: string = "100%";
  @Input() readAndWritePermissions: boolean = false;
  @Input() showButton: boolean = true;

  debugMode: boolean = false;

  constructor(private localStorage: LocalStorageService,
    private consoleLogger: ConsoleLoggerService) { }

  ngOnInit(): void {
    this.debugMode = this.localStorage.isDebugModeEnabled();
  }

  onClickNew() {
    this.clicked.emit(true);
    this.consoleLogger.consoleLog(this.debugMode, true);
  }
}
