#!/bin/bash

# Verazial System Admin Frontend - Complete Deployment Package Creator
# This script creates a complete deployment package with Docker image and configuration files

set -e

echo "=========================================="
echo "Creating Verazial Admin Deployment Package"
echo "=========================================="

# Check if Docker is running
if ! docker info > /dev/null 2>&1; then
    echo "Error: Docker is not running. Please start Docker and try again."
    exit 1
fi

# Variables
# Get version using shared function
source deployment-package/get-version.sh
VERSION=$(get_app_version "src/environments/environment.ts" "v2.0.0")
echo "Using version: ${VERSION}"

IMAGE_NAME="verazial/admin"
IMAGE_TAG="${IMAGE_NAME}:${VERSION}"
PACKAGE_NAME="verazial-admin-deployment-${VERSION}"
TAR_FILE="verazial-admin-${VERSION}.tar"

echo "Building deployment package: ${PACKAGE_NAME}"
echo "Docker image: ${IMAGE_TAG}"
echo ""

# Step 1: Build the Docker image
echo "Step 1: Building Docker image..."
docker build -f deployment-package/Dockerfile -t "${IMAGE_TAG}" .

# Step 2: Export Docker image
echo ""
echo "Step 2: Exporting Docker image..."
docker save "${IMAGE_TAG}" -o "deployment-package/${TAR_FILE}"

# Step 3: Create deployment package directory
echo ""
echo "Step 3: Creating deployment package..."
rm -rf "${PACKAGE_NAME}"
mkdir -p "${PACKAGE_NAME}"

# Copy deployment files
cp "deployment-package/${TAR_FILE}" "${PACKAGE_NAME}/"
cp "deployment-package/.env" "${PACKAGE_NAME}/"
cp "deployment-package/README.md" "${PACKAGE_NAME}/"
cp "deployment-package/DEPLOYMENT_GUIDE.md" "${PACKAGE_NAME}/"

# Copy and update docker-compose.yml with the correct version
cp "deployment-package/docker-compose.yml" "${PACKAGE_NAME}/"
# Update the image version in docker-compose.yml to match the detected version
sed -i "s|verazial/admin:v[0-9]*\.[0-9]*\.[0-9]*|verazial/admin:${VERSION}|g" "${PACKAGE_NAME}/docker-compose.yml"
echo "Updated docker-compose.yml to use image version: ${VERSION}"

# Copy the existing scripts from deployment-package (which have all the latest fixes)
cp "deployment-package/deploy.sh" "${PACKAGE_NAME}/"
cp "deployment-package/get-version.sh" "${PACKAGE_NAME}/"
cp "deployment-package/test-env.sh" "${PACKAGE_NAME}/"

# Make scripts executable
chmod +x "${PACKAGE_NAME}/deploy.sh"
chmod +x "${PACKAGE_NAME}/get-version.sh"
chmod +x "${PACKAGE_NAME}/test-env.sh"

# Step 4: Create package archive
echo ""
echo "Step 4: Creating package archive..."
tar -czf "${PACKAGE_NAME}.tar.gz" "${PACKAGE_NAME}"

# Get file sizes
IMAGE_SIZE=$(du -h "deployment-package/${TAR_FILE}" | cut -f1)
PACKAGE_SIZE=$(du -h "${PACKAGE_NAME}.tar.gz" | cut -f1)

echo ""
echo "=========================================="
echo "Deployment package created successfully!"
echo "=========================================="
echo ""
echo "Package details:"
echo "  - Docker image: ${IMAGE_TAG}"
echo "  - Image size: ${IMAGE_SIZE}"
echo "  - Package file: ${PACKAGE_NAME}.tar.gz"
echo "  - Package size: ${PACKAGE_SIZE}"
echo ""
echo "Package contents:"
echo "  - ${TAR_FILE} (Docker image)"
echo "  - docker-compose.yml (Container configuration)"
echo "  - .env (Environment variables)"
echo "  - deploy.sh (Complete deployment script)"
echo "  - get-version.sh (Version detection utility)"
echo "  - test-env.sh (Environment testing utility)"
echo "  - README.md (Detailed documentation)"
echo "  - DEPLOYMENT_GUIDE.md (Step-by-step guide)"
echo ""
echo "To deploy on a Linux server:"
echo "1. Transfer ${PACKAGE_NAME}.tar.gz to your server"
echo "2. Extract: tar -xzf ${PACKAGE_NAME}.tar.gz"
echo "3. Navigate: cd ${PACKAGE_NAME}"
echo "4. Configure: nano .env"
echo "5. Deploy: ./deploy.sh"
echo ""
echo "The deploy.sh script will automatically:"
echo "  - Load the Docker image if needed"
echo "  - Start the application with your configuration"
echo "  - Show you the access URL"
echo ""
echo "The application will be accessible at http://your-server-ip:[ADMIN_HOST_PORT]"
