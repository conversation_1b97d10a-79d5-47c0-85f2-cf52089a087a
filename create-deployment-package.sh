#!/bin/bash

# Verazial System Admin Frontend - Complete Deployment Package Creator
# This script creates a complete deployment package with Docker image and configuration files

set -e

echo "=========================================="
echo "Creating Verazial Admin Deployment Package"
echo "=========================================="

# Check if Docker is running
if ! docker info > /dev/null 2>&1; then
    echo "Error: Docker is not running. Please start Docker and try again."
    exit 1
fi

# Variables
VERSION="v2.0.16"
IMAGE_NAME="verazial/admin"
IMAGE_TAG="${IMAGE_NAME}:${VERSION}"
PACKAGE_NAME="verazial-admin-deployment-${VERSION}"
TAR_FILE="verazial-admin-${VERSION}.tar"

echo "Building deployment package: ${PACKAGE_NAME}"
echo "Docker image: ${IMAGE_TAG}"
echo ""

# Step 1: Build the Docker image
echo "Step 1: Building Docker image..."
docker build -f deployment-package/Dockerfile -t "${IMAGE_TAG}" .

# Step 2: Export Docker image
echo ""
echo "Step 2: Exporting Docker image..."
docker save "${IMAGE_TAG}" -o "deployment-package/${TAR_FILE}"

# Step 3: Create deployment package directory
echo ""
echo "Step 3: Creating deployment package..."
rm -rf "${PACKAGE_NAME}"
mkdir -p "${PACKAGE_NAME}"

# Copy deployment files
cp "deployment-package/${TAR_FILE}" "${PACKAGE_NAME}/"
cp "deployment-package/docker-compose.yml" "${PACKAGE_NAME}/"
cp "deployment-package/.env" "${PACKAGE_NAME}/"
cp "deployment-package/README.md" "${PACKAGE_NAME}/"
cp "deployment-package/DEPLOYMENT_GUIDE.md" "${PACKAGE_NAME}/"

# Create load-image script
cat > "${PACKAGE_NAME}/load-image.sh" << EOF
#!/bin/bash
# Script to load the Verazial Admin Docker image

echo "Loading Verazial Admin Docker image..."
docker load -i ${TAR_FILE}

echo "Image loaded successfully!"
echo "Available images:"
docker images | grep verazial/admin

echo ""
echo "Next steps:"
echo "1. Edit the .env file with your configuration"
echo "2. Run: docker-compose up -d"
echo "3. Access the application at http://localhost:[HOST_PORT]"
EOF

# Create deploy script
cat > "${PACKAGE_NAME}/deploy.sh" << EOF
#!/bin/bash
# Verazial Admin Frontend - Quick Deploy Script

set -e

echo "=========================================="
echo "Verazial Admin Frontend Deployment"
echo "=========================================="

# Check if Docker is running
if ! docker info > /dev/null 2>&1; then
    echo "Error: Docker is not running. Please start Docker and try again."
    exit 1
fi

# Load the image if not already loaded
if ! docker images | grep -q "verazial/admin"; then
    echo "Loading Docker image..."
    docker load -i ${TAR_FILE}
fi

# Check if .env file exists
if [ ! -f ".env" ]; then
    echo "Error: .env file not found. Please ensure the .env file exists."
    exit 1
fi

echo "Loading environment configuration..."
source .env

echo "Configuration:"
echo "  - Host Port: \${HOST_PORT}"
echo "  - gRPC API Gateway: \${GRPC_API_GATEWAY}"
echo "  - Konektor API: \${KONEKTOR_API}"
echo "  - Credentials API: \${CREDENTIALS_API}"

echo ""
read -p "Do you want to proceed with the deployment? (y/N): " -n 1 -r
echo
if [[ ! \$REPLY =~ ^[Yy]\$ ]]; then
    echo "Deployment cancelled."
    exit 0
fi

echo ""
echo "Deploying application..."
docker-compose up -d

echo ""
echo "=========================================="
echo "Deployment completed successfully!"
echo "=========================================="
echo ""
echo "Application is running at: http://localhost:\${HOST_PORT}"
echo ""
echo "Management commands:"
echo "  - Check status: docker-compose ps"
echo "  - View logs: docker-compose logs -f"
echo "  - Stop application: docker-compose down"
EOF

# Make scripts executable
chmod +x "${PACKAGE_NAME}/load-image.sh"
chmod +x "${PACKAGE_NAME}/deploy.sh"

# Step 4: Create package archive
echo ""
echo "Step 4: Creating package archive..."
tar -czf "${PACKAGE_NAME}.tar.gz" "${PACKAGE_NAME}"

# Get file sizes
IMAGE_SIZE=$(du -h "deployment-package/${TAR_FILE}" | cut -f1)
PACKAGE_SIZE=$(du -h "${PACKAGE_NAME}.tar.gz" | cut -f1)

echo ""
echo "=========================================="
echo "Deployment package created successfully!"
echo "=========================================="
echo ""
echo "Package details:"
echo "  - Docker image: ${IMAGE_TAG}"
echo "  - Image size: ${IMAGE_SIZE}"
echo "  - Package file: ${PACKAGE_NAME}.tar.gz"
echo "  - Package size: ${PACKAGE_SIZE}"
echo ""
echo "Package contents:"
echo "  - ${TAR_FILE} (Docker image)"
echo "  - docker-compose.yml (Container configuration)"
echo "  - .env (Environment variables)"
echo "  - load-image.sh (Image loading script)"
echo "  - deploy.sh (Quick deployment script)"
echo "  - README.md (Detailed documentation)"
echo "  - DEPLOYMENT_GUIDE.md (Step-by-step guide)"
echo ""
echo "To deploy on a Linux server:"
echo "1. Transfer ${PACKAGE_NAME}.tar.gz to your server"
echo "2. Extract: tar -xzf ${PACKAGE_NAME}.tar.gz"
echo "3. Navigate: cd ${PACKAGE_NAME}"
echo "4. Configure: nano .env"
echo "5. Deploy: ./deploy.sh"
echo ""
echo "The application will be accessible at http://your-server-ip:[HOST_PORT]"
