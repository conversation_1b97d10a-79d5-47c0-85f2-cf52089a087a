<p-toast/>
<div>
    <p-dialog appendTo="body" [(visible)]="showSearchDialog" [modal]="!isHidden"
              [style]="{ background: '#FFFFFF', 'border-radius': '6px', 'display': isHidden ? 'none' : 'block' }"
              [draggable]="false" [resizable]="false">
        <ng-template pTemplate="headless">
            <div class="closeWidgetButtonDiv">
                <button pButton pRipple icon="pi pi-times" class="buttonCloseBk closeWidgetButton"
                        (click)="closeSearchDialog()"></button>
            </div>
            <div *ngIf="!waitSelectTech else widgetSearchSplashScreen" [id]="widgetElementId + 'Div'"
                 [class]="widgetElementId + 'Div'">
                <ng-container *ngTemplateOutlet="widget"></ng-container>
            </div>
            <ng-template #widgetSearchSplashScreen>
                <div id="selectTechnology" [class]="widgetElementId + 'Div'">
                    <div [class]="widgetElementId + ' selectTechnology'">
                        <div *ngIf="waitSelectTech else loadingWidget" [class]="widgetElementId + ' selectTechnology'">
                            <img src="verazial-common-frontend/assets/images/all/selectTechnology.svg"/>
                            <label class="selectTechnologyLabel">{{ 'messages.selectTechnology' | translate }}</label>
                        </div>
                        <ng-template #loadingWidget>
                            <p-progressSpinner styleClass="w-5rem h-5rem" strokeWidth="8" fill="var(--surface-ground)"
                                               animationDuration=".5s" ariaLabel="loading"/>
                        </ng-template>
                    </div>
                    <div class="flex flex-column align-items-center" [style]="{'width':'100%'}"
                         pTooltip="{{ tenantMessage | translate}}" tooltipPosition="top">
                        <ngx-verazial-ui-verify
                                [verifyActive]="false"
                                [fingerprintActive]="fingerprintActive && isTenantValid()"
                                [facialActive]="facialActive && isTenantValid()"
                                [irisActive]="irisActive && isTenantValid()"
                                class="" name="{{ 'loginForm.loginOptions' | translate }}"
                                (technology)="search($event)"
                                srcFingerPrint="verazial-common-frontend/assets/images/bio-tech-buttons/md/FingerprintMain.svg"
                                srcFacial="verazial-common-frontend/assets/images/bio-tech-buttons/md/FacialMain.svg"
                                srcIris="verazial-common-frontend/assets/images/bio-tech-buttons/md/IrisMain.svg"
                                srcFingerPrintWhite="verazial-common-frontend/assets/images/bio-tech-buttons/md/FingerprintWhite.svg"
                                srcFacialWhite="verazial-common-frontend/assets/images/bio-tech-buttons/md/FacialWhite.svg"
                                srcIrisWhite="verazial-common-frontend/assets/images/bio-tech-buttons/md/IrisWhite.svg"
                                srcFingerPrintDisabled="verazial-common-frontend/assets/images/bio-tech-buttons/md/FingerPrintDisabled.svg"
                                srcFacialDisabled="verazial-common-frontend/assets/images/bio-tech-buttons/md/FacialDisabled.svg"
                                srcIrisDisabled="verazial-common-frontend/assets/images/bio-tech-buttons/md/IrisDisabled.svg">
                        </ngx-verazial-ui-verify>
                    </div>
                </div>
            </ng-template>
        </ng-template>
    </p-dialog>
</div>

<ng-template #widget>
    <div *ngIf="showWidget && ready else widgetSkelly">
        <iframe
                [id]="widgetElementId"
                [class]="widgetElementId"
                [src]="this.pageWidgetURL"
                frameBorder="0"
                allow="camera">
        </iframe>
    </div>
</ng-template>

<ng-template #widgetSkelly>
    <div [class]="widgetElementId">
        <p-skeleton width="100%" height="100%"></p-skeleton>
    </div>
</ng-template>