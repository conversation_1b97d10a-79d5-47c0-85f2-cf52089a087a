/**
 * @fileoverview gRPC-Web generated client stub for 
 * @enhanceable
 * @public
 */

// Code generated by protoc-gen-grpc-web. DO NOT EDIT.
// versions:
// 	protoc-gen-grpc-web v1.5.0
// 	protoc              v6.31.1
// source: criminalistics/criminalistics.proto


/* eslint-disable */
// @ts-nocheck


import * as grpcWeb from 'grpc-web';

import * as criminalistics_criminalistics_pb from '../criminalistics/criminalistics_pb'; // proto import: "criminalistics/criminalistics.proto"
import * as util_pb from '../util_pb'; // proto import: "util.proto"
import * as google_protobuf_empty_pb from 'google-protobuf/google/protobuf/empty_pb'; // proto import: "google/protobuf/empty.proto"


export class CoreCriminalisticsServiceClient {
  client_: grpcWeb.AbstractClientBase;
  hostname_: string;
  credentials_: null | { [index: string]: string; };
  options_: null | { [index: string]: any; };

  constructor (hostname: string,
               credentials?: null | { [index: string]: string; },
               options?: null | { [index: string]: any; }) {
    if (!options) options = {};
    if (!credentials) credentials = {};
    options['format'] = 'text';

    this.client_ = new grpcWeb.GrpcWebClientBase(options);
    this.hostname_ = hostname.replace(/\/+$/, '');
    this.credentials_ = credentials;
    this.options_ = options;
  }

  methodDescriptorcreateCase = new grpcWeb.MethodDescriptor(
    '/CoreCriminalisticsService/createCase',
    grpcWeb.MethodType.UNARY,
    criminalistics_criminalistics_pb.CaseGrpcModel,
    criminalistics_criminalistics_pb.CriminalisticCaseResponse,
    (request: criminalistics_criminalistics_pb.CaseGrpcModel) => {
      return request.serializeBinary();
    },
    criminalistics_criminalistics_pb.CriminalisticCaseResponse.deserializeBinary
  );

  createCase(
    request: criminalistics_criminalistics_pb.CaseGrpcModel,
    metadata?: grpcWeb.Metadata | null): Promise<criminalistics_criminalistics_pb.CriminalisticCaseResponse>;

  createCase(
    request: criminalistics_criminalistics_pb.CaseGrpcModel,
    metadata: grpcWeb.Metadata | null,
    callback: (err: grpcWeb.RpcError,
               response: criminalistics_criminalistics_pb.CriminalisticCaseResponse) => void): grpcWeb.ClientReadableStream<criminalistics_criminalistics_pb.CriminalisticCaseResponse>;

  createCase(
    request: criminalistics_criminalistics_pb.CaseGrpcModel,
    metadata?: grpcWeb.Metadata | null,
    callback?: (err: grpcWeb.RpcError,
               response: criminalistics_criminalistics_pb.CriminalisticCaseResponse) => void) {
    if (callback !== undefined) {
      return this.client_.rpcCall(
        this.hostname_ +
          '/CoreCriminalisticsService/createCase',
        request,
        metadata || {},
        this.methodDescriptorcreateCase,
        callback);
    }
    return this.client_.unaryCall(
    this.hostname_ +
      '/CoreCriminalisticsService/createCase',
    request,
    metadata || {},
    this.methodDescriptorcreateCase);
  }

  methodDescriptorupdateCase = new grpcWeb.MethodDescriptor(
    '/CoreCriminalisticsService/updateCase',
    grpcWeb.MethodType.UNARY,
    criminalistics_criminalistics_pb.CaseGrpcModel,
    criminalistics_criminalistics_pb.CriminalisticCaseResponse,
    (request: criminalistics_criminalistics_pb.CaseGrpcModel) => {
      return request.serializeBinary();
    },
    criminalistics_criminalistics_pb.CriminalisticCaseResponse.deserializeBinary
  );

  updateCase(
    request: criminalistics_criminalistics_pb.CaseGrpcModel,
    metadata?: grpcWeb.Metadata | null): Promise<criminalistics_criminalistics_pb.CriminalisticCaseResponse>;

  updateCase(
    request: criminalistics_criminalistics_pb.CaseGrpcModel,
    metadata: grpcWeb.Metadata | null,
    callback: (err: grpcWeb.RpcError,
               response: criminalistics_criminalistics_pb.CriminalisticCaseResponse) => void): grpcWeb.ClientReadableStream<criminalistics_criminalistics_pb.CriminalisticCaseResponse>;

  updateCase(
    request: criminalistics_criminalistics_pb.CaseGrpcModel,
    metadata?: grpcWeb.Metadata | null,
    callback?: (err: grpcWeb.RpcError,
               response: criminalistics_criminalistics_pb.CriminalisticCaseResponse) => void) {
    if (callback !== undefined) {
      return this.client_.rpcCall(
        this.hostname_ +
          '/CoreCriminalisticsService/updateCase',
        request,
        metadata || {},
        this.methodDescriptorupdateCase,
        callback);
    }
    return this.client_.unaryCall(
    this.hostname_ +
      '/CoreCriminalisticsService/updateCase',
    request,
    metadata || {},
    this.methodDescriptorupdateCase);
  }

  methodDescriptorgetAllCases = new grpcWeb.MethodDescriptor(
    '/CoreCriminalisticsService/getAllCases',
    grpcWeb.MethodType.SERVER_STREAMING,
    util_pb.OffsetLimit,
    criminalistics_criminalistics_pb.CaseGrpcModel,
    (request: util_pb.OffsetLimit) => {
      return request.serializeBinary();
    },
    criminalistics_criminalistics_pb.CaseGrpcModel.deserializeBinary
  );

  getAllCases(
    request: util_pb.OffsetLimit,
    metadata?: grpcWeb.Metadata): grpcWeb.ClientReadableStream<criminalistics_criminalistics_pb.CaseGrpcModel> {
    return this.client_.serverStreaming(
      this.hostname_ +
        '/CoreCriminalisticsService/getAllCases',
      request,
      metadata || {},
      this.methodDescriptorgetAllCases);
  }

  methodDescriptorgetNumberOfCases = new grpcWeb.MethodDescriptor(
    '/CoreCriminalisticsService/getNumberOfCases',
    grpcWeb.MethodType.UNARY,
    google_protobuf_empty_pb.Empty,
    criminalistics_criminalistics_pb.NumberResponse,
    (request: google_protobuf_empty_pb.Empty) => {
      return request.serializeBinary();
    },
    criminalistics_criminalistics_pb.NumberResponse.deserializeBinary
  );

  getNumberOfCases(
    request: google_protobuf_empty_pb.Empty,
    metadata?: grpcWeb.Metadata | null): Promise<criminalistics_criminalistics_pb.NumberResponse>;

  getNumberOfCases(
    request: google_protobuf_empty_pb.Empty,
    metadata: grpcWeb.Metadata | null,
    callback: (err: grpcWeb.RpcError,
               response: criminalistics_criminalistics_pb.NumberResponse) => void): grpcWeb.ClientReadableStream<criminalistics_criminalistics_pb.NumberResponse>;

  getNumberOfCases(
    request: google_protobuf_empty_pb.Empty,
    metadata?: grpcWeb.Metadata | null,
    callback?: (err: grpcWeb.RpcError,
               response: criminalistics_criminalistics_pb.NumberResponse) => void) {
    if (callback !== undefined) {
      return this.client_.rpcCall(
        this.hostname_ +
          '/CoreCriminalisticsService/getNumberOfCases',
        request,
        metadata || {},
        this.methodDescriptorgetNumberOfCases,
        callback);
    }
    return this.client_.unaryCall(
    this.hostname_ +
      '/CoreCriminalisticsService/getNumberOfCases',
    request,
    metadata || {},
    this.methodDescriptorgetNumberOfCases);
  }

  methodDescriptorgetCaseById = new grpcWeb.MethodDescriptor(
    '/CoreCriminalisticsService/getCaseById',
    grpcWeb.MethodType.UNARY,
    util_pb.StringParam,
    criminalistics_criminalistics_pb.CriminalisticCaseResponse,
    (request: util_pb.StringParam) => {
      return request.serializeBinary();
    },
    criminalistics_criminalistics_pb.CriminalisticCaseResponse.deserializeBinary
  );

  getCaseById(
    request: util_pb.StringParam,
    metadata?: grpcWeb.Metadata | null): Promise<criminalistics_criminalistics_pb.CriminalisticCaseResponse>;

  getCaseById(
    request: util_pb.StringParam,
    metadata: grpcWeb.Metadata | null,
    callback: (err: grpcWeb.RpcError,
               response: criminalistics_criminalistics_pb.CriminalisticCaseResponse) => void): grpcWeb.ClientReadableStream<criminalistics_criminalistics_pb.CriminalisticCaseResponse>;

  getCaseById(
    request: util_pb.StringParam,
    metadata?: grpcWeb.Metadata | null,
    callback?: (err: grpcWeb.RpcError,
               response: criminalistics_criminalistics_pb.CriminalisticCaseResponse) => void) {
    if (callback !== undefined) {
      return this.client_.rpcCall(
        this.hostname_ +
          '/CoreCriminalisticsService/getCaseById',
        request,
        metadata || {},
        this.methodDescriptorgetCaseById,
        callback);
    }
    return this.client_.unaryCall(
    this.hostname_ +
      '/CoreCriminalisticsService/getCaseById',
    request,
    metadata || {},
    this.methodDescriptorgetCaseById);
  }

  methodDescriptorcreateEvidence = new grpcWeb.MethodDescriptor(
    '/CoreCriminalisticsService/createEvidence',
    grpcWeb.MethodType.UNARY,
    criminalistics_criminalistics_pb.CaseEvidenceGrpcModel,
    criminalistics_criminalistics_pb.CriminalisticCaseEvidenceResponse,
    (request: criminalistics_criminalistics_pb.CaseEvidenceGrpcModel) => {
      return request.serializeBinary();
    },
    criminalistics_criminalistics_pb.CriminalisticCaseEvidenceResponse.deserializeBinary
  );

  createEvidence(
    request: criminalistics_criminalistics_pb.CaseEvidenceGrpcModel,
    metadata?: grpcWeb.Metadata | null): Promise<criminalistics_criminalistics_pb.CriminalisticCaseEvidenceResponse>;

  createEvidence(
    request: criminalistics_criminalistics_pb.CaseEvidenceGrpcModel,
    metadata: grpcWeb.Metadata | null,
    callback: (err: grpcWeb.RpcError,
               response: criminalistics_criminalistics_pb.CriminalisticCaseEvidenceResponse) => void): grpcWeb.ClientReadableStream<criminalistics_criminalistics_pb.CriminalisticCaseEvidenceResponse>;

  createEvidence(
    request: criminalistics_criminalistics_pb.CaseEvidenceGrpcModel,
    metadata?: grpcWeb.Metadata | null,
    callback?: (err: grpcWeb.RpcError,
               response: criminalistics_criminalistics_pb.CriminalisticCaseEvidenceResponse) => void) {
    if (callback !== undefined) {
      return this.client_.rpcCall(
        this.hostname_ +
          '/CoreCriminalisticsService/createEvidence',
        request,
        metadata || {},
        this.methodDescriptorcreateEvidence,
        callback);
    }
    return this.client_.unaryCall(
    this.hostname_ +
      '/CoreCriminalisticsService/createEvidence',
    request,
    metadata || {},
    this.methodDescriptorcreateEvidence);
  }

  methodDescriptorupdateEvidence = new grpcWeb.MethodDescriptor(
    '/CoreCriminalisticsService/updateEvidence',
    grpcWeb.MethodType.UNARY,
    criminalistics_criminalistics_pb.CaseEvidenceGrpcModel,
    criminalistics_criminalistics_pb.CriminalisticCaseEvidenceResponse,
    (request: criminalistics_criminalistics_pb.CaseEvidenceGrpcModel) => {
      return request.serializeBinary();
    },
    criminalistics_criminalistics_pb.CriminalisticCaseEvidenceResponse.deserializeBinary
  );

  updateEvidence(
    request: criminalistics_criminalistics_pb.CaseEvidenceGrpcModel,
    metadata?: grpcWeb.Metadata | null): Promise<criminalistics_criminalistics_pb.CriminalisticCaseEvidenceResponse>;

  updateEvidence(
    request: criminalistics_criminalistics_pb.CaseEvidenceGrpcModel,
    metadata: grpcWeb.Metadata | null,
    callback: (err: grpcWeb.RpcError,
               response: criminalistics_criminalistics_pb.CriminalisticCaseEvidenceResponse) => void): grpcWeb.ClientReadableStream<criminalistics_criminalistics_pb.CriminalisticCaseEvidenceResponse>;

  updateEvidence(
    request: criminalistics_criminalistics_pb.CaseEvidenceGrpcModel,
    metadata?: grpcWeb.Metadata | null,
    callback?: (err: grpcWeb.RpcError,
               response: criminalistics_criminalistics_pb.CriminalisticCaseEvidenceResponse) => void) {
    if (callback !== undefined) {
      return this.client_.rpcCall(
        this.hostname_ +
          '/CoreCriminalisticsService/updateEvidence',
        request,
        metadata || {},
        this.methodDescriptorupdateEvidence,
        callback);
    }
    return this.client_.unaryCall(
    this.hostname_ +
      '/CoreCriminalisticsService/updateEvidence',
    request,
    metadata || {},
    this.methodDescriptorupdateEvidence);
  }

  methodDescriptorgetEvidenceById = new grpcWeb.MethodDescriptor(
    '/CoreCriminalisticsService/getEvidenceById',
    grpcWeb.MethodType.UNARY,
    util_pb.StringParam,
    criminalistics_criminalistics_pb.CriminalisticCaseEvidenceResponse,
    (request: util_pb.StringParam) => {
      return request.serializeBinary();
    },
    criminalistics_criminalistics_pb.CriminalisticCaseEvidenceResponse.deserializeBinary
  );

  getEvidenceById(
    request: util_pb.StringParam,
    metadata?: grpcWeb.Metadata | null): Promise<criminalistics_criminalistics_pb.CriminalisticCaseEvidenceResponse>;

  getEvidenceById(
    request: util_pb.StringParam,
    metadata: grpcWeb.Metadata | null,
    callback: (err: grpcWeb.RpcError,
               response: criminalistics_criminalistics_pb.CriminalisticCaseEvidenceResponse) => void): grpcWeb.ClientReadableStream<criminalistics_criminalistics_pb.CriminalisticCaseEvidenceResponse>;

  getEvidenceById(
    request: util_pb.StringParam,
    metadata?: grpcWeb.Metadata | null,
    callback?: (err: grpcWeb.RpcError,
               response: criminalistics_criminalistics_pb.CriminalisticCaseEvidenceResponse) => void) {
    if (callback !== undefined) {
      return this.client_.rpcCall(
        this.hostname_ +
          '/CoreCriminalisticsService/getEvidenceById',
        request,
        metadata || {},
        this.methodDescriptorgetEvidenceById,
        callback);
    }
    return this.client_.unaryCall(
    this.hostname_ +
      '/CoreCriminalisticsService/getEvidenceById',
    request,
    metadata || {},
    this.methodDescriptorgetEvidenceById);
  }

  methodDescriptorgetEvidenceByCaseId = new grpcWeb.MethodDescriptor(
    '/CoreCriminalisticsService/getEvidenceByCaseId',
    grpcWeb.MethodType.SERVER_STREAMING,
    util_pb.StringParam,
    criminalistics_criminalistics_pb.CaseEvidenceGrpcModel,
    (request: util_pb.StringParam) => {
      return request.serializeBinary();
    },
    criminalistics_criminalistics_pb.CaseEvidenceGrpcModel.deserializeBinary
  );

  getEvidenceByCaseId(
    request: util_pb.StringParam,
    metadata?: grpcWeb.Metadata): grpcWeb.ClientReadableStream<criminalistics_criminalistics_pb.CaseEvidenceGrpcModel> {
    return this.client_.serverStreaming(
      this.hostname_ +
        '/CoreCriminalisticsService/getEvidenceByCaseId',
      request,
      metadata || {},
      this.methodDescriptorgetEvidenceByCaseId);
  }

  methodDescriptordeleteEvidenceById = new grpcWeb.MethodDescriptor(
    '/CoreCriminalisticsService/deleteEvidenceById',
    grpcWeb.MethodType.UNARY,
    util_pb.StringParam,
    util_pb.FailSuccessResponse,
    (request: util_pb.StringParam) => {
      return request.serializeBinary();
    },
    util_pb.FailSuccessResponse.deserializeBinary
  );

  deleteEvidenceById(
    request: util_pb.StringParam,
    metadata?: grpcWeb.Metadata | null): Promise<util_pb.FailSuccessResponse>;

  deleteEvidenceById(
    request: util_pb.StringParam,
    metadata: grpcWeb.Metadata | null,
    callback: (err: grpcWeb.RpcError,
               response: util_pb.FailSuccessResponse) => void): grpcWeb.ClientReadableStream<util_pb.FailSuccessResponse>;

  deleteEvidenceById(
    request: util_pb.StringParam,
    metadata?: grpcWeb.Metadata | null,
    callback?: (err: grpcWeb.RpcError,
               response: util_pb.FailSuccessResponse) => void) {
    if (callback !== undefined) {
      return this.client_.rpcCall(
        this.hostname_ +
          '/CoreCriminalisticsService/deleteEvidenceById',
        request,
        metadata || {},
        this.methodDescriptordeleteEvidenceById,
        callback);
    }
    return this.client_.unaryCall(
    this.hostname_ +
      '/CoreCriminalisticsService/deleteEvidenceById',
    request,
    metadata || {},
    this.methodDescriptordeleteEvidenceById);
  }

  methodDescriptordeleteCaseById = new grpcWeb.MethodDescriptor(
    '/CoreCriminalisticsService/deleteCaseById',
    grpcWeb.MethodType.UNARY,
    util_pb.StringParam,
    util_pb.FailSuccessResponse,
    (request: util_pb.StringParam) => {
      return request.serializeBinary();
    },
    util_pb.FailSuccessResponse.deserializeBinary
  );

  deleteCaseById(
    request: util_pb.StringParam,
    metadata?: grpcWeb.Metadata | null): Promise<util_pb.FailSuccessResponse>;

  deleteCaseById(
    request: util_pb.StringParam,
    metadata: grpcWeb.Metadata | null,
    callback: (err: grpcWeb.RpcError,
               response: util_pb.FailSuccessResponse) => void): grpcWeb.ClientReadableStream<util_pb.FailSuccessResponse>;

  deleteCaseById(
    request: util_pb.StringParam,
    metadata?: grpcWeb.Metadata | null,
    callback?: (err: grpcWeb.RpcError,
               response: util_pb.FailSuccessResponse) => void) {
    if (callback !== undefined) {
      return this.client_.rpcCall(
        this.hostname_ +
          '/CoreCriminalisticsService/deleteCaseById',
        request,
        metadata || {},
        this.methodDescriptordeleteCaseById,
        callback);
    }
    return this.client_.unaryCall(
    this.hostname_ +
      '/CoreCriminalisticsService/deleteCaseById',
    request,
    metadata || {},
    this.methodDescriptordeleteCaseById);
  }

  methodDescriptordeleteAllCaseLocationsById = new grpcWeb.MethodDescriptor(
    '/CoreCriminalisticsService/deleteAllCaseLocationsById',
    grpcWeb.MethodType.UNARY,
    util_pb.StringParam,
    util_pb.FailSuccessResponse,
    (request: util_pb.StringParam) => {
      return request.serializeBinary();
    },
    util_pb.FailSuccessResponse.deserializeBinary
  );

  deleteAllCaseLocationsById(
    request: util_pb.StringParam,
    metadata?: grpcWeb.Metadata | null): Promise<util_pb.FailSuccessResponse>;

  deleteAllCaseLocationsById(
    request: util_pb.StringParam,
    metadata: grpcWeb.Metadata | null,
    callback: (err: grpcWeb.RpcError,
               response: util_pb.FailSuccessResponse) => void): grpcWeb.ClientReadableStream<util_pb.FailSuccessResponse>;

  deleteAllCaseLocationsById(
    request: util_pb.StringParam,
    metadata?: grpcWeb.Metadata | null,
    callback?: (err: grpcWeb.RpcError,
               response: util_pb.FailSuccessResponse) => void) {
    if (callback !== undefined) {
      return this.client_.rpcCall(
        this.hostname_ +
          '/CoreCriminalisticsService/deleteAllCaseLocationsById',
        request,
        metadata || {},
        this.methodDescriptordeleteAllCaseLocationsById,
        callback);
    }
    return this.client_.unaryCall(
    this.hostname_ +
      '/CoreCriminalisticsService/deleteAllCaseLocationsById',
    request,
    metadata || {},
    this.methodDescriptordeleteAllCaseLocationsById);
  }

  methodDescriptordeleteAllRelatedCasesById = new grpcWeb.MethodDescriptor(
    '/CoreCriminalisticsService/deleteAllRelatedCasesById',
    grpcWeb.MethodType.UNARY,
    util_pb.StringParam,
    util_pb.FailSuccessResponse,
    (request: util_pb.StringParam) => {
      return request.serializeBinary();
    },
    util_pb.FailSuccessResponse.deserializeBinary
  );

  deleteAllRelatedCasesById(
    request: util_pb.StringParam,
    metadata?: grpcWeb.Metadata | null): Promise<util_pb.FailSuccessResponse>;

  deleteAllRelatedCasesById(
    request: util_pb.StringParam,
    metadata: grpcWeb.Metadata | null,
    callback: (err: grpcWeb.RpcError,
               response: util_pb.FailSuccessResponse) => void): grpcWeb.ClientReadableStream<util_pb.FailSuccessResponse>;

  deleteAllRelatedCasesById(
    request: util_pb.StringParam,
    metadata?: grpcWeb.Metadata | null,
    callback?: (err: grpcWeb.RpcError,
               response: util_pb.FailSuccessResponse) => void) {
    if (callback !== undefined) {
      return this.client_.rpcCall(
        this.hostname_ +
          '/CoreCriminalisticsService/deleteAllRelatedCasesById',
        request,
        metadata || {},
        this.methodDescriptordeleteAllRelatedCasesById,
        callback);
    }
    return this.client_.unaryCall(
    this.hostname_ +
      '/CoreCriminalisticsService/deleteAllRelatedCasesById',
    request,
    metadata || {},
    this.methodDescriptordeleteAllRelatedCasesById);
  }

  methodDescriptordeleteAllRelatedSubjectsById = new grpcWeb.MethodDescriptor(
    '/CoreCriminalisticsService/deleteAllRelatedSubjectsById',
    grpcWeb.MethodType.UNARY,
    util_pb.StringParam,
    util_pb.FailSuccessResponse,
    (request: util_pb.StringParam) => {
      return request.serializeBinary();
    },
    util_pb.FailSuccessResponse.deserializeBinary
  );

  deleteAllRelatedSubjectsById(
    request: util_pb.StringParam,
    metadata?: grpcWeb.Metadata | null): Promise<util_pb.FailSuccessResponse>;

  deleteAllRelatedSubjectsById(
    request: util_pb.StringParam,
    metadata: grpcWeb.Metadata | null,
    callback: (err: grpcWeb.RpcError,
               response: util_pb.FailSuccessResponse) => void): grpcWeb.ClientReadableStream<util_pb.FailSuccessResponse>;

  deleteAllRelatedSubjectsById(
    request: util_pb.StringParam,
    metadata?: grpcWeb.Metadata | null,
    callback?: (err: grpcWeb.RpcError,
               response: util_pb.FailSuccessResponse) => void) {
    if (callback !== undefined) {
      return this.client_.rpcCall(
        this.hostname_ +
          '/CoreCriminalisticsService/deleteAllRelatedSubjectsById',
        request,
        metadata || {},
        this.methodDescriptordeleteAllRelatedSubjectsById,
        callback);
    }
    return this.client_.unaryCall(
    this.hostname_ +
      '/CoreCriminalisticsService/deleteAllRelatedSubjectsById',
    request,
    metadata || {},
    this.methodDescriptordeleteAllRelatedSubjectsById);
  }

  methodDescriptorupdateCaseGeneralInfo = new grpcWeb.MethodDescriptor(
    '/CoreCriminalisticsService/updateCaseGeneralInfo',
    grpcWeb.MethodType.UNARY,
    criminalistics_criminalistics_pb.CaseGeneralInfoGrpcModel,
    criminalistics_criminalistics_pb.CriminalisticCaseGeneralInfoResponse,
    (request: criminalistics_criminalistics_pb.CaseGeneralInfoGrpcModel) => {
      return request.serializeBinary();
    },
    criminalistics_criminalistics_pb.CriminalisticCaseGeneralInfoResponse.deserializeBinary
  );

  updateCaseGeneralInfo(
    request: criminalistics_criminalistics_pb.CaseGeneralInfoGrpcModel,
    metadata?: grpcWeb.Metadata | null): Promise<criminalistics_criminalistics_pb.CriminalisticCaseGeneralInfoResponse>;

  updateCaseGeneralInfo(
    request: criminalistics_criminalistics_pb.CaseGeneralInfoGrpcModel,
    metadata: grpcWeb.Metadata | null,
    callback: (err: grpcWeb.RpcError,
               response: criminalistics_criminalistics_pb.CriminalisticCaseGeneralInfoResponse) => void): grpcWeb.ClientReadableStream<criminalistics_criminalistics_pb.CriminalisticCaseGeneralInfoResponse>;

  updateCaseGeneralInfo(
    request: criminalistics_criminalistics_pb.CaseGeneralInfoGrpcModel,
    metadata?: grpcWeb.Metadata | null,
    callback?: (err: grpcWeb.RpcError,
               response: criminalistics_criminalistics_pb.CriminalisticCaseGeneralInfoResponse) => void) {
    if (callback !== undefined) {
      return this.client_.rpcCall(
        this.hostname_ +
          '/CoreCriminalisticsService/updateCaseGeneralInfo',
        request,
        metadata || {},
        this.methodDescriptorupdateCaseGeneralInfo,
        callback);
    }
    return this.client_.unaryCall(
    this.hostname_ +
      '/CoreCriminalisticsService/updateCaseGeneralInfo',
    request,
    metadata || {},
    this.methodDescriptorupdateCaseGeneralInfo);
  }

  methodDescriptorupdateCaseDetails = new grpcWeb.MethodDescriptor(
    '/CoreCriminalisticsService/updateCaseDetails',
    grpcWeb.MethodType.UNARY,
    criminalistics_criminalistics_pb.ArrayOfCaseDetailGrpcModel,
    criminalistics_criminalistics_pb.CriminalisticCaseDetailResponse,
    (request: criminalistics_criminalistics_pb.ArrayOfCaseDetailGrpcModel) => {
      return request.serializeBinary();
    },
    criminalistics_criminalistics_pb.CriminalisticCaseDetailResponse.deserializeBinary
  );

  updateCaseDetails(
    request: criminalistics_criminalistics_pb.ArrayOfCaseDetailGrpcModel,
    metadata?: grpcWeb.Metadata | null): Promise<criminalistics_criminalistics_pb.CriminalisticCaseDetailResponse>;

  updateCaseDetails(
    request: criminalistics_criminalistics_pb.ArrayOfCaseDetailGrpcModel,
    metadata: grpcWeb.Metadata | null,
    callback: (err: grpcWeb.RpcError,
               response: criminalistics_criminalistics_pb.CriminalisticCaseDetailResponse) => void): grpcWeb.ClientReadableStream<criminalistics_criminalistics_pb.CriminalisticCaseDetailResponse>;

  updateCaseDetails(
    request: criminalistics_criminalistics_pb.ArrayOfCaseDetailGrpcModel,
    metadata?: grpcWeb.Metadata | null,
    callback?: (err: grpcWeb.RpcError,
               response: criminalistics_criminalistics_pb.CriminalisticCaseDetailResponse) => void) {
    if (callback !== undefined) {
      return this.client_.rpcCall(
        this.hostname_ +
          '/CoreCriminalisticsService/updateCaseDetails',
        request,
        metadata || {},
        this.methodDescriptorupdateCaseDetails,
        callback);
    }
    return this.client_.unaryCall(
    this.hostname_ +
      '/CoreCriminalisticsService/updateCaseDetails',
    request,
    metadata || {},
    this.methodDescriptorupdateCaseDetails);
  }

  methodDescriptorupdateRelatedCases = new grpcWeb.MethodDescriptor(
    '/CoreCriminalisticsService/updateRelatedCases',
    grpcWeb.MethodType.UNARY,
    criminalistics_criminalistics_pb.ArrayOfRelatedCaseGrpcModel,
    criminalistics_criminalistics_pb.CriminalisticRelatedCasesResponse,
    (request: criminalistics_criminalistics_pb.ArrayOfRelatedCaseGrpcModel) => {
      return request.serializeBinary();
    },
    criminalistics_criminalistics_pb.CriminalisticRelatedCasesResponse.deserializeBinary
  );

  updateRelatedCases(
    request: criminalistics_criminalistics_pb.ArrayOfRelatedCaseGrpcModel,
    metadata?: grpcWeb.Metadata | null): Promise<criminalistics_criminalistics_pb.CriminalisticRelatedCasesResponse>;

  updateRelatedCases(
    request: criminalistics_criminalistics_pb.ArrayOfRelatedCaseGrpcModel,
    metadata: grpcWeb.Metadata | null,
    callback: (err: grpcWeb.RpcError,
               response: criminalistics_criminalistics_pb.CriminalisticRelatedCasesResponse) => void): grpcWeb.ClientReadableStream<criminalistics_criminalistics_pb.CriminalisticRelatedCasesResponse>;

  updateRelatedCases(
    request: criminalistics_criminalistics_pb.ArrayOfRelatedCaseGrpcModel,
    metadata?: grpcWeb.Metadata | null,
    callback?: (err: grpcWeb.RpcError,
               response: criminalistics_criminalistics_pb.CriminalisticRelatedCasesResponse) => void) {
    if (callback !== undefined) {
      return this.client_.rpcCall(
        this.hostname_ +
          '/CoreCriminalisticsService/updateRelatedCases',
        request,
        metadata || {},
        this.methodDescriptorupdateRelatedCases,
        callback);
    }
    return this.client_.unaryCall(
    this.hostname_ +
      '/CoreCriminalisticsService/updateRelatedCases',
    request,
    metadata || {},
    this.methodDescriptorupdateRelatedCases);
  }

  methodDescriptorupdateRelatedSubjects = new grpcWeb.MethodDescriptor(
    '/CoreCriminalisticsService/updateRelatedSubjects',
    grpcWeb.MethodType.UNARY,
    criminalistics_criminalistics_pb.ArrayOfSubjectRelatedCaseGrpcModel,
    criminalistics_criminalistics_pb.CriminalisticSubjectRelatedCasesResponse,
    (request: criminalistics_criminalistics_pb.ArrayOfSubjectRelatedCaseGrpcModel) => {
      return request.serializeBinary();
    },
    criminalistics_criminalistics_pb.CriminalisticSubjectRelatedCasesResponse.deserializeBinary
  );

  updateRelatedSubjects(
    request: criminalistics_criminalistics_pb.ArrayOfSubjectRelatedCaseGrpcModel,
    metadata?: grpcWeb.Metadata | null): Promise<criminalistics_criminalistics_pb.CriminalisticSubjectRelatedCasesResponse>;

  updateRelatedSubjects(
    request: criminalistics_criminalistics_pb.ArrayOfSubjectRelatedCaseGrpcModel,
    metadata: grpcWeb.Metadata | null,
    callback: (err: grpcWeb.RpcError,
               response: criminalistics_criminalistics_pb.CriminalisticSubjectRelatedCasesResponse) => void): grpcWeb.ClientReadableStream<criminalistics_criminalistics_pb.CriminalisticSubjectRelatedCasesResponse>;

  updateRelatedSubjects(
    request: criminalistics_criminalistics_pb.ArrayOfSubjectRelatedCaseGrpcModel,
    metadata?: grpcWeb.Metadata | null,
    callback?: (err: grpcWeb.RpcError,
               response: criminalistics_criminalistics_pb.CriminalisticSubjectRelatedCasesResponse) => void) {
    if (callback !== undefined) {
      return this.client_.rpcCall(
        this.hostname_ +
          '/CoreCriminalisticsService/updateRelatedSubjects',
        request,
        metadata || {},
        this.methodDescriptorupdateRelatedSubjects,
        callback);
    }
    return this.client_.unaryCall(
    this.hostname_ +
      '/CoreCriminalisticsService/updateRelatedSubjects',
    request,
    metadata || {},
    this.methodDescriptorupdateRelatedSubjects);
  }

  methodDescriptorupdateCaseLocations = new grpcWeb.MethodDescriptor(
    '/CoreCriminalisticsService/updateCaseLocations',
    grpcWeb.MethodType.UNARY,
    criminalistics_criminalistics_pb.ArrayOfCaseLocationGrpcModel,
    criminalistics_criminalistics_pb.CriminalisticLocationCasesResponse,
    (request: criminalistics_criminalistics_pb.ArrayOfCaseLocationGrpcModel) => {
      return request.serializeBinary();
    },
    criminalistics_criminalistics_pb.CriminalisticLocationCasesResponse.deserializeBinary
  );

  updateCaseLocations(
    request: criminalistics_criminalistics_pb.ArrayOfCaseLocationGrpcModel,
    metadata?: grpcWeb.Metadata | null): Promise<criminalistics_criminalistics_pb.CriminalisticLocationCasesResponse>;

  updateCaseLocations(
    request: criminalistics_criminalistics_pb.ArrayOfCaseLocationGrpcModel,
    metadata: grpcWeb.Metadata | null,
    callback: (err: grpcWeb.RpcError,
               response: criminalistics_criminalistics_pb.CriminalisticLocationCasesResponse) => void): grpcWeb.ClientReadableStream<criminalistics_criminalistics_pb.CriminalisticLocationCasesResponse>;

  updateCaseLocations(
    request: criminalistics_criminalistics_pb.ArrayOfCaseLocationGrpcModel,
    metadata?: grpcWeb.Metadata | null,
    callback?: (err: grpcWeb.RpcError,
               response: criminalistics_criminalistics_pb.CriminalisticLocationCasesResponse) => void) {
    if (callback !== undefined) {
      return this.client_.rpcCall(
        this.hostname_ +
          '/CoreCriminalisticsService/updateCaseLocations',
        request,
        metadata || {},
        this.methodDescriptorupdateCaseLocations,
        callback);
    }
    return this.client_.unaryCall(
    this.hostname_ +
      '/CoreCriminalisticsService/updateCaseLocations',
    request,
    metadata || {},
    this.methodDescriptorupdateCaseLocations);
  }

  methodDescriptorcreateComment = new grpcWeb.MethodDescriptor(
    '/CoreCriminalisticsService/createComment',
    grpcWeb.MethodType.UNARY,
    criminalistics_criminalistics_pb.CaseCommentGrpcModel,
    criminalistics_criminalistics_pb.CriminalisticCaseCommentResponse,
    (request: criminalistics_criminalistics_pb.CaseCommentGrpcModel) => {
      return request.serializeBinary();
    },
    criminalistics_criminalistics_pb.CriminalisticCaseCommentResponse.deserializeBinary
  );

  createComment(
    request: criminalistics_criminalistics_pb.CaseCommentGrpcModel,
    metadata?: grpcWeb.Metadata | null): Promise<criminalistics_criminalistics_pb.CriminalisticCaseCommentResponse>;

  createComment(
    request: criminalistics_criminalistics_pb.CaseCommentGrpcModel,
    metadata: grpcWeb.Metadata | null,
    callback: (err: grpcWeb.RpcError,
               response: criminalistics_criminalistics_pb.CriminalisticCaseCommentResponse) => void): grpcWeb.ClientReadableStream<criminalistics_criminalistics_pb.CriminalisticCaseCommentResponse>;

  createComment(
    request: criminalistics_criminalistics_pb.CaseCommentGrpcModel,
    metadata?: grpcWeb.Metadata | null,
    callback?: (err: grpcWeb.RpcError,
               response: criminalistics_criminalistics_pb.CriminalisticCaseCommentResponse) => void) {
    if (callback !== undefined) {
      return this.client_.rpcCall(
        this.hostname_ +
          '/CoreCriminalisticsService/createComment',
        request,
        metadata || {},
        this.methodDescriptorcreateComment,
        callback);
    }
    return this.client_.unaryCall(
    this.hostname_ +
      '/CoreCriminalisticsService/createComment',
    request,
    metadata || {},
    this.methodDescriptorcreateComment);
  }

  methodDescriptorgetAllCommentsByCaseId = new grpcWeb.MethodDescriptor(
    '/CoreCriminalisticsService/getAllCommentsByCaseId',
    grpcWeb.MethodType.SERVER_STREAMING,
    util_pb.StringParam,
    criminalistics_criminalistics_pb.CaseCommentGrpcModel,
    (request: util_pb.StringParam) => {
      return request.serializeBinary();
    },
    criminalistics_criminalistics_pb.CaseCommentGrpcModel.deserializeBinary
  );

  getAllCommentsByCaseId(
    request: util_pb.StringParam,
    metadata?: grpcWeb.Metadata): grpcWeb.ClientReadableStream<criminalistics_criminalistics_pb.CaseCommentGrpcModel> {
    return this.client_.serverStreaming(
      this.hostname_ +
        '/CoreCriminalisticsService/getAllCommentsByCaseId',
      request,
      metadata || {},
      this.methodDescriptorgetAllCommentsByCaseId);
  }

  methodDescriptorcreateCoincidence = new grpcWeb.MethodDescriptor(
    '/CoreCriminalisticsService/createCoincidence',
    grpcWeb.MethodType.UNARY,
    criminalistics_criminalistics_pb.CaseCoincidenceGrpcModel,
    criminalistics_criminalistics_pb.CriminalisticCaseCoincidenceResponse,
    (request: criminalistics_criminalistics_pb.CaseCoincidenceGrpcModel) => {
      return request.serializeBinary();
    },
    criminalistics_criminalistics_pb.CriminalisticCaseCoincidenceResponse.deserializeBinary
  );

  createCoincidence(
    request: criminalistics_criminalistics_pb.CaseCoincidenceGrpcModel,
    metadata?: grpcWeb.Metadata | null): Promise<criminalistics_criminalistics_pb.CriminalisticCaseCoincidenceResponse>;

  createCoincidence(
    request: criminalistics_criminalistics_pb.CaseCoincidenceGrpcModel,
    metadata: grpcWeb.Metadata | null,
    callback: (err: grpcWeb.RpcError,
               response: criminalistics_criminalistics_pb.CriminalisticCaseCoincidenceResponse) => void): grpcWeb.ClientReadableStream<criminalistics_criminalistics_pb.CriminalisticCaseCoincidenceResponse>;

  createCoincidence(
    request: criminalistics_criminalistics_pb.CaseCoincidenceGrpcModel,
    metadata?: grpcWeb.Metadata | null,
    callback?: (err: grpcWeb.RpcError,
               response: criminalistics_criminalistics_pb.CriminalisticCaseCoincidenceResponse) => void) {
    if (callback !== undefined) {
      return this.client_.rpcCall(
        this.hostname_ +
          '/CoreCriminalisticsService/createCoincidence',
        request,
        metadata || {},
        this.methodDescriptorcreateCoincidence,
        callback);
    }
    return this.client_.unaryCall(
    this.hostname_ +
      '/CoreCriminalisticsService/createCoincidence',
    request,
    metadata || {},
    this.methodDescriptorcreateCoincidence);
  }

  methodDescriptorupdateCoincidence = new grpcWeb.MethodDescriptor(
    '/CoreCriminalisticsService/updateCoincidence',
    grpcWeb.MethodType.UNARY,
    criminalistics_criminalistics_pb.CaseCoincidenceGrpcModel,
    criminalistics_criminalistics_pb.CriminalisticCaseCoincidenceResponse,
    (request: criminalistics_criminalistics_pb.CaseCoincidenceGrpcModel) => {
      return request.serializeBinary();
    },
    criminalistics_criminalistics_pb.CriminalisticCaseCoincidenceResponse.deserializeBinary
  );

  updateCoincidence(
    request: criminalistics_criminalistics_pb.CaseCoincidenceGrpcModel,
    metadata?: grpcWeb.Metadata | null): Promise<criminalistics_criminalistics_pb.CriminalisticCaseCoincidenceResponse>;

  updateCoincidence(
    request: criminalistics_criminalistics_pb.CaseCoincidenceGrpcModel,
    metadata: grpcWeb.Metadata | null,
    callback: (err: grpcWeb.RpcError,
               response: criminalistics_criminalistics_pb.CriminalisticCaseCoincidenceResponse) => void): grpcWeb.ClientReadableStream<criminalistics_criminalistics_pb.CriminalisticCaseCoincidenceResponse>;

  updateCoincidence(
    request: criminalistics_criminalistics_pb.CaseCoincidenceGrpcModel,
    metadata?: grpcWeb.Metadata | null,
    callback?: (err: grpcWeb.RpcError,
               response: criminalistics_criminalistics_pb.CriminalisticCaseCoincidenceResponse) => void) {
    if (callback !== undefined) {
      return this.client_.rpcCall(
        this.hostname_ +
          '/CoreCriminalisticsService/updateCoincidence',
        request,
        metadata || {},
        this.methodDescriptorupdateCoincidence,
        callback);
    }
    return this.client_.unaryCall(
    this.hostname_ +
      '/CoreCriminalisticsService/updateCoincidence',
    request,
    metadata || {},
    this.methodDescriptorupdateCoincidence);
  }

  methodDescriptordeleteCoincidenceById = new grpcWeb.MethodDescriptor(
    '/CoreCriminalisticsService/deleteCoincidenceById',
    grpcWeb.MethodType.UNARY,
    util_pb.StringParam,
    util_pb.FailSuccessResponse,
    (request: util_pb.StringParam) => {
      return request.serializeBinary();
    },
    util_pb.FailSuccessResponse.deserializeBinary
  );

  deleteCoincidenceById(
    request: util_pb.StringParam,
    metadata?: grpcWeb.Metadata | null): Promise<util_pb.FailSuccessResponse>;

  deleteCoincidenceById(
    request: util_pb.StringParam,
    metadata: grpcWeb.Metadata | null,
    callback: (err: grpcWeb.RpcError,
               response: util_pb.FailSuccessResponse) => void): grpcWeb.ClientReadableStream<util_pb.FailSuccessResponse>;

  deleteCoincidenceById(
    request: util_pb.StringParam,
    metadata?: grpcWeb.Metadata | null,
    callback?: (err: grpcWeb.RpcError,
               response: util_pb.FailSuccessResponse) => void) {
    if (callback !== undefined) {
      return this.client_.rpcCall(
        this.hostname_ +
          '/CoreCriminalisticsService/deleteCoincidenceById',
        request,
        metadata || {},
        this.methodDescriptordeleteCoincidenceById,
        callback);
    }
    return this.client_.unaryCall(
    this.hostname_ +
      '/CoreCriminalisticsService/deleteCoincidenceById',
    request,
    metadata || {},
    this.methodDescriptordeleteCoincidenceById);
  }

  methodDescriptorgetAllCoincidencesByCaseId = new grpcWeb.MethodDescriptor(
    '/CoreCriminalisticsService/getAllCoincidencesByCaseId',
    grpcWeb.MethodType.SERVER_STREAMING,
    util_pb.StringParam,
    criminalistics_criminalistics_pb.CaseCoincidenceGrpcModel,
    (request: util_pb.StringParam) => {
      return request.serializeBinary();
    },
    criminalistics_criminalistics_pb.CaseCoincidenceGrpcModel.deserializeBinary
  );

  getAllCoincidencesByCaseId(
    request: util_pb.StringParam,
    metadata?: grpcWeb.Metadata): grpcWeb.ClientReadableStream<criminalistics_criminalistics_pb.CaseCoincidenceGrpcModel> {
    return this.client_.serverStreaming(
      this.hostname_ +
        '/CoreCriminalisticsService/getAllCoincidencesByCaseId',
      request,
      metadata || {},
      this.methodDescriptorgetAllCoincidencesByCaseId);
  }

}

