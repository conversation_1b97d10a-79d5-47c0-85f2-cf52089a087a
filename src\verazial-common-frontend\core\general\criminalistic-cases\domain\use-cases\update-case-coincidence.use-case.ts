import {UseCaseGrpc} from "src/verazial-common-frontend/core/use-case-grpc";
import {
    CaseRepository
} from "src/verazial-common-frontend/core/general/criminalistic-cases/domain/repository/case.repository";
import {
    CaseCoincidenceEntity
} from "src/verazial-common-frontend/core/general/criminalistic-cases/domain/entity/caseCoincidence.entity";


export class UpdateCaseCoincidenceUseCase implements UseCaseGrpc<CaseCoincidenceEntity, CaseCoincidenceEntity> {
    constructor(private caseRepository: CaseRepository) {
    }

    execute(params: CaseCoincidenceEntity): Promise<CaseCoincidenceEntity> {
        return this.caseRepository.updateCaseCoincidence(params);
    }
}