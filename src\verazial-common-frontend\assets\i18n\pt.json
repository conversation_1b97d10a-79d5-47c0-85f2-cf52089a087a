{"locale": "pt-PT", "add_application": "Adicionar aplicativo", "add_window": "<PERSON><PERSON><PERSON><PERSON>", "dateFormat": "dd/mm/yy", "today": "Hoje", "dateFormatLong": "dd/MM/yyyy", "dateTimeFormat": "dd/MM/yyyy HH:mm:ss", "timeOnlyFormat": "HH:mm:ss", "hourFormat": "24", "username": "Nome de usuário", "password": "<PERSON><PERSON>", "currentPassword": "<PERSON><PERSON> atual", "newPassword": "Nova senha", "confirmNewPassword": "Confirme a nova senha", "setupNewAdminUser": "Novo Administrador", "updatePassword": "<PERSON><PERSON><PERSON><PERSON>", "resetPassword": "<PERSON><PERSON><PERSON><PERSON>", "versions": "Versõ<PERSON>", "save": "<PERSON><PERSON>", "add": "<PERSON><PERSON><PERSON><PERSON>", "confirm": "Confirme", "continue": "<PERSON><PERSON><PERSON><PERSON>", "accept": "Aceitar", "clear": "Limpar", "cancel": "<PERSON><PERSON><PERSON>", "close": "<PERSON><PERSON><PERSON>", "search_coincidences": "Busca Coincidências", "view_full_profile": "Acessar perfil completo", "update": "<PERSON><PERSON><PERSON><PERSON>", "delete": "Excluir", "delete_entity": "Excluir", "edit_entity": "Editar Entidade", "edit": "<PERSON><PERSON>", "view_data": "Ver dados", "create_new_user": "Novo Usuário", "create_new_subject": "Nov<PERSON> Su<PERSON>ito", "create_new_case": "Novo Processo", "createRecord": "<PERSON><PERSON><PERSON>", "auditTrail": "auditTrail", "next": "Próximo", "after_searching_by_id": "Após Busca por ID, Permitir", "disconnect": "Desconectar", "manageProfile": " Gerenciar Perfil", "logout": " <PERSON><PERSON>", "spanish": "Espanhol", "english": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "portuguese": "Português", "yes": "<PERSON>m", "no": "Não", "publish": "Publicar", "back": "Regressar", "created_at": "C<PERSON><PERSON> em", "updated_at": "Atualizado em", "profilePicture": "Foto de Perfil", "searchInRecords": "Pesquisar em Registros", "searchInUsers": "Pesquisar em Usuários", "biographic": "Biográfico", "biometric": "Biométrico", "prisons": "Pris<PERSON><PERSON>", "history": "História", "actions": "Ações", "flowAssignment": "Atribuição de Fluxo", "flows": "Fluxos", "profiles": "<PERSON><PERSON><PERSON>", "change_view": "Alterar Visualização", "verify": "Verificar", "identify": "Identificar", "verify_identity": "Verificar Identidade", "verify_user": "Verificar Usuário", "datePlaceholder": "dd/mm/aaaa", "ADMIN": "ADMIN", "ENROLLER": "ENROLLER", "clearFilters": "Clear filters", "update_password": "<PERSON><PERSON><PERSON><PERSON>", "remove": "Remover", "confirmation": "Confirmação", "subject": {"new_subject": "Nov<PERSON> Su<PERSON>ito", "no_subjects_available": "Nenhum sujeito disponível"}, "buttons": {"save": "<PERSON><PERSON>", "show": "Mostrar", "logout": "Desconectar", "add_option": "Adicionar <PERSON>", "add_relation": "Adicionar <PERSON>", "add_segment": "Adicionar segmento", "add_location": "Adicionar localização", "add_device": "Adicionar dispositivo", "new": "Novo", "cancel": "<PERSON><PERSON><PERSON>", "yes": "<PERSON>m", "no": "Não", "sign_in": "Entrar", "verify": "Verificar", "apply": "Aplicar", "continue": "<PERSON><PERSON><PERSON><PERSON>", "add": "<PERSON><PERSON><PERSON><PERSON>", "new_license": "Nova licença", "new_api_connection": "Nova conexão API", "add_method": "<PERSON><PERSON><PERSON><PERSON>", "next": "Próximo", "back": "Voltar", "update": "<PERSON><PERSON><PERSON><PERSON>", "new_ldap_connection": "Nova conexão LDAP", "return": "Retornar", "add_data_source": "<PERSON><PERSON><PERSON><PERSON>", "add_parameters": "Adiciona<PERSON>", "show_data_sources": "Mostrar Fonte Dados", "show_parameters": "Mostrar <PERSON>", "show_application": "Mostrar Aplicativos", "show_windows": "<PERSON><PERSON>", "change_password": "Alterar a senha", "reload": "Recarrega", "update_credentials": "<PERSON><PERSON><PERSON><PERSON> creden<PERSON>is", "new_application": "Nova aplicação", "publish": "Publicar", "unpublish": "Cancelar publicação", "new_window": "Nova janela", "new_component": "Novo componente", "send": "Enviar", "new_datasource": "Nova fonte de dados", "add_parameter": "Adicionar <PERSON>", "returnHome": "Retornar à página inicial", "sendPasswordRecovery": "Enviar recuperação de senha", "add_location_table": "Adicionar localização", "close": "<PERSON><PERSON><PERSON>"}, "menu": {"locations": "Localizações", "subject_locations": "Localizações do Sujeito", "main": "Principal", "home": "Início", "search": "<PERSON><PERSON><PERSON><PERSON>", "subject": "<PERSON><PERSON><PERSON>", "subjects": "<PERSON><PERSON><PERSON><PERSON>", "enroll": "Inscrição", "new_subject": "Nov<PERSON> Su<PERSON>ito", "administration": "Administração", "tenants": "Tenants", "roles": "Roles", "profile_role": "Perfis/Funções", "users": "Usuários", "process": "Processo", "clock": "Clock", "flows": "Fluxos", "categories": "Categorias", "assignments": "Atribuições", "time_records": "Registros de tempo", "audit_trail": "Trilha de auditoria", "pass": "Pass", "application_datasource": "Fonte de Dados", "application_flows": "Processos de Aplicação", "application_assign": "Atribuição de aplicação", "application_settings": "Application Settings", "versions": "Versions", "admin_users": "User", "config_role": "Config. Role", "config_admin": "ID Admin", "config_enroll": "ID Enroll", "config_prisons": "ID Prisons", "config_widget": "ID Widget", "config_clock": "ID Clock", "config_pass": "ID Pass", "config_manager": "ID Manager", "config_home": "<PERSON><PERSON>o", "config_reports": "ID Reports", "config_general": "G<PERSON><PERSON>", "configuration": "Configuração", "datasources": "Fontes de dados", "applications": "Aplicativos", "my_applications": "Os meus aplicativos", "reports": "Analítica", "reports_general": "General", "reports_users": "Usuários", "reports_subjects": "<PERSON><PERSON><PERSON><PERSON>", "reports_verification": "Verificação", "reports_identification": "Identificação", "reports_performance": "<PERSON><PERSON><PERSON><PERSON>", "reports_actions": "A<PERSON>s", "reports_audit_trail": "Audit trail", "prisons": "Prisons", "prisons_visits": "Visitas", "prisons_schedules": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "prisons_transfers": "Transferências", "criminalistics": "Forense", "criminalistics_reports": "Relatórios", "criminalistics_cases": "<PERSON>as<PERSON>", "fingerprint_form": "Formulário de Impressão Digital"}, "divider": {"new_action": "Nova Ação", "or_continue_with": "ou continuar com", "new_reason": "Novo motivo", "new_profile": "Novo perfil", "new_segment": "Novo segmento", "matcher": "Matcher", "edit_licenses": "<PERSON>ar lice<PERSON>", "new_location": "Nova localização", "new_device": "Nova dispositivo", "credentials": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "new_field": "Novo campo", "add_fields": "<PERSON><PERSON><PERSON><PERSON> camp<PERSON>", "new_theme": "Novo tema", "connection": "Conexão", "default_datasource": "Origem de dados por padrão", "new_method": "Novo método", "new_license": "Nova licença", "add_catalog": "Adicionar catálogo", "new_app_flow_type": "Novo tipo de fluxo"}, "loginForm": {"enterEmail": "Digite o e-mail", "enterUsername": "Digite o nome de usuário", "enterPassword": "Digite a senha", "enterNumID": "Digite o número de ID", "loginOptions": "Opções de login", "userNotFound": "Usuário não encontrado", "wrongUsername": "Nome de usuário incorreto", "wrongPassword": "Senha incorreta", "wrongCredentials": "Credenciais incorretas", "adminBioLabel": "ou continuar com", "subjectBioLabel": "Verifique sua identidade", "signIn": "Entrar", "back_to_login": "Voltar ao login"}, "table": {"num_id": "Número de ID", "numId": "Número de ID", "profile": "Perfil", "main_profile": "<PERSON><PERSON><PERSON> Principal", "profiles": "<PERSON><PERSON><PERSON>", "role": "Função", "main_role": "Função Principal", "verification": "1:1 Verificação", "segmented_search": "1:n Pesquisa Segmentada", "roles": "Funções", "username": "Nome de Usuário", "first_name": "Primeiro Nome", "second_name": "<PERSON><PERSON><PERSON>", "last_name": "Sobrenome", "second_last_name": "<PERSON><PERSON><PERSON>", "names": "Nomes", "lastNames": "Sobrenomes", "birthdate": "Data de Nascimento", "gender": "<PERSON><PERSON><PERSON><PERSON>", "language": "Idioma", "datasource": "Fonte de dados", "actions": "Ações", "flowAssignment": "Atribuição de Fluxo", "last_action": "Última Ação", "reasonUpdate": "Motivo da atualização", "reasonDelete": "Motivo da retirada", "reasonCreate": "Motivo da criação", "reasonConfig": "Motivo da atualização", "center": "Centro", "especific_location": "Localização Específica", "present/absent": "Presente/Ausente (hh:mm:ss)", "present": "Presente", "absent": "Ausente", "transfer": "Transferir", "lastActions": "Últimas Ações", "assignedTo": "Atribuído a", "caseNumber": "Número de processo", "name": "Nome", "createdBy": "<PERSON><PERSON><PERSON> por", "updatedBy": "Atualizado por", "createdAt": "C<PERSON><PERSON> em", "updatedAt": "Atualizado em", "openDate": "Aberto em", "title": "<PERSON><PERSON><PERSON><PERSON>", "type": "Tipo", "location": "Localização", "description": "Descrição", "comments": "Comentários", "subject_profiles": "Perfis do Sujeito", "user_roles": "Funções do Usuário", "assignedType": "Asignación"}, "action_codes": {"no_device": "NO_DEV", "modify_user": "MOD_BIO", "modify_config": "MOD_CONF", "remove_subject_sample": "REM_SUB_SAM", "remove_subject_button": "REM_SUB_BT", "remove_picture": "REM_PIC", "remove_sample": "REM_SAM", "add_subject": "NEW_SUB", "add_sample": "NEW_SAM", "add_picture": "ADD_PIC", "verification_initial": "VER_INI", "verification_complementary": "VER_COM", "identification_biometrics": "ID_SAM", "identification_user_pass": "ID_PASS", "identification_otp": "ID_KEY", "identification_pin": "ID_PIN", "debug_local": "DEB_LOC", "modify_prisons": "MOD_PR"}, "prisons_tab": {"location": "Localização", "occupiedBy": "Ocupada por", "relationships": "Relações", "entry_date": "Data de Entrada", "exit_date": "Data de Saída", "init_date": "Data de Início", "end_date": "Data de Término", "reason": "Motivo de Entrada/Saída", "visited_person": "Pess<PERSON>", "visit_time": "Tempo de Visita", "is_inside": "Está Dentro da Instalação?", "cpl": "CPL", "regime": "Regime", "clasification": "Classificação", "pending": "Pendente", "actual_location": "Localização Atual", "new_location": "Assinar nova localização", "new_entry_exit": "Assinar nova entrada/saída", "definitive_entry": "Entrada final", "definitive_exit": "Saída final", "transfer_entry": "Entrada de traslado", "transfer_exit": "Saída de traslado", "entry": "Entrada", "exit": "<PERSON><PERSON><PERSON>", "select_location_entry": "Selecione a localização de entrada", "select_related_subject": "Selecione o sujeito relacionado de entrada", "select_transfer_auth": "Selecione a autorização de transferência de saida", "select_entry_and_exit_auth": "Selecione a autorização de entrada e saída"}, "actions_tab": {"action_code": "Código da Ação", "action_name": "Nome da Ação"}, "config": {"auto_delete": "Exclusão Automática de Dados Biométricos", "delete": "Excluir", "widget_options": "Opções do Widget", "url_widget": "URL DO WIDGET", "widget_match_key": "CHAVE DE CORRESPONDÊNCIA DO WIDGET", "widget_enroll_key": "CHAVE DE INSCRIÇÃO DO WIDGET", "biometricServerComponent_options": "biometricServerComponent_options", "biometricServerUrl": "URL BIOMETRIC SERVER", "biometricServerUser": "Nome de usuário", "biometricServerPassword": "<PERSON><PERSON>", "biographicServerComponent_options": "biographicServerComponent_options", "biographicServerUrl": "URL BIOGRAPHIC SERVER", "biographicServerUser": "Nome de usuário", "biographicServerPassword": "<PERSON><PERSON>", "externalBiographicServerComponent_options": "externalBiographicServerComponent_options", "externalBiographicServerUrl": "URL EXTERNAL BIOGRAPHIC SERVER", "externalBiographicServer": "Ser<PERSON>or Biográfico Externo", "externalBiographicServerUser": "Nome de usuário", "externalBiographicServerPassword": "<PERSON><PERSON>", "clockServerComponent_options": "clockServerComponent_options", "clockServerUrl": "URL CLOCK SERVER", "clockServerUser": "Nome de usuário", "clockServerPassword": "<PERSON><PERSON>", "storageServerComponent_options": "storageServerComponent_options", "storageServerUrl": "URL STORAGE SERVER", "storageServerUser": "Nome de usuário", "storageServerPassword": "<PERSON><PERSON>", "actions available": "Ações Disponíveis", "show_table": "<PERSON><PERSON>", "code": "Código", "name": "Nome", "filter_action": "Filtrar Nome da Ação", "filter_profile": "Filtrar Nome do Perfil", "close": "<PERSON><PERSON><PERSON>", "subjectTabs": "Configuração da guia do sujeito", "showAdditionalTabs": "Mostrar guias adicionais", "showPrisonTab": "Mostrar guia de p<PERSON>ão", "showProfilePictureTab": "Mostrar guia de foto de perfil", "allowAfternIdSearch": "Após a busca por ID", "allowEditAfternIdSearch": "Permitir ed<PERSON>", "allowDeleteAfterIdSearch": "Permitir exclus<PERSON>", "actionsServerComponent_options": "actionsServerComponent_options", "actionsServerUrl": "URL ACTIONS SERVER", "actionsServerUser": "Nome de usuário", "actionsServerPassword": "<PERSON><PERSON>"}, "groups": {"new_group": "novo grupo", "no_groups_available": "Nenhum grupo disponível", "group_type": "Tipo de Grupo", "configuration": "Configuração", "group": "grupo", "delete_group": "Excluir grupo", "update_group": "Atualizar grupo"}, "category": {"new_category": "Nova Categoria", "no_categories_available": "Nenhuma categoria disponível", "category_type": "Categoria", "configuration": "Configuração", "category": "Categoria", "delete_category": "Excluir categoria", "update_category": "Atualizar categoria", "list_categories": "Lista de categorias", "selected_categories": "Categorias selecionadas", "enable_date_range": "intervalo de datas"}, "flow": {"code": "Código", "name": "Nome", "description": "Descrição", "profiles": "<PERSON><PERSON><PERSON>", "showFlowActions": "Mostrar ações de fluxo", "predecessors": "Predecessores", "action": "Ação", "new_flow": "Novo fluxo", "no_flows_available": "Nenhuma transmissão disponível", "remove_flow": "Remover Fluxo", "add_action": "Adicionar ação", "actions_availables": "Ações disponíveis", "drag_drop": "Arrastrar e soltar ", "the_action": "a acção", "new_action": "Nova ação", "edit_action": "Editar <PERSON>", "attributes": "Atributos", "select_element": "Selecionar um elemento", "input": "Input", "dropdown": "Dropdown", "toggle": "Toggle", "required": "Obrigatório", "user": "<PERSON><PERSON><PERSON><PERSON>", "option": "Opção", "options": "Opções", "remove_attribute": "Remover atributo", "remove_action": "Remover ação", "save_flow": "<PERSON><PERSON>", "status": "Estado", "updated_at": "atualizado em", "published": "Publicados", "unpublished": "Remover publicação", "no_published": "Não poste", "update_flow": "Fluxo de atualização", "last_update": "Última atualização", "publish": "Publicar", "flow": "Fluxo", "button": "Botão", "is_relay": "<PERSON>", "list_flows": "Lista de fluxos", "selected_flows": "Fluxos selecionados", "min_characters": "Núm. mín. de carateres", "max_characters": "Núm. máx. de carateres", "message": "Mensagem", "no_actions": "Nenhuma ação", "severity": "Severidade"}, "assignment": {"name": "Nome", "num_flows": "Núm. fluxos", "num_categories": "Núm. categorias", "updated_at": "Atualizado em", "new_assignment": "Nova atribuição", "update_assignment": "Atualizar atri<PERSON>", "general": "Em geral", "add_flow": "<PERSON>ici<PERSON><PERSON>o", "add_category": "Adicionar categoria", "assignment": "atribuição", "delete_assignment": "Excluir atribuição", "no_assignment_available": "Nenhuma atribuição disponível", "flows": "Fluxos", "categories": "Categorias", "configuration": "Configuração", "locations": "Localizações", "subjects": "<PERSON><PERSON><PERSON><PERSON>", "schedules": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "user": {"num_id": "NÚMERO DE ID", "profile": "PERFIL", "username": "NOME DE USUÁRIO", "names": "NOMES", "lastNames": "SOBRENOMES", "first_name": "PRIMEIRO NOME", "second_name": "SEGUNDO NOME", "last_name": "SOBRENOME", "second_last_name": "SEGUNDO SOBRENOME", "birthdate": "DATA DE NASCIMENTO", "gender": "GÊNERO", "language": "IDIOMA", "location": "LOCALIZAÇÃO", "segment": "SEGMENTO", "device": "DISPOSITIVO", "no_roles_assigned": "Nenhuma função atribuída", "new_user": "Novos usuários", "no_users_available": "Nenhum usuário disponí<PERSON>."}, "signaturesTable": {"identityId": "ID da Identidade", "eventCode": "Código do Evento", "eventTimestamp": "Carimbo de data/hora do evento", "locationId": "ID da Localização", "segmentId": "ID do Segmento", "deviceId": "ID do Dispositivo", "eventLatitude": "Latitude", "eventLongitude": "Longitude", "applicationId": "ID da Aplicação", "id": "ID", "eventId": "ID do Evento", "startDate": "Data de início", "endDate": "Data de término", "eventResource": "Recurso do evento", "eventData": "Dados do evento", "executorId": "<PERSON><PERSON><PERSON><PERSON>", "receiverId": "<PERSON><PERSON><PERSON>", "receiverProfile": "Perfil do sujeito", "actionId": "ID da ação", "actionDuration": "Duração da ação", "actionResult": "Resultado da ação", "technologyId": "Tecnologia", "samplesNumber": "Número de amostras", "samplesSequentially": "Amostras sequencialmente", "sensorBrand": "Marca <PERSON>", "sensorModel": "Modelo do sensor", "attributes": "Atributos", "numericAttributes": "Atributos numéricos", "samples": "<PERSON><PERSON>", "coordinates": "<PERSON><PERSON><PERSON><PERSON>", "action": "Acción", "result": "<PERSON><PERSON><PERSON><PERSON>"}, "titles": {"subjects": "<PERSON><PERSON><PERSON><PERSON>", "subjectsShort": "<PERSON><PERSON><PERSON><PERSON>", "records": "<PERSON><PERSON><PERSON><PERSON>", "recordsShort": "<PERSON><PERSON><PERSON><PERSON>", "fingerPrint": "Impressão digital", "facial": "Facial", "iris": "<PERSON><PERSON>", "rolledFingerPrint": "<PERSON><PERSON>", "palm": "Palma", "administration": "Administração", "administrationShort": "Admin", "settings": "Configurações", "signatures": "signatures", "auditTrail": "auditTrail", "error_general": "Ocorreu um erro", "error_requiredField": "Campo obrigatório", "success_general": "error_general", "error_permisionDenied": "error_permisionDenied", "error_subjectNotFound": "error_subjectNotFound", "error_dataInconsistency": "error_dataInconsistency", "personalData": "<PERSON><PERSON>", "editSubject": "<PERSON><PERSON>", "edit_subject": "<PERSON><PERSON>", "edit_user": "<PERSON><PERSON>", "exitingWithoutSaving": "exitingWithoutSaving", "lastActionDetails": "Detalhes da Última Ação", "success_operation": "Success", "info_operation": "Information registered", "error_operation": "Operation error", "success_enroll_user": "Subject enrolled successfully", "success_add_user": "Subject created successfully", "success_modify_user": "Subject modified successfully", "success_delete_user": "Subject deleted successfully", "success_delete_picture": "Subject picture deleted successfully", "success_delete_sample": "Biometric sample deleted successfully", "success_add_picture": "Profile image added successfully", "error_enroll_user": "Subject not enrolled", "error_add_user": "Subject not created", "error_modify_user": "Subject not modified", "error_delete_user": "Subject not deleted", "error_delete_picture": "Subject picture not deleted", "error_delete_sample": "Biometric sample not deleted", "error_add_picture": "Profile image not registered", "success_identify_user": "Subject identified successfully", "error_identify_user": "Subject not found", "success_auth_user": "Subject identified successfully", "error_auth_user": "Subject authenticated successfully", "error_data_inconsistency": "Data inconsistency detected", "access_granted": "Access granted", "access_denied": "Access denied", "flows": "Fluxos", "users": "Usuários", "groups": "Grupos", "categories": "Categorias", "process": "Processo", "assignment": "Atribuições", "login_error": "<PERSON>rro de login", "role": "Roles", "profile_role": "Perfis/Funçãos", "page_not_found": "Página não Encontrada", "unauthorized": "Não autorizado", "biometric_data": "Dados Biométricos", "biographic_data": "Dados Biográficos", "extended_biographic_data": "Dados Biográficos Estendidos", "physical_data": "Dados Físicos", "profile_pic_history": "Histórico de Foto de Perfil", "related_subjects": "Sujeitos Relacionados", "entries_and_exits": "Entradas e Saídas", "entry_exit_authorizations": "Autorizações de Entrada e Saída", "tenants": "Tenants", "important": "Importante", "konektor": "Konektor", "konektor_properties": "Propriedades do Konektor", "auth": "Authentication", "systemSettings": "System Settings", "error_role_access": "Role access error", "error_login": "Login error", "widget": "Widget", "error": "Error", "samples": "<PERSON><PERSON>", "samples_subjects": "Samples by Subject", "profile_pics": "Profile pictures", "unknown": "Unknown", "requests": "Requests", "verified": "Verified", "notVerified": "Not verified", "found": "Found", "notFound": "Not found", "qFailed": "Poor quality", "tError": "Server timeout", "sError": "Server error", "other": "Other", "actionV": "Verification 1:1", "actionI": "Identification 1:N", "actionA": "Add sample", "actionD": "Delete sample", "actionP": "Add picture", "actionS": "Add subject", "actionR": "Delete subject", "userTime": "User time", "connTime": "Network time", "serverTime": "Server time", "picYes": "With profile picture", "picNo": "Without profile picture", "male": "Male", "female": "Female", "otherGender": "Other", "error_change_tenant": "Error changing tenant", "belongings": "Belongings", "no_belongings_records_available": "No belongings records available", "user_creation": "User creation", "subject_creation": "Subject creation", "add_profile_pic_history": "Add profile picture history", "belongings_photos": "Belongings photos", "belongings_hand_over_signatures": "Belongings hand over signatures", "belongings_return_signatures": "Belongings return signatures", "judicial_files": "Judicial files", "judicialFile": "Judicial file", "new_judicial_file": "New judicial file", "prisons_visits": "Prisons visits", "authorization_schedules": "Authorization schedules", "authorization_schedules_to_enter_centers": "Authorization schedules to enter centers", "transfer_authorization": "Transfer authorizations", "no_judicial_files_records_available": "No judicial files records available", "no_transfer_auths_available": "No transfer authorizations available", "no_entry_exit_auths_available": "No entry/exit authorizations available", "no_auth_schedules_available": "No authorization schedules available", "subject_files": "Files", "cases": "Processos", "evidence": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "coincidences": "Coincidências", "case_info": "Informações do Caso", "warning": "Aviso"}, "title": {"settings": "Configurações", "users": "Usuários", "applications": "Atribuindo Aplicativos", "credential_management": "Gerenciamento de credenciais", "change_password": "<PERSON><PERSON><PERSON> a <PERSON>"}, "application": {"ROLE_APP_CLOCK": "ID Clock", "ROLE_APP_PASS": "ID Pass", "ROLE_APP_MANAGER": "ID Manager", "ROLE_APP_ADMIN": "ID Admin", "ROLE_APP_REPORTS": "ID Reports", "ROLE_APP_WIDGET_MATCH": "ID Widget Match", "ROLE_APP_WIDGET_ENROLL": "ID Widget Enroll", "ROLE_APP_WIDGET": "ID Widget"}, "cases": {"case": "Caso", "new": "Novo caso", "edit": "<PERSON><PERSON> caso", "view": "Ver caso", "new_evidence": "Nova evidência", "general_info": "Informação Geral", "additional_info": "Informação Adicional", "locations": "Localizações", "location": "Localização", "search": "Pesquise na área", "assignments_relations": "Atribuições e Relações", "name": "Nome", "number": "Número do Caso", "priority": "Nível de Prioridade", "crime_type": "Tipo de Crime", "category": "Categoria Jurídica", "department": "Departamento", "status": "Status do Caso", "crime_date": "Data do Incidente", "open_date": "Data de Abertura", "close_date": "Data de Encerramento", "description": "Descrição", "location_title": "Título da Localização", "location_type": "Tipo de Localização", "comments": "Comentários", "relations": "Relações", "assignments": "Atribuições", "principal_agent": "<PERSON><PERSON>", "assigned_agents": "Agentes <PERSON>ribu<PERSON>", "related_cases": "Casos Relacionados", "related_subjects": "Sujeitos Relacionados", "under_investigation": "Em investigação", "open": "Abe<PERSON>o", "resolved": "Resolvido", "inactive": "Inativo", "waiting": "<PERSON><PERSON><PERSON><PERSON> jul<PERSON>", "closed": "<PERSON><PERSON><PERSON>", "primary": "<PERSON><PERSON><PERSON><PERSON>", "secondary": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "additional": "Adicional", "pending": "Pendente", "analyzed": "<PERSON><PERSON><PERSON>", "confirmed": "<PERSON><PERSON><PERSON><PERSON>", "edited": "Editado", "new_comment": "Novo comentário", "comment": "<PERSON><PERSON><PERSON><PERSON>", "mark_coincidence": "Marcar coincidência", "mark_coincidence_message": "Você tem certeza de que deseja marcar a amostra deste sujeito como uma ", "case_coincidence": "Coincidência de caso", "search_configuration": "Configuração de busca", "filters": "<PERSON><PERSON><PERSON>", "max_results": "Resultados máximos", "min_coincidence": "Percentual mínimo de coincidência", "coincidence_name": "Nome da Coincidência", "evidence_name": "Nome da Evidência", "coincidence_status": "Estado", "coincidence_score": "Nível de Coincidência", "last_modification": "Última Modificação", "modified_by": "Modificado por", "coincidence_type": "Tipo de Coincidência", "more_info": "<PERSON><PERSON>"}, "tenant": {"name": "Nome", "nif": "CID", "email": "email", "country": "<PERSON><PERSON>", "city": "Cidade", "status": "Status", "is_active": "Ativo", "created_at": "C<PERSON><PERSON> em", "updated_at": "Atualizado em", "no_tenants_available": "Nenhum tenant disponível.", "new_tenant": "Novo tenant", "tenant": "Tenant", "wait_tenant_creation": "Aguarde até que o processo de criação do inquilino seja totalmente concluído.", "engine": "Engine", "host": "Host", "port": "Porta", "database": "Banco de dados", "schema": "Esquema", "username": "Nome de usuário", "password": "<PERSON><PERSON>", "database_config": "Configuração do Banco de Dados", "change_tenant": "<PERSON><PERSON><PERSON>", "no_admin_token": "No admin token", "unable_to_delete_subject": "Não é possível excluir o sujeito", "unable_to_perform_action": "Não é possível executar a ação", "CameraError": "Erro ao acessar a câmera"}, "messages": {"contactYourSystemAdministrator": "Por favor, entre em contato com seu administrador de sistema", "pass_automation": "Automação de Pass", "confirm_delete_profile": "Confirme se deseja excluir o registro", "confirm_delete_multiple_profiles": "confirm_delete_multiple_profiles", "will_be_deleted_profile": "O registro com o seguinte ID será excluído:", "will_be_deleted_multiple_profile": "will_be_deleted_multiple_profile", "error_general": "Ocorreu um erro inesperado", "error_getting_actions": "Erro ao obter ações", "FAILURE": "Ocorreu um erro inesperado", "error_permisionDenied": "error_permisionDenied", "error_isRequiredField": "error_isRequiredField", "error_username_must_be_valid_numId": "O nome de usuário deve ser um número de identificação válido.", "error_username_must_be_valid_email": "O nome de usuário deve ser um e-mail válido.", "error_must_be_valid_email": "Deve ser um e-mail válido.", "success_general": "success_general", "error_subjectNotFound": "error_subjectNotFound", "error_subjectNotFoundCreate": "error_subjectNotFoundCreate", "error_dataInconsistency": "error_dataInconsistency", "confirm_delete_flow": "confirm_delete_flow", "will_be_deleted_flow": "will_be_deleted_flow:", "confirm_delete_config_profile": "confirm_delete_config_profile", "will_be_deleted_config_profile": "will_be_deleted_config_profile:", "error_codeAlreadyExists": "error_codeAlreadyExists", "error_nameAlreadyExists": "error_nameAlreadyExists", "error_parameterAlreadyExists": "error_parameterAlreadyExists", "error_profileAlreadySelected": "error_profileAlreadySelected:", "error_actionCodeAlreadyExists": "error_actionCodeAlreadyExists", "error_dataAlreadyExists": "error_dataAlreadyExists", "success_flowActionUpdated": "success_flowActionUpdated", "ERROR_PERMISSION_DENIED": "ERROR_PERMISSION_DENIED", "ERROR_WRONGCREDENTIALS": "ERROR_WRONGCREDENTIALS", "ERROR_BIOGRAFIC_REMOVEIDENTITY_NOT_COMPLETED": "ERROR_BIOGRAFIC_REMOVEIDENTITY_NOT_COMPLETED", "ERROR_BIOMETRIC_UNENROLLSUBJECT_NOT_COMPLETED": "ERROR_BIOMETRIC_UNENROLLSUBJECT_NOT_COMPLETED", "ERROR_EMPTY_PARAMETER": "ERROR_EMPTY_PARAMETER", "ERROR_USERS_TENANT_CONFIG_NOT_FOUND": "ERROR_USERS_TENANT_CONFIG_NOT_FOUND", "ERROR_CONFIG_NOT_FOUND": "ERROR_CONFIG_NOT_FOUND", "ERROR_CONFIG_DATA_NOT_FOUND": "ERROR_CONFIG_DATA_NOT_FOUND", "confirmPasswordFailed": "confirmPasswordFailed", "exitingWithoutSaving": "exitingWithoutSaving", "continueWithoutSaving": "continueWithoutSaving", "selectTechnology": "selectTechnology", "selectTechnologyUserVerification": "selectTechnologyUserVerification", "error_dateRangeError": "error_dateRangeError", "ERROR_USER_IS_BLOCKED": "ERROR_USER_IS_BLOCKED", "ERROR_BIOMETRIC_VERIFICATION_FAILED": "ERROR_BIOMETRIC_VERIFICATION_FAILED", "ERROR_INVALID_NUMID": "ERROR_INVALID_NUMID", "confirm_delete_profile_picture": "confirm_delete_profile_picture", "will_be_deleted_profile_picture": "will_be_deleted_profile_picture", "user_without_samples_tec_selected": "user_without_samples_tec_selected", "user_without_samples": "user_without_samples", "quality_error": "A qualidade da imagem é insuficiente para identificação", "invalidPassword": "A senha deve ter pelo menos 12 caracteres e conter pelo menos um dígito, uma letra maiúscula, uma letra minúscula e um caractere especial", "invalidPasswordLength": "invalid<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "invalidPasswordComplexity": "invalidPasswordComplexity", "invalidAccessKey": "<PERSON><PERSON> de acesso inválida", "accessKeyDisabled": "accessKeyDisabled", "disableOnNextAttempt": "disableOnNextAttempt", "needLocationToEntry": "O usuário precisa de uma localização pendente para entrar", "noLocationToEnterHere": "O usuario não tem uma localização para entrar aqui", "noLocationToEnterTransfer": "El sujeto no tiene ninguna ubicación válida para entrar en el traslado", "subjectMustReturnToSameLocation": "O sujeito deve retornar à mesma localização", "noAuthScheduleForRole": "Não há agendamento de autenticação para a função", "noAuthScheduleForLocationAndRole": "Não há agendamento de autenticação para a localização e função", "noValidAuthScheduleForLocationAndRole": "Não há agendamento de autenticação válido para a localização e função", "noRelatedSubjectsOfEntryRole": "Não tem disciplinas relacionadas à função exigida para o ingresso: ", "exitLocationNotFound": "Localização de saída não encontrada", "needRoleToEntry": "O usuário precisa de uma função para entrar", "noEntryOrExitUser": "O usuário não tem permissão para entrar ou sair", "noEntryOrExitUserLocation": "O usuário não tem permissão para entrar ou sair desse localização", "noCreateLocationBecausePending": "Não é possível criar uma nova localização porque há uma pendente", "message_remove": "Tem certeza de que deseja excluir ", "message_update": "Tem certeza de que deseja atualizar ", "invalid_shedule_type": "Selecione um tipo de agendamento válido.", "error_range_dates": "A hora de início não pode ser maior que a hora de término.", "no_days_selected": "Selecione pelo menos um dia.", "group_same_name": "Já existe um grupo com o mesmo nome.", "assignment_same_name": "Já existe um atribuição com o mesmo nome.", "category_flow_required": "Para criar atribui<PERSON><PERSON><PERSON>, você deve ter pelo menos um fluxo de trabalho e uma categoria.", "flow_required": "Para criar atribu<PERSON><PERSON><PERSON><PERSON>, você deve ter pelo menos um fluxo de trabalho.", "category_required": "Para criar atribu<PERSON><PERSON><PERSON><PERSON>, você deve ter pelo menos uma categoria.", "warning": "Aviso", "user_password_error": "E-mail o senha incorrecta.", "error_username_password_required": "Nome de usuário e senha são obrigatórios.", "error_email_password_required": "Email e senha são obrigatórios.", "delete_confirmation_header": "Confirmação de eliminação", "delete_multiple_records": "Você tem certeza de que deseja excluir os registros selecionados?", "delete_single_record": "Tem certeza de que deseja excluir o registro:", "page_not_found": "<PERSON><PERSON><PERSON><PERSON>, a página não foi encontrada.", "unauthorized": "<PERSON><PERSON><PERSON><PERSON>, você não tem permissão para acessar este recurso.", "widget_error": "Erro ao carregar o widget", "tenant_id_not_found": "ID do tenant não encontrado", "konektor_properties_not_found": "Propriedades do Konektor não encontradas", "konektor_connection_error": "Erro de conexão com o Konektor", "get_konektor_properties_error": "<PERSON><PERSON> ao obter as propried<PERSON> do Konektor", "konektor_properties_error": "Erro nas propriedades do Konektor", "must_select_reason": "Você deve selecionar um motivo", "password_not_match": "As senhas não correspondem.", "error_updating_password": "Erro ao tentar atualizar a senha.", "password_updated": "Senha atualizada com sucesso!", "no_cam_detected": "<PERSON>enhuma câmera <PERSON>ada", "no_cam_selected": "Nenhuma câmera selecionada", "no_data_to_save": "<PERSON>enhum dado para salvar", "no_data_to_search_coincidence": "Nenhum dado para busca coincidências", "subject_inside_time_left": "O sujeito ainda está dentro da instalação. O tempo restante é de: ", "subject_outside_time_left": "O sujeito já saiu da instalação. O tempo restante é de: ", "verification_required": "Se deseja editar o excluir dados do sujeito, você deve ser verificado por biometria como usuário.", "verification_required_user": "Se deseja editar o excluir dados do usuário, você deve ser verificado por biometria como usuário.", "verification_required_data": "Se deseja editar dados do sujeito, você deve ser verificado por biometria como usuário.", "verification_required_enroll": "Se deseja adicionar dados biométricos ao sujeito, você deve ser verificado por biometria como usuário.", "subject_verification_required_data": "Se deseja editar dados do sujeito, .", "error_getting_access": "Erro ao obter acesso", "error_login": "<PERSON>rro de login", "duplicate_subject_numId": "O número de ID já está em uso", "duplicate_user_email": "O e-mail já está em uso", "duplicate_user_numId_or_email": "O número de ID ou e-mail já está em uso", "error_technology_not_allowed": "Tecnologia não permitida", "no_techs_available": "Nenhuma tecnologia disponível", "window_used": "Esta janela já está no fluxo", "saved_successfully": "Salvo com sucesso!!!", "updated_successfully": "Atualizado com sucesso!!!", "removed_successfully": "Removido com sucesso!!!", "error_retrieving_data": "Erro ao recuperar dados", "error_delete": "Erro ao tentar remover", "error_delete_record": "Erro ao tentar remover o registro", "error_save_record": "Erro ao tentar salvar o registro", "error_save": "Erro ao tentar salvar", "error_update": "Erro ao tentar atualizar", "error_update_record": "Erro ao tentar atualizar o registro", "success_delete_record": "Registro excluído com sucesso", "success_save_record": "Registro salvo com sucesso", "success_update_record": "Registro atualizado com sucesso", "message_update_data_source": "Tem certeza de que deseja atualizar ", "message_add_parameters": "Tem certeza de que deseja adicionar os novos parâmetros a ", "message_remove_subject_app": "Tem certeza de que deseja remover o aplicativo do usuário?: ", "api_connection_error": "Erro na conexão da API ", "error_subject_has_user": "O sujeito tem um usuário associado", "error_resetting_password": "Houve um erro ao tentar redefinir a senha e o e-mail de recuperação não pôde ser enviado, entre em contato com o administrador do sistema", "password_reset_email_sent": "E-mail de redefinição de senha enviado", "error_license": "Erro de licença por favor, entre em contato com o administrador", "error_license_no_access": "<PERSON>rro de licença, sem acesso", "error_license_not_configured_konektor": "<PERSON><PERSON> licen<PERSON>, Konektor não configurado", "error_cannot_access_without_license": "Não é possível acessar sem uma licença, entre em contato com o administrador", "cannot_delete_subject_user": "Não é possível excluir um sujeito com um usuário associado", "cannot_save_subject_user_ds": "O sujeito não pode ser salvo porque a fonte de dados selecionada não permite", "cannot_create_subject_user_ds": "O sujeito não pode ser criado porque a fonte de dados selecionada não permite", "cannot_update_subject_user_ds": "O sujeito não pode ser atualizado porque a fonte de dados selecionada não permite", "cannot_delete_subject_user_ds": "O sujeito não pode ser excluído porque a fonte de dados selecionada não permite", "ContactAdminCamera": "Verifique se a câmera tem permissão para ser utilizada neste site", "error_widget_url": "Erro com a URL do widget, entre em contato com o administrador do sistema", "timeout": "O tempo limite deve ser superior a 10 segundos e menos de 30 minutos.", "new_theme_added": "Novo tema adicionado!", "method_added": "Método adicionado!", "mapping_pair_added": "Mapeo agregado:", "connection_added": "Nova conexão API agregada!", "error_unlink_license": "Erro ao desvincular a licença", "error_license_scope": "O número de licenças excedido para as seguintes aplicações: ", "reduce_num_licenses": "Você tem certeza que quer reduzir o número de licenças para a aplicação selecionada? Esta ação também removerá aplicações das licenças atribuídas ou removerá a licença se não houver mais aplicações.", "passlink_not_available": "PassLink não está disponível. Verifique a conexão e tente novamente.", "no_match": "A senha não corresponde!", "error_obtaining_subject_apps": "Error tratando de obtener las aplicaciones del sujeto. Por favor contacte con el administrador del sistema.", "adding_credentials": "Adicionando credencia<PERSON>, aguarde.", "executing_events": "Executando eventos, aguarde.", "no_credentials_to_update": "Nenhuma credencial para atualizar.", "error_updating_credentials": "<PERSON>rro ao tentar atualizar as credenciais.", "credentials_updated": "Credenciais atualizadas com sucesso.", "subject_without_apps": "Aplicativos não atribuídos.", "web_not_valid": "Página web não válida.", "unable_to_login": "Não foi possível fazer login.", "konektor_properties_missed": "Faltam algumas configurações no Konektor. Por favor, verifique e tente novamente.", "confirm_element_not_found": "O item de confirmação não foi encontrado, portanto as credenciais não serão atualizadas.", "no_apps_assigned": "Nenhum aplicativo atribuído em Pass.", "no_apps_assigned_web": "Não há aplicativos da Web atribuídos à sua identidade para o Verázial ID Pass. Atribua um aplicativo da Web na seção Pass do Verázial ID Admin para prosseguir.", "no_apps_assigned_passlink": "Nenhum aplicativo atribuído em PassLink ou não correspondem com os aplicativos atribuídos em Pass.", "pageNotFound": "Página não encontrada", "pageNotFoundDescription": "A página que você está procurando não existe.", "unauthorised": "Não autorizado", "unauthorised_description": "Você não está autorizado a acessar esta página.", "touchTheScreen": "Toque na tela", "toStart": "para ", "start": "<PERSON><PERSON><PERSON>", "error_auth_token": "Erro ao obter o token de autenticação", "noTechSelected": "Nenhuma tecnologia biométrica principal selecionada", "noTechSelectedDescription": "Por favor, defina uma tecnologia principal no <PERSON>ne<PERSON>or.", "noTechsAvailable": "Nenhuma tecnologia biométrica disponível", "noTechsAvailableDescription": "Não há tecnologias biométricas disponíveis. Por favor, entre em contato com o administrador do sistema.", "generalErrorTitle": "Ocorreu um erro", "errorUpdatingTransferUser": "Erro ao atualizar o usuário de autorização de transferência", "errorUpdatingTransferAuth": "Erro ao atualizar a autorização de transferência", "errorUpdatingEntryExitAuth": "Erro ao atualizar a autorização de entrada e saída", "generalErrorDescription": "Ocorreu um erro inesperado.", "settingsErrorTitle": "Erro ao recuperar as configurações", "techErrorDescription": "Não há tecnologias biométricas definidas.", "settingsErrorDescription": "Ocorreu um erro ao tentar recuperar as configurações.", "konektorErrorTitle": "Erro com as configurações do Konektor", "widgetSettingsErrorTitle": "<PERSON>rro ao recuperar as configurações do widget", "widgetSettingsErrorDescription": "Ocorreu um erro ao tentar recuperar as configurações do widget.", "widgetURLSettingsErrorTitle": "Erro nas configurações de URL do widget", "widgetURLSettingsErrorDescription": "Ocorreu um erro ao tentar utilizar a URL do widget configurado.", "techErrorTitle": "Erro de tecnologia biométrica", "techErrorPayedDescription": "Não há tecnologias biométricas contratadas definidas.", "techErrorAllowSearchDescription": "Não há tecnologias biométricas de identificação definidas.", "fingerprintTechErrorTitle": "Erro de tecnologia de impressão digital", "fingerprintTechErrorDescription": "A tecnologia de impressão digital não está definida.", "fingerprintTechErrorPayedDescription": "A tecnologia de impressão digital não está definida como tecnologia contratada.", "fingerprintTechErrorAllowSearchDescription": "A tecnologia de impressão digital não está definida como tecnologia de identificação.", "fingerprintTechErrorAllowVerifyDescription": "A tecnologia de impressão digital não está definida como tecnologia de verificação.", "irisTechErrorTitle": "Erro de tecnologia de íris", "irisTechErrorDescription": "A tecnologia de íris não está definida.", "irisTechErrorPayedDescription": "A tecnologia de íris não está definida como tecnologia contratada.", "irisTechErrorAllowSearchDescription": "A tecnologia de íris não está definida como tecnologia de identificação.", "irisTechErrorAllowVerifyDescription": "A tecnologia de íris não está definida como tecnologia de verificação.", "facialTechErrorTitle": "Erro de tecnologia facial", "facialTechErrorDescription": "A tecnologia facial não está definida.", "facialTechErrorPayedDescription": "A tecnologia facial não está definida como tecnologia contratada.", "facialTechErrorAllowSearchDescription": "A tecnologia facial não está definida como tecnologia de identificação.", "facialTechErrorAllowVerifyDescription": "A tecnologia facial não está definida como tecnologia de verificação.", "geolocationNotSupported": "Geolocalização não é suportada por este navegador.", "dataInconsistencyErrorTitle": "Inconsistência de dados", "dataInconsistencyErrorDescription": "A identidade existe no servidor de ID Verázial, mas não no seu servidor biométrico. Por favor, entre em contato com o administrador do sistema.", "saveActionErrorTitle": "Erro ao salvar a ação", "saveActionErrorDescription": "Ocorreu um erro ao tentar salvar a ação.", "getActualTimeErrorTitle": "Erro ao obter a hora atual", "getActualTimeErrorDescription": "Ocorreu um erro ao tentar obter a hora atual.", "langModifiedSuccess": "Idioma modificado com sucesso", "abortOperation": "O usuário cancelou a operação", "searchAssignmentsErrorTitle": "<PERSON>rro ao pesquisar as atribuições", "searchAssignmentsErrorDescription": "Ocorreu um erro ao tentar pesquisar as atribuições.", "searchAssignmentsWarningTitle": "Nenhuma atribuição de fluxo de ação encontrada", "searchAssignmentsWarningDescription": "Nenhuma atribuição de fluxo de ação foi encontrada para o sujeito e/ou local.", "noActionsAvailableErrorTitle": "Nenhuma ação disponível", "noActionsAvailableErrorDescription": "Não há ações disponíveis para o sujeito, dispositivo ou horário atual.", "noFlowsAssignedErrorTitle": "Nenhum fluxo atribuído", "noFlowsAssignedErrorDescription": "Não há fluxos atribuídos ao sujeito, dispositivo ou horário atual.", "noFlowsAssignedErrorBothDescription": "Não há fluxos atribuídos ao sujeito e local.", "noFlowsAssignedErrorBothScheduleDescription": "Não há fluxos atribuídos ao sujeito, local e horário.", "noFlowsAssignedErrorLocationDescription": "Não há fluxos atribuídos ao local.", "noFlowsAssignedErrorLocationScheduleDescription": "Não há fluxos atribuídos ao local com base no horário.", "noFlowsAssignedErrorSubjectDescription": "Não há fluxos atribuídos ao sujeito.", "noFlowsAssignedErrorSubjectScheduleDescription": "Não há fluxos atribuídos ao sujeito com base no horário.", "noFlowsAssignedErrorSubjectLocationDescription": "Não há fluxos atribuídos ao sujeito e local.", "noFlowsAssignedErrorSubjectLocationScheduleDescription": "Não há fluxos atribuídos ao sujeito e local com base no horário.", "noFlowsInAssignmentTitle": "Nenhum fluxo na atribuição", "noFlowsInAssignmentBothDescription": "Não há fluxos na atribuição para o sujeito e local.", "noFlowsInAssignmentLocationDescription": "Não há fluxos na atribuição para o local.", "noFlowsInAssignmentSubjectDescription": "Não há fluxos na atribuição para o sujeito.", "noValidFlowsFoundTitle": "Nenhum fluxo válido encontrado", "noValidFlowsFoundDescription": "Nenhum fluxo válido foi encontrado para o sujeito, dispositivo ou hor<PERSON>rio atual.", "noValidFlowsFoundBothDescription": "Não há fluxos válidos na atribuição para o sujeito e local.", "noValidFlowsFoundBothScheduleDescription": "Não há fluxos válidos na atribuição para o sujeito e local com base no horário.", "noValidFlowsFoundLocationDescription": "Não há fluxos válidos para este local.", "noValidFlowsFoundLocationScheduleDescription": "Não há fluxos atribuídos a este local e horário.", "noValidFlowsFoundSubjectDescription": "Não há fluxos válidos na atribuição para o sujeito.", "noValidFlowsFoundSubjectScheduleDescription": "Não há fluxos válidos na atribuição para o sujeito com base no horário.", "noValidFlowsFoundSubjectLocationDescription": "Não há fluxos válidos na atribuição para o sujeito e local.", "noValidFlowsFoundSubjectLocationScheduleDescription": "Não há fluxos válidos na atribuição para o sujeito e local com base no horário.", "noValidAssignmentsFoundTitle": "Nenhuma atribuição válida encontrada", "noValidAssignmentsFoundDescription": "Não há atribuições válidas para o sujeito e/ou local.", "noValidAssignmentFoundBothDescription": "Não há atribuições válidas para o sujeito e local.", "noValidAssignmentFoundLocationDescription": "Não há atribuições válidas para o local.", "noValidAssignmentFoundSubjectDescription": "Não há atribuições válidas para o sujeito.", "fireKonektorRelayErrorTitle": "Erro ao acionar o relé do Konektor", "fireKonektorRelayErrorDescription": "Ocorreu um erro ao tentar acionar o relé do Konektor.", "noValidFlowsErrorTitle": "Nenhum fluxo válido encontrado", "noValidFlowsErrorDescription": "Nenhum fluxo válido foi encontrado para o sujeito, dispositivo ou hor<PERSON>rio atual.", "authServerConnectionErrorTitle": "Erro de conexão com o servidor de autenticação", "authServerConnectionErrorDescription": "Ocorreu um erro ao tentar se conectar ao servidor de autenticação.", "managerSettingsErrorTitle": "Erro ao recuperar as configurações do Manager", "managerSettingsErrorDescription": "Ocorreu um erro ao tentar recuperar as configurações do Manager.", "systemSettingsErrorTitle": "Erro ao recuperar as configurações do sistema", "systemSettingsErrorDescription": "Ocorreu um erro ao tentar recuperar as configurações do sistema.", "systemSettingsErrorNotFound": "Erro ao recuperar as configurações do sistema", "enterNumID": "Digite o número de ID", "invalidNumId": "Número de ID inválido", "licenseErrorDescription": "Erro com a licença, por favor, entre em contato com o administrador do sistema", "no_data_found": "No hay datos disponibles", "no_data": "Sin datos", "DisabledLicenseTitle": "Licença desativada", "NoLicenseMessage": "Não foi encontrada nenhuma licença ativa para este software. Favor entrar em contato com o administrador do sistema", "error_change_tenant": "Erro ao alterar o tenant", "user_created_success": "Usuário criado com sucesso", "a_subject_was_created_based_on_the_user": "Usuário criado com sucesso. Um sujeito foi criado com base no usuário", "location_updated": "Localização atualizada com sucesso", "location_updated_error": "Erro ao atualizar a localização", "location_created": "Localização criada com sucesso", "location_created_error": "Erro ao criar a localização", "location_deleted": "Localização eliminada com sucesso", "location_deleted_error": "Erro ao eliminar a localização", "cant_delete_actual_location": "Não é possível excluir a localização atual", "delete_location": "Tem certeza de que deseja excluir a localização: ", "no_delete_location": "O local não pode ser excluído porque está ocupado por um sujeito", "action_created": "Ação criada com sucesso", "action_created_error": "Erro ao criar a ação", "error_locations": "Erro com as localizações", "error_origin_destiny_locations": "A localização de origem e destino não podem ser iguais", "error_dates": "Erro com as datas", "error_departure_arrival_dates": "A data de partida não pode ser maior que a data de chegada", "error_list_of_prisoners": "Erro com a lista de prisioneiros", "error_at_least_one_subject": "Selecione pelo menos um sujeito", "error_list_of_responsible_personel": "Erro com a lista de pessoal responsável", "error_transfer_authorization": "Erro com a autorização de transferência", "all_required_fields_details_prisoners_responsible_personnel": "Todos os campos obrigatórios devem ser preenchidos, pelo menos um sujeito prisioneiro e um sujeito de pessoal responsável devem ser selecionados", "success_transfer_authorization_created": "Autorização de transferência criada com sucesso", "success_transfer_authorization_updated": "Autorização de transferência atualizada com sucesso", "error_must_select_cancel_reason": "Você deve selecionar um motivo de cancelamento", "error_subject_not_allowed_to_sign": "Os papéis do sujeito não permitem assinar", "error_start_end_dates": "A data de início não pode ser maior que a data de término", "error_entry_exit_authorization": "Erro com a autorização de entrada e saída", "success_entry_exit_authorization_created": "Autorização de entrada e saída criada com sucesso", "success_entry_exit_authorization_updated": "Autorização de entrada e saída atualizada com sucesso", "duplicate_nif": "O CID já está em uso", "error_current_tenant_not_same_as_configured_in_konektor": "O tenant atual não é o mesmo que o configurado no Konektor", "error_jobNotActive": "O trabalho não está ativo", "error_jobExecutionFailed": "A execução do trabalho falhou", "success_jobExecutionSuccess": "A execução do trabalho foi bem-sucedida", "error_cronServiceSchedulerInit": "Erro ao inicializar o agendador de serviço de cron", "cronServiceSchedulerInitSuccess": "Agendador de serviço de cron inicializado com sucesso", "success_jobExecutionScheduled": "A execução do trabalho foi agendada", "error_jobExecutionScheduled": "Erro ao agendar a execução do trabalho", "success_removingJobScheduled": "A remoção do trabalho agendado foi bem-sucedida", "error_removingJobScheduled": "Erro ao remover o trabalho agendado", "error_deleting_roles": "Erro ao excluir funções", "error_adding_roles": "Erro ao adicionar funções", "error_retrieving_roles": "Erro ao recuperar funções", "error_deleting_profiles": "Erro ao excluir perfis", "error_adding_profiles": "Erro ao adicionar perfis", "error_retrieving_profiles": "Erro ao recuperar perfis", "error_creating_user": "Erro ao criar o usuário", "error_creating_subject": "<PERSON>rro ao criar o sujeito", "error_updating_user": "Erro ao atualizar o usuário", "error_updating_subject": "Erro ao atualizar o sujeito", "error_deleting_user": "Erro ao excluir o usuário", "error_deleting_subject": "Erro ao excluir o sujeito", "error_uploading_image": "Erro ao fazer upload da imagem", "error_downloading_image": "Erro ao baixar a imagem", "error_removing_image": "Erro ao remover a imagem", "error_saving_extended_biographic_data": "Erro ao salvar os dados biográficos estendidos", "error_obtaining_extended_biographic_data": "Erro ao obter os dados biográficos estendidos", "error_retrieving_criminal_cases": "Erro ao recuperar processos criminais", "error_retrieving_users": "Erro ao recuperar os usuários", "error_retrieving_subjects": "Erro ao recuperar os sujeitos", "error_retrieving_user": "Erro ao recuperar o usuário", "error_retrieving_subject": "Erro ao recuperar o sujeito", "error_retrieving_user_subject_data": "Erro ao recuperar os dados do sujeito do usuário", "error_processing_subject_deletion": "Erro ao processar a exclusão do sujeito", "error_retrieving_system_report": "Erro ao recuperar o relatório do sistema", "error_retrieving_biometric_sample_count": "Erro ao recuperar a contagem de amostras biométricas", "error_retrieving_license_report": "Erro ao recuperar o relatório de licença", "error_retrieving_number_of_users": "Erro ao recuperar o número de usuários", "error_retrieving_number_of_subjects": "Erro ao recuperar o número de sujeitos", "error_retrieving_system_settings": "Erro ao recuperar as configurações do sistema", "error_no_system_settings_found_for_application": "Erro: nenhuma configuração do sistema encontrada para o aplicativo", "error_retrieving_konektor_properties": "<PERSON><PERSON> ao recuperar as propriedades do Konektor", "error_deleting_related_subjects": "Erro ao excluir os sujeitos relacionados", "error_deleting_entry_exit_auth": "Erro ao excluir a autorização de entrada e saída", "error_updating_location": "Erro ao atualizar a localização", "error_creating_tenant": "Erro ao criar o tenant", "error_updating_tenant": "Erro ao atualizar o <PERSON>", "error_deleting_tenant": "Erro ao excluir o tenant", "error_creating_role_profile": "Erro ao criar o perfil/função", "error_updating_role_profile": "Erro ao atualizar o perfil/função", "error_deleting_role_profile": "Erro ao excluir o perfil/função", "error_retrieving_role_profile": "Erro ao recuperar o perfil/função", "error_deleting_transfer_auth": "Erro ao excluir a autorização de transferência", "error_deleting_belongings": "Erro ao excluir os pertences", "error_deleting_auth_schedule": "Erro ao excluir o agendamento de autenticação", "error_deleting_category": "Erro ao excluir a categoria", "error_updating_task_flow": "Erro ao atualizar o fluxo de tarefas", "error_deleting_judicial_file": "Erro ao excluir o arquivo judicial", "locations_available_in_a_future_date": "As {0} localizações disponíveis são apenas para datas futuras: {1}", "location_available_in_a_future_date": "A 1 localização disponível é apenas para datas futuras: {1}", "error_translation_already_exists_for": "Erro: a tradução já existe para o idioma {0}", "error_translation_record_already_exists_for_key": "Erro: o registro de tradução já existe para a chave {0}", "user_sessions_exceeded_description": "O usuário excedeu o número máximo de sessões permitidas. O número máximo de sessões permitidas é {0}.", "user_blocked_description": "O usuário está bloqueado. O usuário será desbloqueado após {0}", "user_remaining_attempts_description": "Tem {0} tentativas restantes", "user_lock_on_next_attempt": "O usuário será bloqueado se a próxima tentativa de acesso falhar.", "file_size_exceeded": "{0}: <PERSON><PERSON><PERSON> de arquivo inválido", "file_size_exceeded_detail": "O tamanho do arquivo excede o tamanho máximo permitido de {0}", "invalid_file_type": "{0}: Tipo de arquivo inválido", "invalid_file_type_detail": "Os tipos de arquivo permitidos são {0}", "invalid_file_limit_detail": "O limite é de {0} arquivos no máximo", "invalid_file_limit": "Limite máximo de arquivos excedido", "fileUploadedSuccess": "Arquivo {0} carregado com sucesso", "fileUploadedError": "Erro ao carregar o arquivo {0}", "previewNotAvailable": "Pré-visualização não disponível", "downloadNotAvailable": "Download não disponível", "fileDownloadedSuccess": "Arquivo {0} baixado com sucesso", "fileContentNotFound": "Conteúdo do arquivo não encontrado", "error_downloading_file": "Erro ao baixar o arquivo", "data_update_sent": "Dados atualizados localmente e enviados para a aplicação de destino.", "error_creating_case": "Erro ao criar o caso criminal", "error_updating_case": "Erro ao atualizar o caso criminal", "error_deleting_case": "Erro ao excluir o caso criminal", "invalid_max_time": "O tempo máximo não pode ser 00:00", "noValidSampleTypeSelected": "Por favor, selecione um tipo de amostra válido para a amostra.", "error_enrolling_subject": "Erro ao inscrever o sujeito", "duplicate_sample": "A amostra já está inscrita em um sujeito diferente", "BAD_FILTER": "Filtro incorreto", "EXTRACTION_UNKNOWN": "Extração desconhecida", "EXTRACTION_NOT_ENOUGH_QUALITY": "A amostra não atende aos requisitos de qualidade", "UNKNOWN": "<PERSON><PERSON>conhe<PERSON>", "NO_LICENSE": "Nenhuma licença disponível", "must_contain_at_least_one_mapping": "<PERSON>e conter pelo menos um mapeamento", "index_already_used": "O índice já está em uso", "type_already_used": "O tipo já está em uso", "error_retrieving_system_user_profile": "Erro ao recuperar o perfil de usuário do sistema", "cannot_delete_self": "Não é possível excluir você mesmo", "": ""}, "options": {"true": "<PERSON>m", "false": "Não", "male": "<PERSON><PERSON>", "female": "<PERSON><PERSON><PERSON>", "M": "<PERSON><PERSON>", "F": "<PERSON><PERSON><PERSON>"}, "reasons": {"biometric_auth": "Biometric authentication", "biometric_id": "Biometric identification", "biometric_login": "Biometric login", "pass_login": "User/password login", "pin_login": "Identification with ID", "key_login": "Login with one-time password", "no_dev": "Device not detected", "new_subject": "Add new subject", "biographic_data": "Biographic data", "facial": "Facial", "iris": "Iris", "fingerprint": "Fingerprint", "rolledFingerPrint": "Rolled Fingerprint", "palm": "Palm", "additional_info": "Additional information", "summary": "Summary", "new_sample": "Add biometric sample", "new_picture": "Add profile picture", "update_pass": "Update password"}, "role": {"roles": "Roles", "new_role": "Novo Role", "new_profile_role": "Novo Perfil/Função", "general": "G<PERSON>", "accesses": "Acesos", "selected_roles": "Funções selecionadas", "change_role": "<PERSON><PERSON>r perfil", "select_role": "Selecionar a função", "select_as_default_role": "Selecionar como função padrão", "update_role": "Atualizar função", "update_profile_role": "Atualizar <PERSON>/Função", "no_roles_available": "Nenhuma função disponível"}, "role_names": {"SYSTEM_USER": "Usuário do sistema"}, "pass_application": {"applications": "Aplicações", "application_flow": "Processos de Aplicação", "technology": "Tecnologia", "application_type": "Tipo", "data_source": "Fonte de dados", "new_application": "Nova aplicação", "new_application_flow": "Novo processo", "save_application": "<PERSON><PERSON> a<PERSON>", "save_application_flow": "Salvar processo de aplicação", "full_path": "Rota ou URL", "the_window": "a janela", "new_window": "Nova janela", "add_window": "<PERSON><PERSON><PERSON><PERSON> j<PERSON>", "application": "Aplicativo", "no_windows": "<PERSON><PERSON> j<PERSON>", "target": "Alvo", "edit_window": "Janela de edição", "window": "Window", "window_components": "Componentes da janela", "new_component": "Novo componente", "attribute_name": "Nome do atributo", "component_type": "Tipo de componente", "trigger_order": "Ordem de execução", "position": "Posição", "event": "Evento", "display_component_user": "Exibir componente para o usuário?", "update_application": "Atualizar aplicativo", "update_application_flow": "Atualizar processo de aplicação", "remove_application": "Remover aplicativo", "remove_application_flow": "Remover processo de aplicação", "no_applications_available": "Nenhuma aplicação disponível", "authentication": "Autenticação", "process": "Processo", "update_credentials": "<PERSON><PERSON><PERSON><PERSON> creden<PERSON>is", "appRegistry": "Aplicação", "flow_type": "Tipo de Fluxo"}, "pass_datasource": {"datasources": "Fontes de dados", "new_datasource": "Nova fonte de dados", "method": "Método/URL", "parameters": "Parâmetros", "update_datasource": "Atualizar fonte de dados", "add_datasource": "Adicionar fonte de dados", "datasource": "Fonte de dados", "add_parameter": "<PERSON><PERSON><PERSON><PERSON>", "parameter": "Parâmetros", "add_parameters": "<PERSON><PERSON><PERSON><PERSON>", "remove_data_source": "Remover fonte de dados", "no_datasource_available": "Sem fonte de dados disponível"}, "pass_assigment": {"application_assigment": "Atribuição de Aplicativos", "Application_identifier": "ID do aplicativo", "authorization_id": "ID de autorização", "application": "Aplicativo", "application_flow": "Processos de Aplicação", "application_flow_name": "Nome do Processo", "connection_type": "Tipo de <PERSON>", "can_user_update": "<PERSON><PERSON><PERSON><PERSON>", "initialised": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "host": "Host", "subject_application": "Aplicações do assunto", "credentials": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "parameter": "Parâmetro", "value": "Valor", "fill_credential": "Adicione credenciais e dados", "select_apps": "Selecione um aplicativo.", "select_apps_flow": "Selecione um aplicativo ou fluxo.", "allow_user_update_password": "<PERSON><PERSON><PERSON> que o usuário atualize sua senha.", "must_user_update_credentials": "As credenciais devem ser inicializadas?", "host_name": "Endereço IP/Nome do host", "datagrip_row": "Insira o número da linha da tabela.", "option_dropdown": "Insira o texto da opção a ser selecionada no menu suspenso.", "option_list": "Digite o texto da opção a ser selecionada na lista.", "window": "<PERSON><PERSON>", "application_credential": "Aplicação e Credenciais", "Dirección IP/Nombre del Host": "Endereço IP/Nome do Host"}, "headers": {"contracted_bio_tech": "Tecnologias Biométricas Contratadas", "after_searching_by_id": "<PERSON><PERSON>is de Pesquisar por ID, Permitir", "allow_searching_by": "<PERSON><PERSON><PERSON>", "tech_required_enroll": "Tecnologia Necessária para se Inscrever", "basic_bio_fields": "Campos Biográficos Básicos", "min_quality_samples": "Qualidade Mínima <PERSON>", "avg_quality_samples": "Qualidade Média <PERSON>", "allow_verify_by": "<PERSON><PERSON><PERSON> por", "num_iris_one_n": "Num. de Íris 1:N", "sensors_capacity": "Capacidade dos sensores", "num_fp_one_n": "Num. de Impressões Digitais 1:N", "num_fp_roll_one_n": "Impressões Digitais Roladas 1:N", "num_palm_one_n": "Palmas 1:N", "min_matching_fp": "Impressões Digitais Correspondentes mínimas", "reason_uncollect_samples": "Motivos para Amostras Incobráveis", "min_iris_match": "Correspondência Mínima de Íris", "one_n_when_enrolling": "1:N ao se Inscrever", "profile_type": "Tipo de Perfil", "locations": "Localizações", "segmented_search": "Pesquisa Segmentada", "enroller_session_timeout": "Enroller <PERSON><PERSON><PERSON> (ms)", "licences": "Licenças", "licenses": "Licenças", "access_key": "<PERSON><PERSON>", "external_bio_server": "<PERSON><PERSON><PERSON> (Legacy)", "config_access": "Acesso de Configuração", "access_action_reg": "Servidor <PERSON> Açõ<PERSON>", "templates_info": "Informações do modelo", "verazial_id_server": "Verázial ID Server", "verazial_id_modules": "<PERSON><PERSON><PERSON><PERSON>", "external_servers": "<PERSON><PERSON><PERSON>", "biometric_server": "Biometric Server", "biografic_server": "Biografic Server", "storage_server": "Storage Server", "clock_server": "Clock Server", "extended_bio_fields": "Campos Biográficos Estendidos", "user": "Usuários", "roles": "Funções", "asign_modules": "Atribuição de Módulo", "modules": "<PERSON><PERSON><PERSON>", "role": "Rol", "external_guess_fields": "Campos Externo Visitante", "external_employee_fields": "Campos Externo Empleado", "tenant": "Tenant", "user_verification": "Verificação do Usuário", "user_verification_userpass": "Login do usuário com autenticação básica", "user_verification_biometric": "Verificação do Usuário com Biometria", "password_recovery_token_expiration": "Expiração do Token de Recuperação de Senha", "password_expiration": "Expiração da Senha", "biographic_data_management_reasons": "Motivos - Gestão Dados Biográficos", "biometric_samples_management_reasons": "Motivos - Gestão Dados Biométricos", "config_management_reasons": "Motivos - Gestão Configurações", "password_management_reasons": "Motivos - Gestão Senhas", "location_management_reasons": "Motivos - Gestão Localizações", "tenant_license_management_reasons": "Motivos - Gestão Licenças Tenant", "role_management_reasons": "Motivos - Gestão Funções", "export_reports_management_reasons": "Motivos - Gestão Exportação Relatórios", "user_management_reasons": "Motivos - Gestão Usuários", "pass_app_management_reasons": "Motivos - Gestão Aplicações Pass", "concurrent_sessions": "Sessões Simultâneas", "audit_trail_config": "Configuração Trilha Auditoria", "intern_fields": "Campos Interno", "extern_provider_fields": "Campos Fornecedor Externo", "physical_fields": "Campos Físicos", "visiting_time_visitors": "Tempo Disita Visitantes", "supplier_visit_time": "Tempo Visita Fornecedor", "reasons_exit": "Razões Partida", "allow_entry_exit": "Permitir Entrada e Saída", "update_password": "<PERSON><PERSON><PERSON><PERSON>", "modify": "Modificar", "delete": "Excluir", "widget_options": "Configuração de Acesso ao Widget", "subjectTabsConfig": "Configuração da guia do sujeito", "showAdditionalTabs": "Mostrar guias adicionais", "restrictTabToSpecificRoles": "Restringir guia para perfil específicas", "specificRoles": "Perfis específicas", "showPrisonTab": "Mostrar guia de p<PERSON>ão", "showProfilePictureTab": "Mostrar guia de histórico de fotos", "showExtendedBioFieldsTab": "Mostrar guia de campos biográficos estendidos", "showPhysicalDataTab": "Mostrar guia de dados físicos", "showRelatedSubjectsTab": "Mostrar guia de sujeitos relacionados", "showLocationsTab": "Mostrar guia de localizações", "showEntriesExitsTab": "Mostrar guia de entradas e saídas", "showEntryExitAuthorizationsTab": "Mostrar guia de autorizações de entrada e saída", "debugMode": "Modo de depuração", "streamingSamples": "Streaming de amostras", "general": "G<PERSON>", "general_widget": "Widget <PERSON>", "general_clock": "Clock Geral", "general_pass": "Pass Geral", "segment": "Segmentos", "devices": "Dispositivo", "security": "Segurança", "verification": "Verificação", "application_login": "Login do aplicativo", "audit_trail": "Trilha de Auditoria", "page_not_found": "Página não Encontrada", "unauthorized": "Não autorizado", "theme": "<PERSON><PERSON>", "subject_data_origen": "Origem dos Dados do Usuários", "api_gateway": "API Gateway", "actions_extra_data": "Ações - Dados Extra", "new_license": "Nova licença", "new_api_connection": "Nova conexão API", "update_api_connection": "Atualizar conexão API", "confirm_reduce_num_licenses": "Reduzir número de licenças", "update_ldap_connection": "Atualizar conexão LDAP", "new_ldap_connection": "Nova conexão LDAP", "tenant_licenses": "Licenças do Tenant", "client_data_source": "Origem dos Dados do Cliente", "applications": "Aplicativos", "application": "Aplicativo", "application_window": "Janela do aplicativo", "window_components": "Componentes da janela", "remove_component": "Excluir componente", "edit_window": "Janela de Edição", "save_window": "<PERSON><PERSON>", "remove_window": "Remover <PERSON>", "save_application": "Salvar aplicativo", "remove_application": "Remover aplicativo", "update_application": "Atualizar Aplicativo", "report_error": "Informar um erro", "data_sources": "Fontes de dados", "remove_data_source": "Remover Fonte de Dados", "update_data_source": "Atualizar <PERSON>", "add_parameters": "Adiciona<PERSON>", "create_data_source": "<PERSON><PERSON><PERSON>", "assign_application": "Atribuir Aplicação", "select_language": "Selecione um idioma", "selectLanguage": "Selecione um idioma", "selectFlow": "Selecione um fluxo", "flowSelection": "Seleção de fluxo", "catalogs": "Catálogos", "passwords_complexity": "Complexidade de Senhas", "prisonsSettings": "Configurações de Prisões", "belongings": "<PERSON>ten<PERSON><PERSON>", "belonging": "Pertença", "belonging_record": "Registro de pertença", "judicial_files": "Arquivos judiciais", "judicial_file": "Arquivo judicial", "prisons_visits_list": "Lista de visitas de prisões", "prisons_authorization_schedules": "Horários de autorização de prisões", "custom_partner_logo": "Logotipo personalizado", "cron_service": "Serviço Cron", "cron_service_config": "Configuração do serviço Cron", "cron_service_jobs": "Trabalhos do serviço Cron", "threshold_configs": "Configurações de limite", "inputTextAreaThreshold": "Limite de texto de entrada", "prometheusCredentials": "Credenciais do Prometheus", "authentication": "Autenticação", "pass_app_flow_types": "Pass - Tipos de fluxos de aplicações", "criminalistics_settings": "Configurações de Criminalistics", "services": "Serviços", "application_configs": "Configurações de Aplicativos", "pass_settings": "Configurações de Pass", "clock_settings": "Configurações de Clock"}, "content": {"username": "Nome de usuário", "password": "<PERSON><PERSON>", "fingerprint": "Impressão digital", "face": "Facial", "facial": "Facial", "iris": "<PERSON><PERSON>", "primary": "<PERSON><PERSON><PERSON><PERSON>", "secondary": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "name": "Nome", "key": "Chave", "value": "Valor", "drop_down": "Suspenso", "search": "Procurar", "date": "Data", "show_table": "Mostrar tabela", "option": "Opção", "known_position": "Posição conhecida", "no_match_search_db": "Sem correspondência, pesquise em todo o <PERSON>", "search_two_fp": "Busca de 2 impressão digital", "search_four_fp": "Busca de 4 impressão digital", "reason": "Razão", "search_two_iris": "Procurar 2 íris", "take_one_iris": "Pegue 1 íris", "take_two_iris": "Pegue 2 íris", "min_one_iris": "Mínimo 1 íris", "min_two_iris": "Mínimo 2 íris", "min_one_fp": "Mínimo 1 impressão digital", "min_two_fp": "Mínimo 2 impressões digitais", "min_three_fp": "Mínimo 3 impressões digitais", "min_four_fp": "Mínimo 4 impressões digitais", "take_one_fp": "Take 1 impressão digital", "take_two_fp": "Take 2 impressões digitais", "take_four_fp": "Take 4 impressões digitais", "add_profile_type": "Adicionar tipo de perfil", "enroller": "Registrador", "employee": "Funcionário", "intern": "Interno", "code": "Código", "default_profile_type": "Tipo de perfil padrão", "location": "Localização", "segment": "Segmento", "relation_location": "Relação com localização", "device": "Dispositivo", "relation_segment": "Relação com segmento", "segmented_search": "Pesquisa segmentada", "num_available_lic": "Número de licenças disponíveis", "show_licenses": "Mostrar licenças", "modify_key": "Modificar chave", "available": "Disponível", "selected": "Selecionado", "available_access_permissions": "Permissões de acesso disponíveis", "selected_access_permissions": "Permissões de acesso selecionadas", "url": "URL", "delete_entity": "Excluir entidade", "edit_entity": "Editar entidade", "verify": "Verificar", "related_with": "Relacionado com", "profile": "Perfil", "id_number": "Número de ID", "nothing": "<PERSON><PERSON><PERSON>", "role": "Role", "show_roles": "Mostrar roles", "show_users": "Mostrar usuarios", "show_assigments": "Mostrar asignaciones", "left_iris": "<PERSON><PERSON>", "right_iris": "<PERSON><PERSON> dire<PERSON>", "left_hand": "Mão esquerda", "right_hand": "Mão direita", "tenant": "Tenant", "select": "Selecionar", "enable_edit": "habilitar a edição", "errorTitle": "Erro", "successMessage": "Salvo ou excluído com sucesso!", "successTitle": "<PERSON><PERSON><PERSON><PERSON>", "download": "Descar<PERSON>", "tenant_name": "Nome do tenant", "show_tenants": "Mostrar tenants", "version": "Vers<PERSON>", "description": "Descrição", "new_version": "Nova versão", "show_access": "<PERSON><PERSON> acesso", "user_lock_time": "Tempo de Bloqueio", "expiration_time": "Tempo de expiração", "user_num_attempts": "Número de Tentativas", "is_enable_lock_user": "Habilitar bloqueio de usuários", "user_lock_time_by": "Tempo de Bloqueio em", "expiration_time_in": "Tempo de expiração em", "second": "<PERSON><PERSON><PERSON>", "minute": "<PERSON><PERSON><PERSON>", "hour": "<PERSON><PERSON>", "day": "<PERSON><PERSON>", "number_sessions": "Número de sessões", "action_name": "Nome da ação", "is_enable": "Está habilitado?", "new_action": "Nova Ação", "ecualize_images": "Equalizar foto", "identify_with_template": "Usando modelos em buscas", "copy_data": "<PERSON><PERSON><PERSON> da<PERSON>", "duplicate_version": "Este produto já existe no banco de dados.", "file_not_supported": "Arquivo não suportado. Os arquivos permitidos são .zip, .exe and .msi.", "successfully_removed": "Removido com sucesso.", "number": "Número", "allow": "<PERSON><PERSON><PERSON>", "device_used": "Dispositivos usados", "selectColumns": "Selecionar colunas", "columnsSelected": "Colunas selecionadas", "selectedColumns": "Colunas selecionadas", "biometricSearch": "Pesquisa biométrica", "Identification": "Identificação", "subjectIdentification": "Identificação de sujeito", "userIdentification": "Identificação de usuário", "schedule": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "start": "<PERSON><PERSON><PERSON>", "end": "Fim", "monday": "Segunda-feira", "tuesday": "Terça-feira", "wednesday": "Quarta-feira", "thursday": "Quin<PERSON>-f<PERSON>", "friday": "Sexta-feira", "saturday": "Sábado", "sunday": "Domingo", "subject": "<PERSON><PERSON><PERSON>", "subject_profile": "Perfil do sujeito", "user": "<PERSON><PERSON><PERSON><PERSON>", "user_role": "Função do usuário", "monday_to_friday": "Segunda a sexta", "customised": "Personalizado", "everyday": "Todos os dias", "type": "tipo", "to": "a", "level": "Nível", "must_update": "O usuário deve alterar a senha no próximo login.", "email": "Email", "repeat_password": "<PERSON><PERSON> a senha", "welcome": "<PERSON><PERSON>-vindo", "lastAccess": "<PERSON><PERSON><PERSON>", "select_reason": "Selecione um motivo", "new_password": "Nova Senha", "active": "Ativo", "inactive": "Inativo", "under_verification": "Em verificação", "documents": "Documentos", "profiles": "<PERSON><PERSON><PERSON>", "tattoos": "Tatuagens", "scars": "Cicatrizes", "select_camera": "Selecione a câmera", "enterNumID": "Digite o número de ID", "replace_image": "Substituir", "general": "Em geral", "server": "<PERSON><PERSON><PERSON>", "hardDrive": "Disco rígido", "ram": "RAM", "biometricServer": "Servidor biométrico", "subjectsDB": "Sujeitos BD", "numSubjects": "Número de sujeitos", "usersDB": "Usuários BD", "numUsers": "Número de usuários", "numRecords": "Número de registros", "status": "Estado", "noDataAvailable": "Nenhum dado disponível", "noPhotosAdded": "Nenhuma foto adicionada", "showing": "Mostrando", "of": "de", "records": "registros", "requiredFields": "Campos obrigatórios", "forgot_password": "Esque<PERSON>u a senha?", "oneTimePasswordVerification": "Verificação de senha única", "enterAccessKey": "Digite a chave de acesso", "accessKey": "<PERSON><PERSON>", "createSubjectToContinue": "Crie o sujeito para continuar", "takePhoto": "Tirar foto", "changePhoto": "Alterar foto", "rolledFingerprint": "Impressão digital rolada", "palm": "Palma", "minimumCoincidence": "Coincid<PERSON><PERSON>", "match": "Correspondência", "enroll": "Inscrever", "error_title": "Erro", "error_message_required_fields": "Por favor, preencha todos os campos obrigatórios.", "error_duplicate_location": "A localização já existe.", "error_duplicate_segment": "O segmento já existe.", "error_duplicate_device": "O dispositivo já existe.", "error_duplicate_option": "A opção já existe.", "some_duplicate_option": "Algumas opções já existem.", "success_message": "Processado sucesso!", "success_title": "<PERSON><PERSON><PERSON><PERSON>", "try_again": "Tentar novamente", "auto_enroll": "Auto inscrever", "option_not_valid": "Opção inválida", "no_reasons_configured": "Motivos não configurados", "data_inconsistent": "Dados inconsistentes. Por favor, atualize a página.", "reason_change": "Motivos para modificar", "showAdditionalTabs": "Mostrar guias adicionais", "showPrisonTab": "Mostrar guia de p<PERSON>ão", "showProfilePictureTab": "Mostrar guia de histórico de fotos", "enableDebugMode": "Habilitar modo de depuração", "autoDelete": "Exclusão Automática de Dados Biométricos", "api_credentials": "Credenciais API", "role_assigned": "As seguintes funções já foram atribuídas a alguns usuários:", "confirmation_continue": "¿Você tem certeza que quer continuar?", "tenant_not_valid": "Tenant <PERSON><PERSON><PERSON><PERSON><PERSON>.", "confirmation": "Confirmação", "show_actions": "Mostrar ações", "field_required": "Campo requerido", "options": "Opções", "capture_one_iris": "Capture 1 íris", "capture_two_iris": "Capture 2 íris", "one_fingerprint": "1 impressão digital", "two_fingerprints": "2 impressões digital", "three_fingerprints": "3 impressões digital", "four_fingerprints": "4 impressões digital", "timeoutGeneral": "Tempo limite geral (s.)", "timeoutInactivity": "Tempo de inatividade (s.)", "timeoutCapture": "Tempo de captura (s.)", "timeoutNotification": "Tempo de notificação (s.)", "timeoutStaticMessage": "Tempo de mensagem estática (s.)", "timeoutRequestsServer": "Tempo de solicitações ao servidor (s.)", "faceWidthMin": "Largura mínima do rosto (px.)", "matchAutoFaceCapture": "Captura facial automática (Verázial-ID Match)", "matchAutoFaceCaptureTime": "Tempo de captura facial automática (s.)", "autoPalmCapture": "Captura de palma automática", "showSuccess": "Aguardar mensagem de sucesso", "autoPalmCaptureTime": "Tempo de captura de palma automática (s.)", "minQualityFile": "Usar qualidade mínima para amostras de ficheiro", "matchAutoOn": "<PERSON><PERSON><PERSON> (Verázial-ID Match)", "language": "Idioma", "languageForAlerts": "Idioma para alertas", "debug": "Depuração", "logLevel": "Nível de log", "checkWithKonektor": "Verificar com Konektor", "version1": "Versão 1.0", "sipCompatibility": "Compatibilidade SIP", "admin_profile_type": "Perfil de administrador", "enroller_profile_type": "Perfil de registrador", "show_fields": "Mostrar campos", "show_data": "Mostrar dados", "show_jobs": "Mostrar trabalhos", "show_field_groups": "Mostrar grupos de campos", "extended_fields": "Campos estendidos", "quality_thumbnail": "Qualidade da miniatura", "config_quality_thumbnail": "Modificar a qualidade da miniatura", "config_size_thumbnail": "Modificar o tamanho máximo da miniatura", "size_thumbnail": "<PERSON><PERSON><PERSON> m<PERSON>xi<PERSON> da miniatura", "left_little": "<PERSON><PERSON><PERSON>", "left_ring": "<PERSON><PERSON><PERSON>", "left_middle": "<PERSON><PERSON><PERSON>", "left_fore": "Indicador esquerdo", "left_thumb": "<PERSON><PERSON>", "right_thumb": "<PERSON><PERSON>", "right_fore": "Indicador direito", "right_middle": "<PERSON><PERSON><PERSON>", "right_ring": "<PERSON><PERSON><PERSON>", "right_little": "<PERSON><PERSON><PERSON>", "left_little_ring": "<PERSON><PERSON><PERSON> e anelar es<PERSON>", "left_middle_fore": "Médio e indicador esquerdos", "thumbs": "<PERSON><PERSON><PERSON>", "right_middle_fore": "Médio e indicador direitos", "right_little_ring": "<PERSON><PERSON><PERSON> e anelar direitos", "left_upper_palm": "Palma superior esquerda", "left_lower_palm": "Palma inferior esquerda", "left_writer_palm": "Palma lateral esquerda", "right_upper_palm": "Palma superior direita", "right_lower_palm": "Palma inferior direita", "right_writer_palm": "Palma lateral direita", "second_search": "2da perquisa", "related_to": "Relacionado a", "values": "Valores", "delete_title": "Excluir", "delete_license": "Tem certeza de que deseja excluir a(s) licença(s) selecionada(s)?", "installer": "Installer", "upload": "Arquivo", "delete_application": "Tem certeza de que deseja excluir o aplicativo selecionado?", "delete_confirmation": "Tem certeza de que deseja excluir o registro selecionado?", "getSubjectInfoEndpoint": "Endpoint informações do sujeito", "getActionsInfoEndpoint": "Endpoint informações da ação", "mainStrongBioTech": "Tecnologia Biométrica Principal", "show_reasons": "Mostrar motivos", "show_profiles": "<PERSON>rar perfis", "show_segments": "Mostrar segmentos", "guid": "GUID", "serial_number": "Núm. Série", "mac": "MAC", "ip": "IP", "created_at": "<PERSON><PERSON><PERSON>", "show_locations": "Mostrar localizações", "show_relations": "Mostrar relacionamentos", "show_devices": "Mostrar dispositivos", "show_used_devices": "Mostrar dispositivos usados", "relation_add": "Relação adicionada", "relations_add": "Relações adicionadas", "cannot_remove_device_used": "O dispositivo não pode ser removido porque está vinculado a uma licença", "cannot_remove_segment_used": "O segmento não pode ser removido porque está vinculado a uma licença", "cannot_remove_location_used": "O localização não pode ser removido porque está vinculado a uma licença", "devices": "Dispositivos", "segments": "Segmentos", "relationships": "Relação", "field_type": "Tipo de campo", "field_already_exists": "Campo já existe", "parameter": "Parâmetro", "must_not_contain_spaces_or_special_characters": "Não deve conter espaços ou caracteres especiais", "input": "Input", "min_characters": "<PERSON><PERSON><PERSON>", "must_be_less_than_max": "Deve ser menor que o máximo", "max_characters": "Máximo de caracteres", "must_be_greater_than_min_and_0": "Deve ser maior que o mínimo e 0", "required": "Obrigatório", "dropdown": "Dropdown", "option_already_exists": "Opção já existe", "at_least_one_option_required": "Pelo menos uma opção é necessária", "toggle": "Toggle", "api_gateway": "API Gateway", "modify_api_gateway": "Modificar API Gateway", "theme": "<PERSON><PERSON>", "default_theme": "<PERSON><PERSON>", "show_themes": "Mostrar temas", "data_origen": "Origem dos dados", "ldap_host": "Host LDAP", "ldap_port": "Porta LDAP", "bind_dn": "Vincular DN", "search_base": "Base de pesquisa", "local": "Local", "external_api": "API externa", "ldap": "LDAP", "admin_user_guid": "GUID usuário administrador", "applied_to": "Aplicado a", "both": "Ambos", "criminology": "Imá<PERSON>s m<PERSON>", "nist_score": "Pontuação NIST", "other": "Outro", "assigned": "Atribu<PERSON><PERSON>", "used": "Usado", "used_by_application": "Por aplicativo", "maintenance": "Manutenção", "appsStatus": "Estado Apps", "mServiceStatus": "Estado MService", "secuential_enroll": "Inscrição sequencial", "collapse_all": "<PERSON><PERSON><PERSON><PERSON> tudo", "expand_all": "Expandir tudo", "total_licenses": "Total de licenças", "licenses": "Licenças", "application": "Aplicativo", "enable": "Habilitado", "last_used": "Último uso", "available_licenses": "Licenças disponíveis: ", "first_used": "<PERSON><PERSON> uso", "updated_at": "Atualizado em", "delete_single_license": "Tem certeza de que deseja excluir a licença selecionada?", "delete_multiple_license": "Tem certeza de que deseja excluir as licenças selecionadas?", "user_default_datasource": "Origem de dados de usuários padrão", "subject_default_datasource": "Origem de dados de sujeitos padrão", "show_api_connections": "Mostrar conexões API", "show_ldap_connections": "Mostrar conexões LDAP", "id": "ID", "api_token": "Token API", "admin_user_object_guid": "ID do usuário administrador", "admin_user_local_role_id": "Rol ID do usuário administrador", "api_methods": "Métodos API", "connection_name": "Nome", "allow_updating": "<PERSON><PERSON><PERSON><PERSON>", "allow_deleting": "Excluir", "allow_creating": "<PERSON><PERSON><PERSON>", "allow_reading": "<PERSON>r", "is_active": "Ativo", "user_subject_field_id": "Campo Index", "credentials": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "method": "<PERSON><PERSON><PERSON><PERSON>", "action": "Ação", "header_parameters_mapping": "Parâmetros de cabeçalho", "api_request_mapping": "Parâmetros de solicitação", "api_response_mapping": "Parâmetros de resposta", "source": "Origem", "target": "<PERSON><PERSON>", "HTTP_GET": "GET", "HTTP_POST": "POST", "HTTP_PUT": "PUT", "HTTP_DELETE": "DELETE", "API_READ": "LEITURA", "API_WRITE": "ESCRITA", "API_UPDATE": "ATUALIZAÇÃO", "API_CREATE": "CRIAÇÃO", "API_DELETE": "EXCLUSÃO", "info": "Informação", "show_methods": "<PERSON><PERSON>", "local_method_name": "Nome do método local", "camerasFromKonektor": "Câmeras do Konektor", "autoOnWidget": "<PERSON><PERSON><PERSON> (Widget)", "autoCaptureWidget": "Captura facial automática (Widget)", "autoCaptureTimeFacialWidget": "Tempo de captura facial automática (Widget)", "readingWaitTimePass": "Tempo de espera para leitura", "request_json_string": "Solicitação (formato JSON)", "response_json_string": "Resposta (formato JSON)", "licenses_used": "Licenças usadas", "verazial_loading": "O Verázial ID Pass está começando ....", "select_tech": "Selecione uma tecnologia", "dear": "Querid(a)", "select_app": "Por favor selecione o processo de inscrição que deseja abrir.", "select_process": "Por favor selecione o processo que deseja executar.", "success_login": "Indentidade encontrada", "error_login": "Identidade não encontrada", "loading_apps": "Carregando Aplicativos", "technology": "Tecnologia", "application_type": "Tipo de aplicação", "data_source_type": "<PERSON><PERSON><PERSON> de fonte", "data_source": "Origem dos dados", "attribute_name": "Nome do Atributo", "component_type": "Tipo de componente", "position": "Posição", "event": "Evento", "saved_successfully": "Salvo com sucesso!!!", "updated_successfully": "Atualizado com sucesso!!!", "removed_successfully": "Removido com sucesso!!!", "select_apps": "Selecione um aplicativo.", "full_path": "Caminho do aplicativo", "display_name": "Nome de exibição", "credentials_not_found": "Credenciais não encontradas na fonte de dados.", "window_title": "<PERSON><PERSON><PERSON><PERSON> da jane<PERSON>", "ventana": "<PERSON><PERSON>", "trigger_order": "Ordem de execução", "first_name": "Primeiro Nome", "second_name": "<PERSON><PERSON><PERSON>", "last_name": "Sobrenome", "connection_type": "Tipo de <PERSON>", "subjects": "Sujetos", "subject_application": "Aplicativos de usuário", "application_name": "Nome da Aplicação", "application_information": "Informações do Aplicativo", "application_identifier": "Identificador do aplicativo", "host": "Host", "fill_credential": "Adicionar credenciais e dados", "fields_not_valid": "Campos não válidos", "order": "Ordem", "loading_user_info": "Carregando informações do usuário...", "loading_app_data": "Carregando dados do aplicativo...", "loading_cred_ext": "Carregando credenciais - externo...", "loading_cred_int": "Carregando credenciais - interno...", "can_user_update": "<PERSON><PERSON><PERSON><PERSON> senha?", "method_not_implemented": "Método não implementado", "update_password": "Actualizar <PERSON>", "password_requirements": "Requisitos de <PERSON>a", "password_req_1": "12 caracteres", "password_req_2": "1 caracters maiúsculas", "password_req_3": "1 minúscula", "password_req_4": "1 caracter especiais", "password_req_5": "1 valor numérico", "confirm_change_password": "Tem certeza de que deseja alterar sua senha?", "update_credentials": "<PERSON><PERSON><PERSON><PERSON> creden<PERSON>is", "admin_application": "Aplicativo de administração", "user_application": "Aplicativo do usuário", "host_name": "Endereço IP/Nome do host", "administration": "Administração", "couldnt_connect_server": "Não foi possível conectar ao servidor.", "lastname": "Sobrenome", "num_id": "NumID", "display_component_user": "Exibir componente para o usuário?", "show_component": "Mostrar componente", "credentials_updated": "Credenciais atualizadas com sucesso!", "credentials_not_updated": "Não foi possível atualizar as credenciais.", "select_user": "Selecione pelo menos um usuário", "restarting_pass": "O Verázial ID Pass será recarregado em alguns segundos.", "must_user_update_credentials": "As credenciais devem ser inicializadas?", "no_applications_available": "Nenhum aplicativo disponível.", "apps_available": "Aplicativos disponíveis", "windows_available": "<PERSON><PERSON> di<PERSON>", "drag_drop": "Arraste e solte ", "the_window": "a janela", "message_remove": "Tem certeza de que deseja excluir ", "window_exist": "Esta janela já existe.", "publish": "Publicar", "published": "Publicado", "unpublish": "Não publicado", "no_published": "Não publicado", "window_used": "Esta janela já está no fluxo", "error_delete": "Erro ao tentar remover", "error_save": "Erro ao tentar salvar", "error_update": "Erro ao tentar atualizar", "error_web_app": "Web Login e firma digital permitem unicamente o Pass Web Extension.", "user_not_found": "Usuário não encontrado no Admin.", "report_login_error": "Informar um erro de login", "observation": "Observação", "no_datasource_available": "Nenhuma fonte de dados disponível", "edit": "<PERSON><PERSON>", "parameters": "Parâmetros", "message_update_data_source": "Tem certeza de que deseja atualizar ", "message_add_parameters": "Tem certeza de que deseja adicionar os novos parâmetros ao ", "initialised": "Inicializado?", "please_wait": "Por favor, aguarde", "is_starting": "Est<PERSON>", "allow_user_update_password": "Per<PERSON><PERSON> que o usuário atualize sua senha", "input_user_id": "Escreva o seu número de ID", "min_lowercase": "Deve conter pelo menos 1 letra minúscula", "min_uppercase": "Deve conter pelo menos 1 letra maiúscula", "min_digits": "Deve conter pelo menos 1 dígito", "min_special_char": "Deve conter pelo menos 1 caractere especial", "API_TOKEN": "Token API", "API_ENDPOINT_PARAMETER": "Parâmetro de Endpoint API", "API_USERNAME": "Nome de usuário API", "API_PASSWORD": "Senha API", "API_RESULT_PARAM": "Parâmetro de Resultado API", "API_SEARCH_FIELD": "Campo de Busca API", "LDAP_PASSWORD": "Senha LDAP", "LDAP_USERNAME": "Nome de usuário LDAP", "LDAP_BIND_DN": "DN de enlace LDAP", "LDAP_SEARCH_BASE": "Base de Busca LDAP", "LDAP_DOMAIN": "Dominio LDAP", "LDAP_PORT": "Puerto LDAP", "LOCAL_METHOD": "Metodo local", "LOGIN_USERNAME": "login - Nome de usuário", "LOGIN_PASSWORD": "Login - Sen<PERSON>", "month01": "j<PERSON><PERSON>", "month02": "<PERSON><PERSON>", "month03": "mar<PERSON><PERSON>", "month04": "abril", "month05": "maio", "month06": "junho", "month07": "julho", "month08": "agosto", "month09": "setembro", "month10": "outubro", "month11": "novembro", "month12": "dezemb<PERSON>", "availableActions": "Ações disponíveis", "submit": "Enviar", "flow": "Fluxo", "selectFlowToContinue": "Selecione um fluxo para continuar", "verification_1to1_enabled": "Verificação de ID", "LDAP_SSL": "SSL LDAP", "ssl_connection": "Conexão SSL", "group": "Grupo", "select_group": "Selecione um grupo", "new_related_subject": "Novo sujeito relacionado", "related_subject": "<PERSON><PERSON><PERSON> relacionado", "relationship": "Relacionamento", "isVisitor": "É visitante", "comments": "Comentários", "see_who_relates_to_this_subject": "Ver quem se relaciona com este sujeito", "subject_relationships": "Relacionamentos do sujeito", "new_catalog": "Novo catálogo", "new_location": "Nova localização", "regex": "Regex", "isPrisonsEnabled": "Prisons habilitado", "prisonerProfileId": "ID do perfil de prisioneiro", "showBelongingsTab": "Mostrar guia de pertences", "showJudicialFileTab": "Mostrar guia de arquivo judicial", "belongings": "<PERSON>ten<PERSON><PERSON>", "new_belonging": "Nova pertença", "new_belonging_record": "Novo registro de pertença", "belongingType": "Tipo", "belongingDescription": "Descrição", "registrationDate": "Data de registro", "subjectReceptionSignatureDate": "Data de assinatura de recepção do sujeito", "subjectReceptionSignatureTech": "Tecnologia de assinatura de recepção do sujeito", "subjectReturnSignatureDate": "Data de assinatura de retorno do sujeito", "subjectReturnSignatureTech": "Tecnologia de assinatura de retorno do sujeito", "receptionUserSignatureDate": "Data de assinatura de recepção do usuário", "receptionUserSignatureTech": "Tecnologia de assinatura de recepção do usuário", "receptionUserSignatureNumId": "Número de ID de assinatura de recepção do usuário", "returnUserSignatureDate": "Data de assinatura de retorno do usuário", "returnUserSignatureTech": "Tecnologia de assinatura de retorno do usuário", "returnUserSignatureNumId": "Número de ID de assinatura de retorno do usuário", "new_record": "Novo registro", "returned": "Retornado", "received": "Recebido", "registered": "Registrado", "signature_of": "Assinatura do sujeito", "signature_responsible_of": "Assinatura do responsável", "subject_reception_signature": "Assinatura de recepção do sujeito", "user_reception_signature": "Assinatura de recepção do usuário", "subject_return_signature": "Assinatura de retorno do sujeito", "user_return_signature": "Assinatura de retorno do usuário", "user_coincidence_signature": "Assinatura de confirmação de coincidência", "sign": "<PERSON><PERSON><PERSON>", "judicial_file_fields": "Campos de arquivo judicial", "new_schedule": "Novo horário", "edit_schedule": "<PERSON><PERSON>", "roleId": "ID do papel", "locationId": "ID da localização", "details": "<PERSON><PERSON><PERSON>", "configuration": "Configuração", "uploaded": "Carregado", "partner_login_logo_enabled": "Habilitar logotipo personalizado na tela de login", "partner_topbar_logo_enabled": "Habilitar logotipo personalizado na barra superior", "entry-exit-control-role-relation-restrictions": "Restrições de relação de perfil para controle de entrada e saída", "show-entry-exit-control-role-relation-restrictions": "Mostrar restrições de relação de perfis para controle de entrada e saída", "entry-exit-settings": "Configurações de entrada e saída", "new_restriction": "Nova restrição", "new_role_relation_restriction": "Nova restrição de relação de papel", "entryRole": "Papel de entrada", "subjectWithRole": "Sujeito com perfil", "relatedRole": "Papel relacionado", "hasToHaveARelatedSubjectWithRole": "Deve ter um sujeito relacionado com perfil", "new_transfer": "Nova transferência", "new_transfer_auth": "Nova autorização de transferência", "edit_transfer_auth": "Editar autorização de transferência", "execute_transfer_exit": "Executar saída de transferência", "execute_transfer_entry": "Executar entrada de transferência", "execute": "Executar", "execute_schedule": "Executar", "remove_schedule": "Remover", "authCode": "Código de autenticação", "authReason": "Motivo de transferência", "authRegistrationDate": "Data de registro de autenticação", "authExpirationDate": "Data de expiração de autenticação", "originLocation": "Localização de origem", "destinyLocation": "Localização de destino", "plannedDepartureDateTime": "Data e hora de partida planejada", "plannedArrivalDateTime": "Data e hora de chegada planejada", "actualDepartureDateTime": "Data e hora de partida atual", "actualArrivalDateTime": "Data e hora de chegada atual", "listOfPrisoners": "Lista de prisioneiros", "listOfResponsiblePersonel": "Lista de pessoal responsável", "listOfResponsible": "Lista de responsáveis", "authDate": "Data de autenticação", "authUser": "Usuário de autenticação", "isCompleted": "Est<PERSON> completo", "isCancelled": "<PERSON>st<PERSON>", "cancelDate": "Data de cancelamento", "cancelUser": "Usuário de cancelamento", "createdBy": "<PERSON><PERSON><PERSON> por", "updatedBy": "Atualizado por", "createdAt": "C<PERSON><PERSON> em", "updatedAt": "Atualizado em", "obtainedAt": "Data de recopilado", "signatures": "<PERSON><PERSON><PERSON>", "enter_numId_to_verify": "Digite o número de ID para verificar", "signedOn": "<PERSON><PERSON><PERSON> em", "authSignature": "Assinatura de autenticação", "transferExitSignature": "Assinatura de saída de transferência", "transferEntrySignature": "Assinatura de entrada de transferência", "authUserSignatureTech": "Tecnologia de assinatura de usuário de autenticação", "subjectWhoAuthrizes": "<PERSON>jeito que autoriza", "transferSubject": "Sujeito de transferência", "transferResponsible": "Responsável de transferência", "cancelSignature": "Assinatura de cancelamento", "cancelUserSignatureTech": "Tecnologia de assinatura de usuário de cancelamento", "subjectWhoCancels": "<PERSON><PERSON><PERSON> que cancela", "cancelReason": "Motivo de cancelamento", "cancelObservation": "Observação de cancelamento", "prisonerTabs": "<PERSON><PERSON><PERSON> <PERSON> pris<PERSON>iro", "transferAuthConfig": "Configuração de autorização de transferência", "biometricSignaturesConfig": "Configuração de assinaturas biométricas", "transferAuthExpiration": "Expiração de autorização de transferência", "transferAuthExpirationConfig": "Configuração de expiração de autorização de transferência", "responsibleSubjectsRoles": "<PERSON><PERSON><PERSON> de sujeitos responsáveis", "showTransferAuthDetails": "Mostrar detalhes de autorização de transferência", "transfer_auth_details_fields": "Campos de detalhes de autorização de transferência", "authorized_subject_roles_to": "Perfis de sujeitos autorizados para", "authorized_subject_roles_for": "Perfis de sujeitos autorizados para", "authorized_subject_roles_to_sign_on": "Perfis de sujeitos autorizados para assinar em", "belongingsReception": "Recepção de pertences", "belongingsReturn": "Retorno de pertences", "authorizeTransferAuths": "Autorizar autorizações de transferência", "cancelTransferAuths": "Cancelar autorizações de transferência", "authorizePrisonerEntryExit": "Autorizar entrada/saída de prisioneiro", "signPrisonerEntryExit": "Assinar entrada/saída de prisioneiro", "biometricSignaturesAuthorizedSubjectRoles": "Perfis de sujeitos autorizados para assinaturas biométricas", "max_inside_time": "Tempo máximo dentro (hh:mm)", "inside_time": "Tempo dentro (hh:mm)", "signature": "Assinatura", "new_authorization": "Nova autorização", "edit_authorization": "Editar autorização", "authStartDateTime": "Data e hora de início de autenticação", "authEndDateTime": "Data e hora de término de autenticação", "entry": "Entrada", "exit": "<PERSON><PERSON><PERSON>", "showEntryExitAuthDetails": "Mostrar detalhes de autenticação de entrada/saída", "entry_exit_auth_details_fields": "Campos de detalhes de autenticação de entrada/saída", "isRequiredWithinSchedule": "É necessário dentro do hor<PERSON>rio", "requiredSchedule": "<PERSON><PERSON><PERSON><PERSON>", "alertIfAllPerformedWithinSchedule": "Alerta se todos forem realizados dentro do horário", "alertIfNotPerformedWithinSchedule": "Alerta se não for realizado dentro do horário", "alertIfPerformedOutsideSchedule": "Alerta se realizado fora do horário", "usersToAlert": "Usuários para alertar", "new_job": "Novo trabalho", "edit_job": "<PERSON><PERSON>", "time": "Tempo", "timeIn": "Tempo en", "onlySelectOneSchedule": "Selecione apenas um horário", "limitReached": "Limite al<PERSON>", "completed_on_time": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "completed_late": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "on_time": "Pendente", "late": "Pendente", "timeInit": "Hora de início", "timeEnd": "<PERSON>ra de término", "timeInitEnd": "Hora de início e término", "dailyWithinTimeRange": "Diariamente dentro do intervalo de tempo", "presEnterToAdd": "Pressione Enter para adicionar", "record_already_exists": "Registro j<PERSON>e", "showInMenu": "Mostrar no menu", "triggerInitJobScheduler": "Disparar inicialização do agendador de trabalhos", "scheduleJob": "<PERSON><PERSON><PERSON>", "removeJobSchedule": "Remover agendamento de trabalho", "specificDateTimeStart": "Data e hora específicas de início", "dateTimeStart": "Data e hora de início", "baseIdentifier": "Identificador", "appRegistry": "Aplicação", "errorMessageToDisplay": "Mensagem de erro a ser exibida", "translations": "Traduções", "show_translations": "Mostrar traduções", "languageCode": "Código do idioma", "inputTextAreaThreshold": "Número mínimo de caracteres para mostrar a área de texto", "userSubjectLazyLoadThreshold": "Número mínimo de caracteres para carregar o sujeito/usuário con lazy load", "up": "Activo", "down": "Inactivo", "current_password": "<PERSON><PERSON> atual", "show_app_flow_types": "Mostrar tipos de fluxo", "flow_types": "Tipos de fluxos", "allowCopyPaste": "<PERSON><PERSON><PERSON> copiar/colar", "datasource_link_field": "Campo de ligação da fonte de dados", "name_translations": "Traduções de nome", "showFilesTab": "Mostrar guia de arquivos", "showSubjectFileTypesList": "Mostrar lista de tipos de arquivos de sujeito", "subjectFileTypes": "Tipos de arquivos de sujeito", "showSubjectFileGroupList": "Mostrar lista de grupos de arquivos de sujeito", "subjectFileGroups": "Grupos de arquivos de sujeito", "noSubjectFileGroupsConfigured": "Nenhum grupo de arquivos de sujeito configurado, por favor contate o administrador.", "evidenceFilesTab": "<PERSON><PERSON><PERSON>", "comparatorTab": "<PERSON><PERSON><PERSON> comparador", "showEvidenceFileTypesList": "Mostrar lista de tipos de ficheiro de prova", "evidenceFileTypes": "Tipos de arquivos de prova", "showEvidenceFileGroupList": "Mostrar lista de grupos de arquivos de prova", "evidenceFileGroups": "Grupos de arquivos de prova", "noEvidenceFileGroupsConfigured": "Nenhum grupo de arquivos de prova configurado, por favor contate o administrador.", "uploadNewFile": "Carregar novo arquivo", "no_files_available": "Nenhum arquivo disponível", "alias": "<PERSON><PERSON>", "subjectFileRestrictions": "Restrições de arquivos de sujeito", "acceptedFileTypes": "Tipos de arquivos aceitos", "acceptedFileTypesToolTip": "Lista separada por vírgula de padrões para restringir os tipos de arquivos permitidos. Pode ser qualquer combinação dos tipos MIME (como 'image/*') ou das extensões de arquivo (como '.jpg').", "maxFileSize": "<PERSON>an<PERSON> máximo de arquivo (bytes)", "maxFileSizeTooltip": "Tamanho máximo de arquivo em bytes.", "maxResults": "<PERSON><PERSON> Re<PERSON>tados", "maxResultsTooltip": "Numero entre 0-100", "coincidencePercentage": "Percentual mínimo de coincidência", "coincidencePercentageTooltip": "Numero entre 0-100", "dragAndDropFilesHereToUpload": "Arraste e solte os arquivos aqui para carregar", "pending": "Pendente", "completed": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "file": "Arquivo", "remove": "Remover", "preview": "Pré-visualização", "mimeType": "Tipo de arquivo", "uploadFile": "Carregar arquivo", "selectOption": "Selecionar opção", "isCriminalisticsEnabled": "Criminalistics habilitado", "isEnrollByFileEnabled": "Habilitar enrolamento por arquivo", "isManageSubjectWithoutVerificationEnabled": "Habilitar gestão de sujeito sem verificação", "subjectMustExistToAccessThisData": "O sujeito deve existir para acessar esses dados", "unavailable": "Indisponível", "liveSamples": "Amostras ao vivo", "fileSamples": "Amostras de arquivo", "fileAndLiveSamples": "Amostras de arquivo e ao vivo", "captureMode": "<PERSON><PERSON> de captura (Enroll)", "captureModeMatch": "<PERSON><PERSON> (Match)", "criminalistic_case_details_fields": "Campos de informação adicionais para a criação de casos", "selectCase": "Selecionar Caso(s)", "selectSubject": "Selecion<PERSON> Sujeito(s)", "selectUser": "<PERSON><PERSON><PERSON><PERSON><PERSON>(s)", "no_created_cases": "Nenhum arquivo criado", "no_created_comments_case": "Não tem comentários relacionados com este caso", "no_created_evidences": "Nenhum evidência criada", "no_created_coincidences": "Nenhum coincidência criada", "add_from_evidences": "Por favor, adicione-a na seção de evidências", "case_must_exist_to_access_data": "O caso deve existir para acessar esses dados", "isPassEnabled": "Pass habilitado", "isClockEnabled": "Clock habilitado", "enroll_live_samples": "Enrolar amostras ao vivo", "enroll_file_samples": "Enrolar amostras por arquivo", "selectImage": "Selecionar imagem", "index": "<PERSON><PERSON><PERSON>", "sample_type": "Tipo de amostra", "UNKNOWN": "Desconhecido", "LEFT_IRIS": "<PERSON><PERSON>", "RIGHT_IRIS": "<PERSON><PERSON> dire<PERSON>", "LEFT_THUMB": "<PERSON><PERSON>", "LEFT_INDEX_FINGER": "<PERSON><PERSON><PERSON> es<PERSON>", "LEFT_MIDDLE_FINGER": "<PERSON><PERSON>", "LEFT_RING_FINGER": "<PERSON><PERSON><PERSON>", "LEFT_LITTLE_FINGER": "<PERSON><PERSON><PERSON>", "RIGHT_THUMB": "<PERSON><PERSON>", "RIGHT_INDEX_FINGER": "<PERSON><PERSON><PERSON> direito", "RIGHT_MIDDLE_FINGER": "<PERSON><PERSON> dire<PERSON>", "RIGHT_RING_FINGER": "<PERSON><PERSON><PERSON>", "RIGHT_LITTLE_FINGER": "<PERSON><PERSON><PERSON>", "UP_LEFT_FACE": "Cara superior esquerda", "UP_FACE": "Cara superior", "UP_RIGHT_FACE": "Cara superior direita", "RIGHT_FACE": "Cara direita", "DOWN_RIGHT_FACE": "Cara inferior direita", "DOWN_FACE": "Cara inferior", "DOWN_LEFT_FACE": "Cara inferior esquerda", "LEFT_FACE": "Cara esquerda", "FRONTAL_FACE": "Cara frontal", "LEFT_UPPER_PALM": "Mão esquerda superior", "RIGHT_UPPER_PALM": "Mão direita superior", "LEFT_LOWER_PALM": "Mão esquerda inferior", "RIGHT_LOWER_PALM": "Mão direita inferior", "LEFT_LATERAL_PALM": "Mão esquerda lateral", "RIGHT_LATERAL_PALM": "Mão direita lateral", "LEFT_INDEX_FINGER_ROLLED": "<PERSON><PERSON><PERSON> esquerdo rolado", "LEFT_MIDDLE_FINGER_ROLLED": "<PERSON><PERSON> rolado", "LEFT_RING_FINGER_ROLLED": "<PERSON><PERSON><PERSON> rolado", "LEFT_LITTLE_FINGER_ROLLED": "<PERSON><PERSON><PERSON> r<PERSON>", "RIGHT_INDEX_FINGER_ROLLED": "Índice direito rolado", "RIGHT_MIDDLE_FINGER_ROLLED": "<PERSON>o direito rolado", "RIGHT_RING_FINGER_ROLLED": "<PERSON><PERSON><PERSON> direito rolado", "RIGHT_LITTLE_FINGER_ROLLED": "<PERSON><PERSON><PERSON> rolado", "LEFT_THUMB_ROLLED": "<PERSON><PERSON> es<PERSON>do rolado", "RIGHT_THUMB_ROLLED": "<PERSON><PERSON> direito rolado", "enroll_by_file_mappings": "Mapeamento de enrolamento por arquivo", "show_mappings": "Mostrar mapeamento", "items": "<PERSON><PERSON>", "finger": "<PERSON><PERSON>", "FINGER": "<PERSON><PERSON>", "new_mapping": "Novo mapeamento", "add": "<PERSON><PERSON><PERSON><PERSON>", "add_sample_mapping": "Adicionar map<PERSON> de amostra", "select_sample_mapping": "Selecionar mapeamento de amostra", "": ""}, "requiredActionStatus": {"completed_on_time": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "completed_late": "<PERSON><PERSON><PERSON><PERSON><PERSON> tarde", "on_time": "Pendente", "late": "Pendente no concluído"}, "reports": {"export": "Exportar PDF", "export2": "Exportar Excel", "tMain2": "of users", "tMain3": "of subjects", "tMain4": "of samples", "tMain5": "Number", "tMain6": "Identifications by technology", "tMain7": "Verifications by technology", "tMain8": "Average", "tMain9": "of pictures", "tUsers1": "User list", "tUsers2": "Users by profile", "tUsers3": "Users by location", "tUsers4": "Users by gender", "tUsers5": "Users and pictures", "tUsers6": "Identifications by technology", "tUsers7": "Verifications by technology", "tSub1": "Subject list", "tSub2": "Subjects by profile", "tSub3": "Subjects by location", "tSub4": "Subjects by gender", "tSub5": "Subjects and pictures", "tSub6": "Identifications by technology", "tSub7": "Verifications by technology", "tV1": "Verification requests by technology", "tV2": "Verification results by technology", "tV3": "Verification requests by location", "tV4": "Verification results by location", "tI1": "Identification requests by technology", "tI2": "Identification results by technology", "tI3": "Identification requests by location", "tI4": "Identification results by location", "tR1": "Average time by technology and action", "tR2": "Average time by action", "tR3": "Average time by technology", "tR4": "Average time extended", "tTo": "to", "tOf": "of", "tLocations": "locations", "tActions1": "Action list", "tActions2": "Verifications", "tActions3": "Identifications", "tActions4": "Enrollments", "tActions5": "Deletions", "tActions6": "Updates", "tActions7": "<PERSON><PERSON>", "tActions8": "<PERSON><PERSON>", "tActions9": "Pictures", "tActions10": "<PERSON><PERSON>", "tActions11": "Devices", "tActions12": "verifications", "tActions13": "identifications", "tActions14": "subjects enrolled", "tActions15": "subjects deleted", "tActions16": "subjects update", "tActions17": "samples added", "tActions18": "samples deteled", "tActions19": "pictures added", "tActions20": "logins with password", "tActions21": "devices not detected", "tAudit1": "Audited actions", "noData": "No data available", "emissionDate": "Report emission date", "customer": "Customer", "reportType": "Report type", "initDate": "Init date", "endDate": "End date", "actionType": "Action type", "reportTitle1": "Report issuer and receiver", "reportTitle2": "Applied filters", "reportMetadata": "Report generated using Verázial ID Reports", "reportColumn1": "Application", "reportColumn2": "Location", "reportColumn3": "Segment", "reportColumn4": "<PERSON><PERSON>", "reportColumn5": "Action", "reportColumn6": "Executor", "reportColumn7": "Profile", "reportColumn8": "Receiver", "reportColumn9": "Profile", "reportColumn10": "Old value", "reportColumn11": "New value", "reportColumn12": "Date", "reportColumn13": "Action result", "reportColumn14": "Dad<PERSON> adiciona<PERSON>", "reportNameAudit": "Audit trail of actions", "reportIdentifier": "Report identifier"}, "ms_errors": {"0": "<PERSON><PERSON>", "404": "Não encontrado", "500": "Erro interno do servidor", "522": "<PERSON>rro <PERSON>", "561": "Não autorizado", "1000": "Ocorreu um erro no banco de dados", "1001": "A tabela não existe", "1002": "Os tenants não podem ser migrados", "2000": "Este sujeito já existe no banco de dados", "2001": "O sujeito não existe no sistema", "2002": "Número de sessões excedido", "2003": "<PERSON><PERSON><PERSON><PERSON> blo<PERSON>", "2004": "Token expirado", "2005": "To<PERSON> in<PERSON>lid<PERSON>", "2006": "Token usado", "2007": "Token indisponível", "3000": "Este campo é obrigatório", "3001": "Existe um registro com as mesmas informações", "3002": "Não pode ser removido", "3003": "Formato de versão inválido. Deve ser v{major}.{minor}.{path}, por exemplo v1.0.0", "4001": "Sem licenças disponíveis", "4002": "Licença não habilitada", "5001": "Tenant não encontrado", "5002": "Tenant não habilitado", "6000": "Erro inesperado no serviço externo", "6001": "Método de serviço externo não configurado", "6002": "Método de serviço externo não configurado corretamente"}, "mservices": {"v-mservice-server-monitoring": "Monitoramento do servidor", "v-mservice-system-settings": "Manager", "v-mservice-tenant-biom-config": "Configuração biométrica do tenant", "v-mservice-subject": "Subjects", "v-mservice-auth": "Autenticação", "v-mservice-tenant-db-config": "Configuração do banco de dados do tenant", "v-mservice-user": "Usuários", "v-mservice-apigateway-grpc": "API Gateway gRPC", "v-mservice-biom-mngr": "Gerenciador Biométrico", "v-mservice-biom-neurotec13": "Facial & Palm", "v-mservice-cron": "Serviço Cron", "v-mservice-actions": "Ações de auditoria", "v-mservice-tenant": "Tenant", "v-mservice-actionsV2": "Ações de auditoria V2", "v-mservice-storage": "Storage", "v-mservice-biom-neurotec": "Iris & Fingerprint", "neurotechnology9": "Neurotechnology 9", "v-mservice-mail-dispatcher": "Mail Dispatcher", "v-mservice-api-binder": "API Binder", "neurotechnology13": "Neurotechnology 13", "v-mservice-apigateway": "API Gateway", "grafana": "<PERSON><PERSON>", "loki": "<PERSON>", "fluent-bit": "Fluent Bit", "prometheus": "Prometheus", "tempo": "Tempo", "v-mservice-ext-credentials": "Credenciais Externas", "v-mservice-biographic": "<PERSON><PERSON><PERSON>", "v-mservice-discovery": "Discovery", "postgres": "Postgres"}, "status": {"created": "<PERSON><PERSON><PERSON>", "authorized": "Autorizado", "cancelled": "Cancelado", "in_progress": "Em progresso", "completed": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "expired": "<PERSON><PERSON><PERSON>"}, "cron-services": {"roll-call-alerts": "Alertas de chama<PERSON>"}, "prime_ng": {"accept": "<PERSON>m", "addRule": "Ad<PERSON><PERSON><PERSON>", "am": "am", "apply": "Aplicar", "cancel": "<PERSON><PERSON><PERSON>", "choose": "Escolha", "chooseDate": "Escolha Data", "chooseMonth": "<PERSON><PERSON><PERSON><PERSON>", "chooseYear": "Escolha <PERSON>", "clear": "Limpar", "close": "<PERSON><PERSON><PERSON>", "completed": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "contains": "<PERSON><PERSON><PERSON>", "custom": "Customizado", "dateAfter": "Data é posterior", "dateBefore": "Date é anterior", "dateFormat": "dd/mm/yy", "dateIs": "Data é", "dateIsNot": "Data não é", "dayNames": ["Domingo", "Segunda", "<PERSON><PERSON><PERSON>", "Quarta", "<PERSON><PERSON><PERSON>", "Sexta", "Sábado"], "dayNamesMin": ["Do", "Se", "Te", "Qa", "Qi", "Sx", "Sa"], "dayNamesShort": ["Dom", "Seg", "<PERSON><PERSON>", "<PERSON>ua", "<PERSON>ui", "Sex", "<PERSON><PERSON><PERSON>"], "emptyFilterMessage": "Nenhum resultado encontrado", "emptyMessage": "Nenhuma opção disponível", "emptySearchMessage": "Nenhum resultado encontrado", "emptySelectionMessage": "Nenhum item selecionado", "endsWith": "Termina com", "equals": "Igual", "fileSizeTypes": ["B", "KB", "MB", "GB", "TB", "PB", "EB", "ZB", "YB"], "filter": "Filtro", "firstDayOfWeek": 0, "gt": "<PERSON><PERSON> que", "gte": "<PERSON><PERSON> que ou igual a", "lt": "<PERSON><PERSON> que", "lte": "<PERSON>or que ou igual a", "matchAll": "Match All", "matchAny": "Match Any", "medium": "Médio", "monthNames": ["Janeiro", "<PERSON><PERSON>", "Março", "Abril", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "Agosto", "Setembro", "Out<PERSON>ro", "Novembro", "Dezembro"], "monthNamesShort": ["Jan", "<PERSON>v", "Mar", "Abr", "<PERSON>", "Jun", "Jul", "Ago", "Set", "Out", "Nov", "<PERSON>z"], "nextDecade": "Década Seguinte", "nextHour": "<PERSON><PERSON>", "nextMinute": "<PERSON><PERSON>", "nextMonth": "<PERSON><PERSON><PERSON>", "nextSecond": "<PERSON><PERSON><PERSON>", "nextYear": "<PERSON><PERSON>", "noFilter": "Sem filtro", "notContains": "Não contém", "notEquals": "Di<PERSON><PERSON>", "now": "<PERSON><PERSON><PERSON>", "passwordPrompt": "Digite uma senha", "pending": "Pendente", "pm": "pm", "prevDecade": "Década Anterior", "prevHour": "<PERSON><PERSON>", "prevMinute": "<PERSON><PERSON>", "prevMonth": "<PERSON><PERSON><PERSON>", "prevSecond": "<PERSON><PERSON><PERSON>", "prevYear": "<PERSON><PERSON>", "reject": "Não", "removeRule": "Remover Regra", "searchMessage": "{0} resultados disponí<PERSON>is", "selectionMessage": "{0} itens selecionados", "showMonthAfterYear": false, "startsWith": "Começa com", "strong": "Forte", "today": "Hoje", "upload": "Upload", "weak": "Fraco", "weekHeader": "Se<PERSON>", "aria": {"cancelEdit": "<PERSON><PERSON><PERSON>", "close": "<PERSON><PERSON><PERSON>", "collapseLabel": "Colapso", "collapseRow": "<PERSON><PERSON>", "editRow": "<PERSON><PERSON>", "expandLabel": "Expandir", "expandRow": "<PERSON><PERSON>", "falseLabel": "<PERSON><PERSON><PERSON>", "filterConstraint": "Restrição de Filtro", "filterOperator": "Operador de Filtro", "firstPageLabel": "Primeira Página", "gridView": "Exibição em Grade", "hideFilterMenu": "Esconder Menu de Filtro", "jumpToPageDropdownLabel": "Pular para Menu da Página", "jumpToPageInputLabel": "Pular para Campo da Página", "lastPageLabel": "Última Página", "listView": "Exibição em Lista", "moveAllToSource": "Mover <PERSON> para Fonte", "moveAllToTarget": "Mover Todos para Alvo", "moveBottom": "Mover para Final", "moveDown": "Mover para Baixo", "moveTop": "Mover para Topo", "moveToSource": "Mover para Fonte", "moveToTarget": "Mover para Alvo", "moveUp": "Mover para Cima", "navigation": "Navegação", "next": "Se<PERSON><PERSON>", "nextPageLabel": "<PERSON><PERSON><PERSON><PERSON>", "nullLabel": "Não selecionado", "otpLabel": "Insira o caractere da senha de uso único {0}", "pageLabel": "<PERSON><PERSON><PERSON><PERSON> {page}", "passwordHide": "Esconder a senha", "passwordShow": "<PERSON><PERSON> senha", "previous": "Anterior", "previousPageLabel": "Página Anterior", "removeLabel": "Remover", "rotateLeft": "<PERSON><PERSON><PERSON>rda", "rotateRight": "Girar à Direita", "rowsPerPageLabel": "<PERSON><PERSON> por página", "saveEdit": "<PERSON><PERSON>", "scrollTop": "Rolar para Topo", "selectAll": "Todos itens selecionados", "selectLabel": "Selecione", "selectRow": "<PERSON><PERSON>", "showFilterMenu": "Mostrar Menu de Filtro", "slide": "<PERSON><PERSON><PERSON>", "slideNumber": "{slideNumber}", "star": "1 estrela", "stars": "{star} estrelas", "trueLabel": "<PERSON><PERSON><PERSON><PERSON>", "unselectAll": "Nenhum item selecionado", "unselectLabel": "<PERSON><PERSON><PERSON>", "unselectRow": "Linha Não Selecionada", "zoomImage": "Ampliar Imagem", "zoomIn": "<PERSON><PERSON>", "zoomOut": "Menos Zoom"}}}