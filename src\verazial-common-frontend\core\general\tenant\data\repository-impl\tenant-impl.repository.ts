import { Injectable } from "@angular/core";
import { TenantRepository } from "../../domain/repository/tenant.repository";
import { LocalStorageService } from "src/verazial-common-frontend/core/services/local-storage.service";
import { TenantStatus } from "../../common/models/tenant-status.enum";
import { TenantEntity } from "../../domain/entity/tenant.entity";
import { TenantMapper } from "../mapper/tenant.mapper";
import { environment } from "src/environments/environment";
import { FailureResponse } from "src/verazial-common-frontend/core/classes/failure-response.model";
import { SuccessResponse } from "src/verazial-common-frontend/core/general/common/models/success-response";
import { CoreTenantServiceClient } from "src/verazial-common-frontend/core/generated/tenant/TenantServiceClientPb";
import { OffsetLimit } from "src/verazial-common-frontend/core/generated/util_pb";
import { RequestUpdateStatus, TenantGrpcModel, TenantRequest } from "src/verazial-common-frontend/core/generated/tenant/tenant_pb";
import { toEnum } from "src/verazial-common-frontend/core/util/to-enum";
import { TenantStatusEnumGrpc } from "src/verazial-common-frontend/core/generated/tenant/tenant_status_pb";
import { GrpcStreamInterceptor } from "src/verazial-common-frontend/core/helpers/grpc-stream.interceptor";
import { HttpClient } from "@angular/common/http";
import { GrpcLicenseStreamInterceptor } from "src/verazial-common-frontend/core/helpers/grpc-license-stream.interceptor";
import { AddTenantRequestEntity } from "../../domain/entity/add-tenant-request.entity";
import { AddTenantRequestMapper } from "../mapper/add-tenant-request.mapper";
import { EncryptionService } from "src/verazial-common-frontend/core/services/encryptionService";

@Injectable({
    providedIn: 'root',
})
export class TenantRepositoryImpl extends TenantRepository {
    tenantMapper = new TenantMapper();
    addTenantRequestMapper = new AddTenantRequestMapper();
    localStorageService = new LocalStorageService(new EncryptionService());

    constructor(
        private httpClient: HttpClient,
    ) {
        super();
    }

    /**
     * Add a new tenant
     * @param params TenantEntity
     * @returns Promise<TenantEntity>
     */
    override addTenant(params: { tenant: AddTenantRequestEntity; }): Promise<TenantEntity> {

        let request = this.addTenantRequestMapper.mapTo(params.tenant);

        let coreTenantServiceClient = new CoreTenantServiceClient(`${this.localStorageService.getApiGatewayURL() ?? environment.grpcApiGateway}`, null,
            { 'streamInterceptors': [environment.token == 'license' ? new GrpcLicenseStreamInterceptor(this.httpClient) : new GrpcStreamInterceptor()] });

        // let metadata = { Authorization: `Bearer ${this.localStorage.getToken()}`}

        return new Promise((resolve, reject) => {
            coreTenantServiceClient.addTenant(request, null, (err, response) => {
                if (err) {
                    let failure: FailureResponse = {
                        code: err.code,
                        message: err.message,
                    };
                    reject(failure);
                } else {
                    if (response.hasFailure()) {
                        let failure: FailureResponse = {
                            code: response.getFailure()?.getCode(),
                            message: response.getFailure()?.getMessage(),
                        };
                        reject(failure);
                    } else {
                        resolve(this.tenantMapper.mapFrom(response.getTenantmodel()!));
                    }
                }
            });
        });
    }

    /**
     * Get all tenant with offset and limit
     * @param params  offset: number; limit: number;
     * @returns Promise<TenantEntity[]
     */
    override getAllTenants(params: { offset: number; limit: number; }): Promise<TenantEntity[]> {
        let request = new OffsetLimit();
        request.setOffset(params.offset);
        request.setLimit(params.limit);

        let tenants: TenantEntity[] = [];

        let coreSubjectServiceClient = new CoreTenantServiceClient(`${this.localStorageService.getApiGatewayURL() ?? environment.grpcApiGateway}`, null,
            { 'streamInterceptors': [environment.token == 'license' ? new GrpcLicenseStreamInterceptor(this.httpClient) : new GrpcStreamInterceptor()] });

        // let metadata = { Authorization: `Bearer ${this.localStorage.getToken()}`}

        let grpc = coreSubjectServiceClient.getAllTenants(request);

        return new Promise((resolve, reject) => {

            grpc.on('data', (response: TenantGrpcModel) => {
                tenants.push(this.tenantMapper.mapFrom(response));
            });

            grpc.on('error', (err: any) => {
                let failure: FailureResponse = {
                    code: err.code,
                    message: err.message,
                };
                reject(failure);
            });

            grpc.on('end', () => {
                resolve(tenants)
            });
        });
    }

    /**
     * Search a tenant by any its attributes
     * @param params TenantEntity
     * @returns Promise<TenantEntity[]>
     */
    override searchTenant(params: { tenant: TenantEntity; }): Promise<TenantEntity[]> {
        let request = this.tenantMapper.mapTo(params.tenant)

        let tenants: TenantEntity[] = [];

        let coreSubjectServiceClient = new CoreTenantServiceClient(`${this.localStorageService.getApiGatewayURL() ?? environment.grpcApiGateway}`, null,
            { 'streamInterceptors': [environment.token == 'license' ? new GrpcLicenseStreamInterceptor(this.httpClient) : new GrpcStreamInterceptor()] });

        // let metadata = { Authorization: `Bearer ${this.localStorage.getToken()}`}

        let grpc = coreSubjectServiceClient.search(request);

        return new Promise((resolve, reject) => {

            grpc.on('data', (response: TenantGrpcModel) => {
                tenants.push(this.tenantMapper.mapFrom(response));
            });

            grpc.on('error', (err: any) => {
                let failure: FailureResponse = {
                    code: err.code,
                    message: err.message,
                };
                reject(failure);
            });

            grpc.on('end', () => {
                resolve(tenants)
            });
        });
    }

    /**
     * Get the tenant using the Tenant ID
     * @param params id: string;
     * @returns Promise<TenantEntity>
     */
    override getTenantById(params: { id: string; }): Promise<TenantEntity> {

        let request = new TenantRequest();
        request.setId(params.id);

        let coreTenantServiceClient = new CoreTenantServiceClient(`${this.localStorageService.getApiGatewayURL() ?? environment.grpcApiGateway}`, null,
            { 'streamInterceptors': [environment.token == 'license' ? new GrpcLicenseStreamInterceptor(this.httpClient) : new GrpcStreamInterceptor()] });

        // let metadata = { Authorization: `Bearer ${this.localStorage.getToken()}`}

        return new Promise((resolve, reject) => {
            coreTenantServiceClient.getTenantById(request, null, (err, response) => {
                if (err) {
                    let failure: FailureResponse = {
                        code: err.code,
                        message: err.message,
                    };
                    reject(failure);
                } else {
                    if (response.hasFailure()) {
                        let failure: FailureResponse = {
                            code: response.getFailure()?.getCode(),
                            message: response.getFailure()?.getMessage(),
                        };
                        reject(failure);
                    } else {
                        resolve(this.tenantMapper.mapFrom(response.getTenantmodel()!));
                    }
                }
            });
        });
    }

    /**
     * Delete a tenant using the tenant ID
     * @param params id: string;
     * @returns  Promise<SuccessResponse>
     */
    override deleteTenantById(params: { id: string; }): Promise<SuccessResponse> {

        let request = new TenantRequest();
        request.setId(params.id);

        let success!: SuccessResponse;

        let coreTenantServiceClient = new CoreTenantServiceClient(`${this.localStorageService.getApiGatewayURL() ?? environment.grpcApiGateway}`, null,
            { 'streamInterceptors': [environment.token == 'license' ? new GrpcLicenseStreamInterceptor(this.httpClient) : new GrpcStreamInterceptor()] });

        // let metadata = { Authorization: `Bearer ${this.localStorage.getToken()}`};

        return new Promise((resolve, reject) => {
            coreTenantServiceClient.deleteTenantById(request, null, (err, response) => {
                if (err) {
                    let failure: FailureResponse = {
                        code: err.code,
                        message: err.message,
                    };
                    reject(failure);
                } else {
                    if (response.hasSuccess()) {
                        success = {
                            success: response.toObject().success?.success
                        }
                        resolve(success);
                    } else {
                        let failure: FailureResponse = {
                            code: response.getFailure()?.getCode(),
                            message: response.getFailure()?.getMessage()
                        }
                        reject(failure);
                    }
                }
            });
        });
    }

    /**
     * Update the tenant's parameters
     * @param params tenant: TenantEntity
     * @returns Promise<TenantEntity>
     */
    override updateTenantById(params: { tenant: TenantEntity; }): Promise<TenantEntity> {

        let request = this.tenantMapper.mapTo(params.tenant);

        let coreTenantServiceClient = new CoreTenantServiceClient(`${this.localStorageService.getApiGatewayURL() ?? environment.grpcApiGateway}`, null,
            { 'streamInterceptors': [environment.token == 'license' ? new GrpcLicenseStreamInterceptor(this.httpClient) : new GrpcStreamInterceptor()] });

        // let metadata = { Authorization: `Bearer ${this.localStorage.getToken()}`}

        return new Promise((resolve, reject) => {
            coreTenantServiceClient.updateTenantById(request, null, (err, response) => {
                if (err) {
                    let failure: FailureResponse = {
                        code: err.code,
                        message: err.message,
                    };
                    reject(failure);
                } else {
                    if (response.hasFailure()) {
                        let failure: FailureResponse = {
                            code: response.getFailure()?.getCode(),
                            message: response.getFailure()?.getMessage(),
                        };
                        reject(failure);
                    } else {
                        resolve(this.tenantMapper.mapFrom(response.getTenantmodel()!));
                    }
                }
            });
        });
    }

    /**
     * Update the tenant status
     * @param params id: string; status: TenantStatus
     * @returns Promise<TenantEntity>
     */
    override updateTenantStatusById(params: { id: string; status: TenantStatus; }): Promise<TenantEntity> {
        let request = new RequestUpdateStatus();
        request.setId(params.id);
        request.setStatus(toEnum(TenantStatusEnumGrpc, params.status)!);


        let coreTenantServiceClient = new CoreTenantServiceClient(`${this.localStorageService.getApiGatewayURL() ?? environment.grpcApiGateway}`, null,
            { 'streamInterceptors': [environment.token == 'license' ? new GrpcLicenseStreamInterceptor(this.httpClient) : new GrpcStreamInterceptor()] });

        // let metadata = { Authorization: `Bearer ${this.localStorage.getToken()}`}

        return new Promise((resolve, reject) => {
            coreTenantServiceClient.updateTenantStatusById(request, null, (err, response) => {
                if (err) {
                    let failure: FailureResponse = {
                        code: err.code,
                        message: err.message,
                    };
                    reject(failure);
                } else {
                    if (response.hasFailure()) {
                        let failure: FailureResponse = {
                            code: response.getFailure()?.getCode(),
                            message: response.getFailure()?.getMessage(),
                        };
                        reject(failure);
                    } else {
                        resolve(this.tenantMapper.mapFrom(response.getTenantmodel()!));
                    }
                }
            });
        });
    }
}