#!/bin/bash
# Verazial Admin Frontend - Quick Deploy Script

set -e

echo "=========================================="
echo "Verazial Admin Frontend Deployment"
echo "=========================================="

# Check if Docker is running
if ! docker info > /dev/null 2>&1; then
    echo "Error: Docker is not running. Please start Docker and try again."
    exit 1
fi

# Load the image if not already loaded
if ! docker images | grep -q "verazial/admin"; then
    echo "Loading Docker image..."
    docker load -i verazial-admin-v2.0.16.tar
fi

# Check if .env file exists
if [ ! -f ".env" ]; then
    echo "Error: .env file not found. Please ensure the .env file exists."
    exit 1
fi

echo "Loading environment configuration..."
source .env

echo "Configuration:"
echo "  - Host Port: ${HOST_PORT}"
echo "  - gRPC API Gateway: ${GRPC_API_GATEWAY}"
echo "  - Konektor API: ${KONEKTOR_API}"
echo "  - Credentials API: ${CREDENTIALS_API}"

echo ""
read -p "Do you want to proceed with the deployment? (y/N): " -n 1 -r
echo
if [[ ! $REPLY =~ ^[Yy]$ ]]; then
    echo "Deployment cancelled."
    exit 0
fi

echo ""
echo "Deploying application..."
docker-compose up -d

echo ""
echo "=========================================="
echo "Deployment completed successfully!"
echo "=========================================="
echo ""
echo "Application is running at: http://localhost:${HOST_PORT}"
echo ""
echo "Management commands:"
echo "  - Check status: docker-compose ps"
echo "  - View logs: docker-compose logs -f"
echo "  - Stop application: docker-compose down"
