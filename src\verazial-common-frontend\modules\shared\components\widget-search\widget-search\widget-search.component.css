.widgetIframeSearch {
    width: 500px;
    height: 361px;
}

.widgetIframeSearchDiv {
    padding: .5rem;
}

.selectTechnology {
    background: #F5FCFE;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
}

.selectTechnologyLabel {
    margin-top: 12px;
    padding: 0px 25px 0px 25px;
    text-align: center;
    color: #162746;
}

.buttonCloseBk {
    background-color: #222C3F;
    border-color: #222C3F;
}

.closeWidgetButtonDiv {
    line-height: 12px;
    width: 25px;
    font-family: tahoma;
    position: absolute;
    top: -20px;
    right: 0;
}

.closeWidgetButton {
    border-radius: 60%;
}

.hidden-widget {
    visibility: hidden;
    position: absolute;
    width: 1px;
    height: 1px;
    overflow: hidden;
}

@media (max-width: 1250px) {
}

@media (max-width: 900px) {
}

@media (max-width: 790px) {
}

@media (max-width: 775px) {
}

@media (max-height: 650px) {
}

@media (max-width: 550px) {
}

@media (max-width: 500px) {
    .widgetIframeSearch {
        width: 319px;
        height: 480px;
    }
}