#!/bin/bash

# Test script to verify environment variables are read correctly from .env file

echo "Testing environment variable reading from .env file..."
echo "=================================================="

if [ ! -f ".env" ]; then
    echo "Error: .env file not found!"
    exit 1
fi

echo "Reading variables from .env file:"
echo ""

# Read each variable safely
HOST_PORT=$(grep "^HOST_PORT=" .env | cut -d'=' -f2 | tr -d '"')
GRPC_API_GATEWAY=$(grep "^GRPC_API_GATEWAY=" .env | cut -d'=' -f2 | tr -d '"')
STATIC_API_GATEWAY_URL=$(grep "^STATIC_API_GATEWAY_URL=" .env | cut -d'=' -f2 | tr -d '"')
KONEKTOR_API=$(grep "^KONEKTOR_API=" .env | cut -d'=' -f2 | tr -d '"')
CREDENTIALS_API=$(grep "^CREDENTIALS_API=" .env | cut -d'=' -f2 | tr -d '"')
CREDENTIAL_USER=$(grep "^CREDENTIAL_USER=" .env | cut -d'=' -f2 | tr -d '"')
CREDENTIAL_PASSWORD=$(grep "^CREDENTIAL_PASSWORD=" .env | cut -d'=' -f2 | tr -d '"')

echo "HOST_PORT: '${HOST_PORT}'"
echo "GRPC_API_GATEWAY: '${GRPC_API_GATEWAY}'"
echo "STATIC_API_GATEWAY_URL: '${STATIC_API_GATEWAY_URL}'"
echo "KONEKTOR_API: '${KONEKTOR_API}'"
echo "CREDENTIALS_API: '${CREDENTIALS_API}'"
echo "CREDENTIAL_USER: '${CREDENTIAL_USER}'"
echo "CREDENTIAL_PASSWORD: '${CREDENTIAL_PASSWORD}'"

echo ""
echo "Testing docker-compose environment variable substitution..."
echo "=========================================================="

# Test docker-compose config (this will show the resolved configuration)
if command -v docker-compose &> /dev/null; then
    echo "Docker-compose configuration with environment variables resolved:"
    docker-compose config
else
    echo "docker-compose not found, skipping configuration test"
fi

echo ""
echo "Environment variable test completed!"
