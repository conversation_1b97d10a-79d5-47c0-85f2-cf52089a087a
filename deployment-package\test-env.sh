#!/bin/bash

# Test script to verify environment variables are read correctly from .env file

# Determine which .env file to use
ENV_FILE="${1:-.env}"

echo "Testing environment variable reading from $ENV_FILE..."
echo "=================================================="

if [ ! -f "$ENV_FILE" ]; then
    echo "Error: Environment file '$ENV_FILE' not found!"
    echo "Usage: $0 [path-to-env-file]"
    echo "Example: $0 ../.env"
    exit 1
fi

echo "Reading variables from $ENV_FILE:"
echo ""

# Read each variable safely
HOST_PORT=$(grep "^HOST_PORT=" "$ENV_FILE" | cut -d'=' -f2 | tr -d '"')
GRPC_API_GATEWAY=$(grep "^GRPC_API_GATEWAY=" "$ENV_FILE" | cut -d'=' -f2 | tr -d '"')
STATIC_API_GATEWAY_URL=$(grep "^STATIC_API_GATEWAY_URL=" "$ENV_FILE" | cut -d'=' -f2 | tr -d '"')
KONEKTOR_API=$(grep "^KONEKTOR_API=" "$ENV_FILE" | cut -d'=' -f2 | tr -d '"')
CREDENTIALS_API=$(grep "^CREDENTIALS_API=" "$ENV_FILE" | cut -d'=' -f2 | tr -d '"')
CREDENTIAL_USER=$(grep "^CREDENTIAL_USER=" "$ENV_FILE" | cut -d'=' -f2 | tr -d '"')
CREDENTIAL_PASSWORD=$(grep "^CREDENTIAL_PASSWORD=" "$ENV_FILE" | cut -d'=' -f2 | tr -d '"')

echo "HOST_PORT: '${HOST_PORT}'"
echo "GRPC_API_GATEWAY: '${GRPC_API_GATEWAY}'"
echo "STATIC_API_GATEWAY_URL: '${STATIC_API_GATEWAY_URL}'"
echo "KONEKTOR_API: '${KONEKTOR_API}'"
echo "CREDENTIALS_API: '${CREDENTIALS_API}'"
echo "CREDENTIAL_USER: '${CREDENTIAL_USER}'"
echo "CREDENTIAL_PASSWORD: '${CREDENTIAL_PASSWORD}'"

echo ""
echo "Testing docker-compose environment variable substitution..."
echo "=========================================================="

# Test docker-compose config (this will show the resolved configuration)
if command -v docker &> /dev/null && docker compose version &> /dev/null; then
    echo "Docker compose configuration with environment variables resolved:"
    docker compose --env-file "$ENV_FILE" config
elif command -v docker-compose &> /dev/null; then
    echo "Docker-compose configuration with environment variables resolved:"
    COMPOSE_FILE=docker-compose.yml docker-compose --env-file "$ENV_FILE" config
else
    echo "docker compose not found, skipping configuration test"
fi

echo ""
echo "Environment variable test completed!"
