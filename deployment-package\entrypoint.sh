#!/bin/sh

# Environment variable substitution script for Angular deployment
# This script replaces environment variables in the built Angular application

echo "Starting environment variable substitution..."

# Define the path to the main JavaScript files
MAIN_JS_PATH="/usr/share/nginx/html"

# Find the main JavaScript file (it has a hash in the name)
MAIN_JS_FILE=$(find $MAIN_JS_PATH -name "main*.js" | head -1)

if [ -z "$MAIN_JS_FILE" ]; then
    echo "Warning: Could not find main JavaScript file"
else
    echo "Found main JavaScript file: $MAIN_JS_FILE"

    # Replace environment variables in the JavaScript file
    # gRPC API Gateway
    if [ ! -z "$GRPC_API_GATEWAY" ]; then
        sed -i "s|https://dev-gatewaygrpc\.verazial\.com|$GRPC_API_GATEWAY|g" "$MAIN_JS_FILE"
        echo "Replaced GRPC_API_GATEWAY with: $GRPC_API_GATEWAY"
    fi

    # Konektor API
    if [ ! -z "$KONEKTOR_API" ]; then
        sed -i "s|https://localhost:8443|$KONEKTOR_API|g" "$MAIN_JS_FILE"
        echo "Replaced KONEKTOR_API with: $KONEKTOR_API"
    fi

    # Credentials API
    if [ ! -z "$CREDENTIALS_API" ]; then
        sed -i "s|https://dev-pass\.verazial\.com/api/v1/|$CREDENTIALS_API|g" "$MAIN_JS_FILE"
        echo "Replaced CREDENTIALS_API with: $CREDENTIALS_API"
    fi

    # Credential User
    if [ ! -z "$CREDENTIAL_USER" ]; then
        sed -i "s|credential_user:\"verazial\"|credential_user:\"$CREDENTIAL_USER\"|g" "$MAIN_JS_FILE"
        echo "Replaced CREDENTIAL_USER with: $CREDENTIAL_USER"
    fi

    # Credential Password
    if [ ! -z "$CREDENTIAL_PASSWORD" ]; then
        sed -i "s|credential_password:\"@4S9w&JMhg27\"|credential_password:\"$CREDENTIAL_PASSWORD\"|g" "$MAIN_JS_FILE"
        echo "Replaced CREDENTIAL_PASSWORD with: [HIDDEN]"
    fi

    # Static API Gateway URL
    if [ ! -z "$STATIC_API_GATEWAY_URL" ]; then
        sed -i "s|staticApiGatewayURL:true|staticApiGatewayURL:$STATIC_API_GATEWAY_URL|g" "$MAIN_JS_FILE"
        echo "Replaced STATIC_API_GATEWAY_URL with: $STATIC_API_GATEWAY_URL"
    fi
fi

echo "Environment variable substitution completed."

# Start nginx
echo "Starting nginx..."
exec nginx -g "daemon off;"
