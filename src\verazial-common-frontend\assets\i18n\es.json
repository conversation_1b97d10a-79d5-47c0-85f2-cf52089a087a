{"locale": "es-ES", "add_application": "Agregar Aplicación", "add_window": "<PERSON><PERSON><PERSON><PERSON>", "dateFormat": "dd/mm/yy", "today": "Hoy", "dateFormatLong": "dd/MM/yyyy", "dateTimeFormat": "dd/MM/yyyy HH:mm:ss", "timeOnlyFormat": "HH:mm:ss", "hourFormat": "24", "username": "Usuario", "password": "Contraseña", "currentPassword": "Contraseña Actual", "newPassword": "Nueva Contraseña", "confirmNewPassword": "Con<PERSON><PERSON><PERSON>", "setupNewAdminUser": "Nuevo Administrador", "updatePassword": "Actualizar <PERSON>", "resetPassword": "Restable<PERSON>", "versions": "Versiones", "save": "Guardar", "add": "Agregar", "confirm": "Confirmar", "continue": "<PERSON><PERSON><PERSON><PERSON>", "accept": "Aceptar", "clear": "Limpiar", "cancel": "<PERSON><PERSON><PERSON>", "close": "<PERSON><PERSON><PERSON>", "search_coincidences": "Buscar Coincidencias", "view_full_profile": "Ver perfil completo", "update": "Actualizar", "delete": "Eliminar", "delete_entity": "Eliminar", "edit_entity": "Editar En<PERSON>", "edit": "<PERSON><PERSON>", "view_data": "<PERSON><PERSON>", "create_new_user": "Nuevo usuario", "create_new_subject": "Nuevo sujeto", "create_new_case": "Nuevo Caso", "createRecord": "<PERSON><PERSON><PERSON>", "auditTrail": "Pista de Auditoría", "next": "Siguient<PERSON>", "after_searching_by_id": "Después de buscar por ID, permitir", "disconnect": "Desconectar", "manageProfile": " Gestionar Perfíl", "logout": " <PERSON><PERSON><PERSON>", "spanish": "Español", "english": "Inglés", "portuguese": "Portugués", "yes": "Si", "no": "No", "publish": "Publicar", "back": "Regresar", "created_at": "Creado el", "updated_at": "Actualizado el", "profilePicture": "Foto de perfil", "searchInRecords": "Buscar en registros", "searchInUsers": "Buscar en usuarios", "biographic": "Biográfico", "biometric": "Biométrico", "prisons": "Prisons", "history": "Hist<PERSON><PERSON><PERSON>", "actions": "Actions", "flowAssignment": "Asignación de Flujo", "flows": "<PERSON><PERSON><PERSON>", "profiles": "<PERSON><PERSON><PERSON>", "change_view": "Cambiar vista", "verify": "Verificar", "identify": "Identificar", "verify_identity": "Verificar identidad", "verify_user": "Verificar usuario", "datePlaceholder": "dd/mm/aaaa", "ADMIN": "Administrador", "ENROLLER": "Enrol<PERSON>", "clearFilters": "Clear filters", "update_password": "Actualizar contraseña", "remove": "Eliminar", "confirmation": "Confirmación", "subject": {"new_subject": "Nuevo sujeto", "no_subjects_available": "No hay sujetos disponibles."}, "buttons": {"save": "Guardar", "show": "Mostrar", "logout": "Desconectar", "add_option": "Agregar opción", "add_relation": "Agregar relación", "add_segment": "Agregar segmento", "add_location": "Agregar ubicación", "add_device": "Agregar dispositivo", "new": "Nuevo", "cancel": "<PERSON><PERSON><PERSON>", "yes": "Si", "no": "No", "sign_in": "Ingresar", "verify": "Verificar", "apply": "Aplicar", "update_password": "Actualizar contraseña", "continue": "<PERSON><PERSON><PERSON><PERSON>", "add": "Agregar", "new_license": "Nueva licencia", "new_api_connection": "Nueva conexión API", "add_method": "Agregar m<PERSON>", "next": "Siguient<PERSON>", "back": "Regresar", "update": "Actualizar", "new_ldap_connection": "Nueva conexión LDAP", "return": "Regresar", "add_data_source": "<PERSON><PERSON><PERSON><PERSON>", "add_parameters": "Agregar <PERSON>", "show_data_sources": "Mostrar Origen de Datos", "show_parameters": "Mostrar Parámetros", "show_application": "Mostrar Aplicaciones", "show_windows": "<PERSON><PERSON>", "change_password": "Cambiar contraseña", "reload": "Recargar", "update_credentials": "Actualizar credenciales", "new_application": "Nueva aplicación", "publish": "Publish", "unpublish": "Despublicar", "new_window": "Nueva ventana", "new_component": "Nuevo componente", "send": "Enviar", "new_datasource": "Nuevo origen de datos", "finish": "Finalizar", "add_parameter": "Agregar Parámetro", "returnHome": "Volver a la página de inicio", "sendPasswordRecovery": "Enviar recuperación de contraseña", "add_location_table": "Agregar ubicación", "close": "<PERSON><PERSON><PERSON>"}, "menu": {"locations": "Ubicaciones", "subject_locations": "Ubicaciones de Sujetos", "main": "Principal", "home": "<PERSON><PERSON>o", "search": "Busqueda", "subject": "Sujetos", "subjects": "Sujetos", "enroll": "Enrolamiento", "new_subject": "Nuevo Sujeto", "biographic_data": "<PERSON>tos <PERSON>r<PERSON>", "facial": "Facial", "iris": "Iris", "all": "<PERSON><PERSON>", "all_m": "Todos", "all_f": "<PERSON><PERSON>", "fingerprint": "<PERSON><PERSON>", "rolledFingerPrint": "<PERSON><PERSON> rolada", "palm": "Palma", "additional_info": "Información Adicional", "summary": "Resumen", "administration": "Administración", "tenants": "Tenants", "roles": "Roles", "profile_role": "Perfiles/Roles", "users": "Usuarios", "process": "Procesos", "clock": "Clock", "flows": "<PERSON><PERSON><PERSON>", "categories": "Categorías", "assignments": "Asignaciones", "time_records": "<PERSON><PERSON><PERSON><PERSON>", "audit_trail": "Pista de Auditoría", "pass": "Pass", "application_datasource": "Origen de Datos", "application_flow": "Procesos de Aplicaciones", "application_assign": "Asignación de Aplicaciones", "application_settings": "Application Settings", "versions": "Versions", "admin_users": "User", "config_role": "Config. Role", "config_admin": "ID Admin", "config_enroll": "ID Enroll", "config_prisons": "ID Prisons", "config_widget": "ID Widget", "config_clock": "ID Clock", "config_pass": "ID Pass", "config_manager": "ID Manager", "config_home": "<PERSON><PERSON>o", "config_reports": "ID Reports", "config_general": "General", "configuration": "Configuraciones", "datasources": "Origen de Datos", "applications": "Aplicaciones", "my_applications": "Mis Aplicaciones", "reports": "Reportes", "reports_general": "General", "reports_users": "Usuarios", "reports_subjects": "Sujetos", "reports_verification": "Verificación", "reports_identification": "Identificación", "reports_performance": "Rendimiento", "reports_actions": "Acciones", "reports_audit_trail": "Audit trail", "prisons": "Prisiones", "prisons_visits": "Visitas", "prisons_schedules": "<PERSON><PERSON><PERSON>", "prisons_transfers": "Traslados", "criminalistics": "Criminalística", "criminalistics_reports": "Informes", "criminalistics_cases": "Casos Actuales", "fingerprint_form": "Formulario de Huella"}, "divider": {"new_action": "Nueva Acción", "or_continue_with": "o continuar con", "new_reason": "Nuevo motivo", "new_profile": "Nuevo perfile", "new_segment": "Nuevo segmento", "matcher": "Matcher", "edit_licenses": "Editar licencias", "new_location": "Nueva ubicación", "new_device": "Nuevo dispositivo", "credentials": "Credenciales", "match": "Match", "enroll": "Enroll", "new_field": "Nuevo campo", "add_fields": "Agregar campos", "new_theme": "Nuevo tema", "connection": "Conexión", "default_datasource": "Origen de datos por defecto", "new_method": "Nuevo método", "new_license": "Nueva licencia", "add_catalog": "Agregar catálogo", "new_app_flow_type": "Nuevo tipo de flujo"}, "loginForm": {"enterEmail": "Ingrese su email", "enterUsername": "Ingrese su nombre de usuario", "enterPassword": "Ingrese la contraseña", "enterNumID": "Ingrese el número de ID", "loginOptions": "Opciones", "userNotFound": "<PERSON><PERSON><PERSON>", "wrongUsername": "Nombre de usuario <PERSON>o", "wrongPassword": "Contrase<PERSON>", "wrongCredentials": "Credenciales incorrectas", "adminBioLabel": "o continuar con", "subjectBioLabel": "Verifique su identidad", "signIn": "In<PERSON><PERSON>", "back_to_login": "Volver al inicio de sesión"}, "table": {"num_id": "Número ID", "numId": "Número ID", "profile": "Perfil", "main_profile": "<PERSON><PERSON><PERSON> Principal", "profiles": "<PERSON><PERSON><PERSON>", "role": "Rol", "main_role": "<PERSON><PERSON>", "verification": "1:1 Verificación", "segmented_search": "1:n Búsqueda segmentada", "roles": "Roles", "username": "Nombre de usuario", "first_name": "Primer nombre", "second_name": "<PERSON><PERSON><PERSON> no<PERSON>", "last_name": "Primer <PERSON><PERSON><PERSON><PERSON>", "second_last_name": "<PERSON><PERSON><PERSON>", "names": "Nombres", "lastNames": "<PERSON><PERSON><PERSON><PERSON>", "birthdate": "Fecha de nacimiento", "gender": "<PERSON><PERSON><PERSON>", "language": "Idioma", "datasource": "Origen de datos", "actions": "Acciones", "flowAssignment": "Asignación de Flujo", "last_action": "Última acción", "reasonUpdate": "Seleccione el motivo de actualización", "reasonDelete": "Seleccione el motivo de eliminación", "reasonCreate": "Seleccione el motivo de creación", "reasonConfig": "Seleccione el motivo de actualización de la configuración", "center": "Centro", "especific_location": "Ubicación específica", "present/absent": "Presente/Ausente (hh:mm:ss)", "present": "Presente", "absent": "Ausente", "transfer": "Traslado", "lastActions": "Últimas acciones", "assignedTo": "Asignado a", "caseNumber": "Número de <PERSON>so", "name": "Nombre", "createdBy": "<PERSON><PERSON>o por", "updatedBy": "Actualizado por", "createdAt": "Creado el", "updatedAt": "Actualizado el", "openDate": "Abierto el", "title": "<PERSON><PERSON><PERSON><PERSON>", "type": "Tipo", "location": "Ubicación", "description": "Descripción", "comments": "Comentarios", "subject_profiles": "Per<PERSON>les de Sujeto", "user_roles": "<PERSON><PERSON> <PERSON> Us<PERSON>", "assignedType": "Asignación"}, "action_codes": {"no_device": "NO_DEV", "modify_user": "MOD_BIO", "modify_config": "MOD_CONF", "remove_subject_sample": "REM_SUB_SAM", "remove_subject_button": "REM_SUB_BT", "remove_picture": "REM_PIC", "remove_sample": "REM_SAM", "add_subject": "NEW_SUB", "add_sample": "NEW_SAM", "add_picture": "ADD_PIC", "verification_initial": "VER_INI", "verification_complementary": "VER_COM", "identification_biometrics": "ID_SAM", "identification_user_pass": "ID_PASS", "identification_otp": "ID_KEY", "identification_pin": "ID_PIN", "debug_local": "DEB_LOC", "modify_prisons": "MOD_PR"}, "prisons_tab": {"location": "Ubicación", "occupiedBy": "Ocupada por", "relationships": "Relaciones", "entry_date": "<PERSON>cha de Ingreso", "init_date": "Fecha de Inicio", "end_date": "<PERSON><PERSON>", "exit_date": "<PERSON><PERSON>", "reason": "Motivo de salida / entrada", "visited_person": "<PERSON>a visitada", "visit_time": "Tiempo de visita", "is_inside": "¿Se encuentra dentro del centro?", "cpl": "CPL", "regime": "Régimen", "clasification": "Clasificación", "pending": "Pendiente", "actual_location": "Ubicación actual", "new_location": "Asignar nueva ubicación", "new_entry_exit": "Nueva entrada/salida", "definitive_entry": "Ingreso definitivo", "definitive_exit": "Egreso definitivo", "entry": "Entrada", "exit": "Salida", "transfer_entry": "Entrada de traslado", "transfer_exit": "Salida de traslado", "select_location_entry": "Seleccione la ubicación a la que entrar", "select_related_subject": "Seleccione el sujeto relacionado para la entrada", "select_transfer_auth": "Seleccione la autorización de traslado para la salida", "select_entry_and_exit_auth": "Seleccione la autorización de entrada y salida"}, "actions_tab": {"action_code": "Código acción", "action_name": "Nombre acción"}, "config": {"auto_delete": "Eliminación automática de datos biométricos", "delete": "Eliminación", "widget_options": "Opciones del Widget", "url_widget": "URL WIDGET", "widget_match_key": "WIDGET MATCH KEY", "widget_enroll_key": "WIDGET ENROLL KEY", "biometricServerComponent_options": "Opciones del Servidor Biométrico", "biometricServerUrl": "URL BIOMETRIC SERVER", "biometricServerUser": "Usuario", "biometricServerPassword": "Contraseña", "biographicServerComponent_options": "Opciones del Servidor Biográfico", "biographicServerUrl": "URL BIOGRAPHIC SERVER", "biographicServerUser": "Usuario", "biographicServerPassword": "Contraseña", "externalBiographicServerComponent_options": "Opciones del Servidor Biográfico Externo", "externalBiographicServerUrl": "URL EXTERNAL BIOGRAPHIC SERVER", "externalBiographicServer": "Ser<PERSON>or Biográfico Externo", "externalBiographicServerUser": "Usuario", "externalBiographicServerPassword": "Contraseña", "clockServerComponent_options": "Opciones del Servidor Clock", "clockServerUrl": "URL CLOCK SERVER", "clockServerUser": "Usuario", "clockServerPassword": "Contraseña", "storageServerComponent_options": "Opciones del Servidor Storage", "storageServerUrl": "URL STORAGE SERVER", "storageServerUser": "Usuario", "storageServerPassword": "Contraseña", "actions available": "Acciones disponibles", "show_table": "Mostrar tabla", "code": "Código", "name": "Nombre", "filter_action": "Filtrar nombre acción", "filter_profile": "Filter nombre perfil", "close": "<PERSON><PERSON><PERSON>", "subjectTabs": "Configuración de las Pestañas de Sujetos", "showAdditionalTabs": "Mostrar opciones adicionales", "showPrisonTab": "Mostrar pestaña Prisons", "showProfilePictureTab": "Mostrar pestaña histórico de fotos de perfil", "allowAfternIdSearch": "Tras buscar por nº de ID, permitir", "allowEditAfternIdSearch": "<PERSON><PERSON>", "allowDeleteAfterIdSearch": "Eliminar Sujeto", "actionsServerComponent_options": "Opciones del Servidor Actions", "actionsServerUrl": "URL ACTIONS SERVER", "actionsServerUser": "Usuario", "actionsServerPassword": "Contraseña"}, "groups": {"new_group": "Nuevo grupo", "no_groups_available": "No hay grupos disponibles", "group_type": "Tipo de Grupo", "configuration": "Configuración", "group": "grupo", "delete_group": "Eliminar grupo", "update_group": "Actualizar grupo"}, "category": {"new_category": "Nueva Categoría", "no_categories_available": "No hay categorías dispobles", "category_type": "Categoría", "configuration": "Configuración", "category": "Categoría", "delete_category": "Eliminar categoría", "update_category": "Actualizat categoría", "list_categories": "Lista de categorías", "selected_categories": "Categorías selecionadas", "enable_date_range": "<PERSON><PERSON>"}, "flow": {"code": "Código", "name": "Nombre", "description": "Descripción", "profiles": "<PERSON><PERSON><PERSON>", "showFlowActions": "Mostrar Acciones de Flujo", "predecessors": "Antecesoras", "action": "Acción", "new_flow": "Nuevo Flujo", "no_flows_available": "No hay flujos disponibles", "remove_flow": "Eliminar Flujo", "add_action": "Agregar Acción", "actions_availables": "Acciones del flujo", "drag_drop": "Arrastrar y soltar ", "the_action": "la acción", "new_action": "Nueva acción", "edit_action": "Editar acción", "attributes": "Atributos", "select_element": "Seleccionar un elemento", "input": "Input", "dropdown": "Dropdown", "toggle": "Toggle", "required": "Requerido", "user": "Usuario", "option": "Opción", "options": "Opciones", "remove_attribute": "Eliminar atributo", "remove_action": "Eleminar acción", "save_flow": "Guardar Flujo", "status": "Estado", "updated_at": "Actualizado el", "published": "Publicado", "unpublished": "Quitar publicación", "no_published": "No publicado", "update_flow": "<PERSON><PERSON><PERSON><PERSON>", "last_update": "Última actualización", "publish": "Publicar", "flow": "F<PERSON>jo", "button": "Botón", "is_relay": "<PERSON><PERSON> <PERSON>", "list_flows": "Lista de flujos", "selected_flows": "<PERSON><PERSON><PERSON>", "min_characters": "Núm. min. de carateres", "max_characters": "Núm. máx. de carateres", "message": "Men<PERSON><PERSON>", "no_actions": "Ninguna acción", "severity": "Severidad"}, "assignment": {"name": "Name", "num_flows": "Núm. flujos", "num_categories": "Núm. categorías", "updated_at": "Actualizado el", "new_assignment": "Nueva asignación", "update_assignment": "Actualizar asignación", "general": "General", "add_flow": "Agregar flujo", "add_category": "Agregar categoría", "assignment": "asignación", "delete_assignment": "Eliminar asignación", "no_assignment_available": "No hay asignaciones disponibles", "flows": "<PERSON><PERSON><PERSON>", "categories": "Categorías", "configuration": "Configuración", "locations": "Ubicaciones", "subjects": "Sujetos", "schedules": "<PERSON><PERSON><PERSON>"}, "user": {"num_id": "NÚMERO DE ID", "profile": "PERFIL", "username": "NOMBRE DE USUARIO", "names": "NOMBRES", "lastNames": "APELLIDOS", "first_name": "PRIMER NOMBRE", "second_name": "SEGUNDO NOMBRE", "last_name": "PRIMER APELLIDO", "second_last_name": "SEGUNDO APELLIDO", "birthdate": "FECHA DE NACIMIENTO", "gender": "GÉNERO", "language": "IDIOMA", "location": "UBICACIÓN", "segment": "SEGMENTO", "device": "DISPOSITIVO", "no_roles_assigned": "No hay roles as<PERSON>ados", "new_user": "Nuevo usuario", "no_users_available": "No hay usuarios disponibles."}, "signaturesTable": {"identityId": "Identity", "eventCode": "Codigo Evento", "eventTimestamp": "Timestamp", "locationId": "Ubicación", "segmentId": "Segmento", "deviceId": "Dispositivo", "eventLatitude": "Lat.", "eventLongitude": "<PERSON>.", "applicationId": "ID Aplicación", "id": "ID", "eventId": "ID Evento", "startDate": "<PERSON><PERSON>", "endDate": "<PERSON><PERSON>", "eventResource": "Recurso Evento", "eventData": "Datos Evento", "executorId": "Usuario", "receiverId": "Sujeto", "receiverProfile": "<PERSON><PERSON>l Sujeto", "actionId": "ID Acción", "actionDuration": "Duración", "actionResult": "<PERSON><PERSON><PERSON><PERSON>", "technologyId": "Tech.", "samplesNumber": "Nº Muestras", "samplesSequentially": "Muestras Secuenciales", "sensorBrand": "<PERSON><PERSON>", "sensorModel": "<PERSON><PERSON>", "attributes": "Atributos", "numericAttributes": "Atributos Numéricos", "samples": "Muestras", "coordinates": "<PERSON><PERSON><PERSON><PERSON>", "action": "Acción", "result": "<PERSON><PERSON><PERSON><PERSON>", "executor": "<PERSON><PERSON><PERSON><PERSON>", "receptor": "Receptor", "date": "<PERSON><PERSON>", "totalTime": "Tiempo total", "details": "Detalles", "userTime": "Tiempo de usuario", "serverTime": "Tiempo de servidor", "connectionTime": "Tiempo de red", "extractionTime": "Tiempo de extracción", "matchingTime": "Tiempo de matching"}, "titles": {"subjects": "Sujetos", "subjectsShort": "Sujetos", "records": "Sujetos", "recordsShort": "Sujetos", "fingerPrint": "<PERSON><PERSON>", "facial": "Facial", "iris": "Iris", "rolledFingerPrint": "<PERSON><PERSON> rolada", "palm": "Palma", "administration": "Administración", "administrationShort": "Admin", "settings": "Configuración", "signatures": "<PERSON><PERSON><PERSON><PERSON>", "auditTrail": "Pista de Auditoría", "error_general": "Se ha producido un error", "error_requiredField": "Campo requerido", "success_general": "Comple<PERSON><PERSON> satisfactoriam<PERSON>", "error_permisionDenied": "<PERSON><PERSON><PERSON> den<PERSON>ado", "error_subjectNotFound": "Error: Sujeto no encontrado", "error_dataInconsistency": "Inconsistencia de datos", "personalData": "Datos Personales", "editSubject": "<PERSON><PERSON> su<PERSON>o", "edit_subject": "<PERSON><PERSON> su<PERSON>o", "edit_user": "<PERSON>ar usuario", "exitingWithoutSaving": "Cambios no guardados", "lastActionDetails": "Datos del último fichaje", "success_operation": "Éxito", "info_operation": "Información registrada", "error_operation": "Error en la operación", "success_enroll_user": "Sujeto enrolado con éxito", "success_add_user": "Sujeto registrado con éxito", "success_modify_user": "Sujeto modificado con éxito", "success_delete_user": "Sujeto eliminado con éxito", "success_delete_picture": "Imagen de perfil borrada con éxito", "success_delete_sample": "Muestra biométrica borrada con éxito", "success_add_picture": "Imagen de perfil registrada con éxito", "error_enroll_user": "Sujeto no enrolado", "error_add_user": "Sujeto no registrado", "error_modify_user": "Sujeto no modificado", "error_delete_user": "Sujeto no eliminado", "error_delete_picture": "Imagen de perfil no eliminada", "error_delete_sample": "Muestra biométrica no eliminada", "error_add_picture": "Imagen de perfil no registrada", "success_identify_user": "Sujeto identificado con éxito", "error_identify_user": "Sujeto no encontrado", "success_auth_user": "Sujeto autenticado con éxito", "error_auth_user": "Sujeto no autenticado", "error_data_inconsistency": "Inconsistencia de datos detectada", "access_granted": "Acceso permitido", "access_denied": "Acceso denegado", "flows": "<PERSON><PERSON><PERSON>", "users": "Usuarios", "groups": "Grupos", "categories": "Categorías", "process": "Procesos", "assignment": "Asignaciones", "login_error": "Error de inicio de sesión.", "role": "Roles", "profile_role": "Perfiles/Roles", "page_not_found": "Página no encontrada", "unauthorized": "No Autorizado", "biometric_data": "<PERSON>tos <PERSON>iomé<PERSON>", "biographic_data": "<PERSON>tos <PERSON>r<PERSON>", "extended_biographic_data": "Datos Biográficos Extendidos", "physical_data": "Datos Físicos", "profile_pic_history": "Histórico de fotos de perfil", "related_subjects": "Sujetos relacionados", "entries_and_exits": "Entradas y Salidas", "entry_exit_authorizations": "Autorizaciones de Entrada y Salida", "tenants": "Tenants", "important": "Importante", "konektor": "Konektor", "konektor_properties": "Propiedades de Konektor", "auth": "Authentication", "systemSettings": "System Settings", "error_role_access": "Error de acceso de rol", "error_login": "Error de inicio de sesión", "widget": "Widget", "unable_to_delete_subject": "No es posible eliminar el sujeto", "unable_to_perform_action": "No es posible realizar la acción", "CameraError": "Error al acceder a la cámara", "error": "Error", "samples": "Muestras", "samples_subjects": "Muestras por Sujeto", "profile_pics": "Fotos de perfil", "unknown": "Desconocida", "requests": "Solicitudes", "verified": "Verificado", "notVerified": "No verificado", "found": "Encontrado", "notFound": "No encontrado", "qFailed": "Calidad insuficiente", "tError": "Timeout de servidor", "sError": "<PERSON><PERSON><PERSON> de servidor", "other": "<PERSON><PERSON><PERSON>", "actionV": "Verificación 1:1", "actionI": "Identificación 1:N", "actionA": "<PERSON><PERSON><PERSON>", "actionD": "Eliminar muestra", "actionP": "Añadir foto de perfil", "actionS": "<PERSON><PERSON><PERSON>", "actionR": "Eliminar sujeto", "userTime": "Tiempo de usuario", "connTime": "Tiempo de red", "serverTime": "Tiempo de servidor", "picYes": "Con foto de perfil", "picNo": "Sin foto de perfil", "male": "<PERSON><PERSON><PERSON><PERSON>", "female": "Femenino", "otherGender": "<PERSON><PERSON><PERSON>", "error_change_tenant": "Error al cambiar de tenant", "belongings": "<PERSON>ten<PERSON><PERSON><PERSON>", "no_belongings_records_available": "No hay registros de pertenencias disponibles", "user_creation": "Creación de usuario", "subject_creation": "Creación de sujeto", "add_profile_pic_history": "Agregar foto de perfil", "belongings_photos": "Fotos de pertenencias", "belongings_hand_over_signatures": "Firmas de entrega de pertenencias", "belongings_return_signatures": "Firmas de devolución de pertenencias", "judicial_files": "Expedientes judiciales", "prisons_visits": "Visitas a prisiones", "authorization_schedules": "Horarios de autorización", "authorization_schedules_to_enter_centers": "Horarios de autorización para entrar en centros", "transfer_authorization": "Autorizaciones de traslado", "no_judicial_files_records_available": "No hay expedientes judiciales disponibles", "no_transfer_auths_available": "No hay autorizaciones de traslado disponibles", "no_entry_exit_auths_available": "No hay autorizaciones de entrada y salida disponibles", "no_auth_schedules_available": "No hay horarios de autorización disponibles", "subject_files": "Archivos", "cases": "Casos", "evidence": "Evidencia", "coincidences": "Coincidencias", "case_info": "Información del caso", "warning": "Advertencia"}, "title": {"settings": "Configuraciones", "users": "Usuarios", "applications": "Asignación de Aplicaciones", "credential_management": "Administración de Credenciales", "change_password": "Cambio de Contraseña"}, "application": {"ROLE_APP_CLOCK": "ID Clock", "ROLE_APP_PASS": "ID Pass", "ROLE_APP_MANAGER": "ID Manager", "ROLE_APP_ADMIN": "ID Admin", "ROLE_APP_REPORTS": "ID Reports", "ROLE_APP_WIDGET_MATCH": "ID Widget Match", "ROLE_APP_WIDGET_ENROLL": "ID Widget Enroll", "ROLE_APP_WIDGET": "ID Widget"}, "cases": {"case": "Caso", "new": "Nuevo caso", "edit": "<PERSON><PERSON> caso", "view": "Ver caso", "new_evidence": "Agregar evidencia", "general_info": "Información General", "additional_info": "Información Adicional", "locations": "Ubicaciones", "location": "Ubicacion", "search": "Buscar la zona", "assignments_relations": "Asignaciones y Relaciones", "name": "Nombre", "number": "Número del caso", "priority": "Nivel de prioridad", "crime_type": "Tipo de Delito", "category": "Categoría Legal", "department": "Departamento", "status": "Estado del caso", "crime_date": "<PERSON><PERSON> del incidente", "open_date": "Fecha de apertura", "close_date": "<PERSON><PERSON>", "description": "Descripción", "location_title": "Título de la ubicación", "location_type": "Tipo de ubicación", "comments": "Comentarios", "relations": "Relaciones", "assignments": "Asignaciones", "principal_agent": "<PERSON><PERSON>", "assigned_agents": "<PERSON><PERSON>", "related_cases": "Casos Relacionados", "related_subjects": "Sujetos Relacionados", "under_investigation": "En Investigación", "open": "<PERSON>bie<PERSON>o", "resolved": "<PERSON><PERSON><PERSON><PERSON>", "inactive": "Inactivo", "waiting": "En espera de juicio", "closed": "<PERSON><PERSON><PERSON>", "primary": "Principal", "secondary": "Secundaria", "additional": "Adicional", "pending": "Pendiente", "analyzed": "<PERSON><PERSON><PERSON><PERSON>", "confirmed": "<PERSON><PERSON><PERSON><PERSON>", "edited": "Editado", "new_comment": "Nuevo comentario", "comment": "Comentario", "mark_coincidence": "<PERSON><PERSON>", "mark_coincidence_message": "Estas seguro que quieres marcar la muestra de este sujeto como una ", "case_coincidence": "Coincidencia de caso", "search_configuration": "Configuración de busqueda", "filters": "<PERSON><PERSON><PERSON>", "max_results": "Resultados <PERSON>", "min_coincidence": "Porcentaje mínimo de coincidencia", "coincidence_name": "Nombre de Coincidencia", "evidence_name": "Nombre de Evidencia", "coincidence_status": "Estado", "coincidence_score": "<PERSON>vel Coinciden<PERSON>", "last_modification": "Última Modificación", "modified_by": "Modificado por", "coincidence_type": "Tipo de coincidencia", "more_info": "Más Información"}, "tenant": {"name": "Nombre", "nif": "CID", "email": "email", "country": "<PERSON><PERSON>", "city": "Ciudad", "status": "Estado", "is_active": "Activo", "created_at": "<PERSON><PERSON><PERSON>", "updated_at": "Actualizado", "no_tenants_available": "No hay tenants disponibles", "new_tenant": "Nuevo tenant", "tenant": "Tenant", "wait_tenant_creation": "Por favor espere hasta que el proceso de creación del tenant esté completo.", "engine": "Motor de BBDD", "host": "<PERSON><PERSON><PERSON>", "port": "Puerto", "database": "Base de datos", "schema": "Esquema", "username": "Usuario", "password": "Contraseña", "database_config": "Configuración de la BBDD", "change_tenant": "Cambiar tenant", "no_admin_token": "No hay token de administrador"}, "messages": {"contactYourSystemAdministrator": "Por favor, contacte con su administrador de sistemas", "pass_automation": "Automatización de Pass", "confirm_delete_profile": "Confirme si desea eliminar el registro", "confirm_delete_multiple_profiles": "Confirme si desea eliminar los registros", "will_be_deleted_profile": "Se eliminará el registro con número de ID:", "will_be_deleted_multiple_profile": "Se eliminarán los registro seleccionados.", "error_general": "Ocurrió un error inesperado", "error_getting_actions": "Error al obtener las acciones", "FAILURE": "Ocurrió un error inesperado", "error_permisionDenied": "No puede realizar esta acción", "error_isRequiredField": "Es un campo requerido", "error_username_must_be_valid_numId": "El nombre de usuario debe ser un número de ID válido", "error_username_must_be_valid_email": "El nombre de usuario debe ser un email válido", "error_must_be_valid_email": "Debe ser un email válido", "success_general": "La acción se ha completado con éxito", "error_subjectNotFound": "El sujeto no existe en la base de datos", "error_subjectNotFoundCreate": "El sujeto no existe en la base de datos, por favor crea el sujeto en el sistema biográfico o elimine sus datos biométricos", "error_dataInconsistency": "La identidad existe en Verázial ID Server, pero no en su servidor biográfico. Por favor contacte a su administrador de sistemas.", "confirm_delete_flow": "Confirme si desea eliminar el flujo", "will_be_deleted_flow": "Se eliminará el flujo con nombre:", "confirm_delete_config_profile": "Confirme si desea eliminar el perfil", "will_be_deleted_config_profile": "Se eliminará el perfil con nombre:", "error_codeAlreadyExists": "El código proporcionado ya está creado", "error_nameAlreadyExists": "El nombre proporcionado ya está creado", "error_parameterAlreadyExists": "El parámetro proporcionado ya está creado", "error_profileAlreadySelected": "Los perfiles seleccionados ya están asignados:", "error_actionCodeAlreadyExists": "La acción proporcionada ya está creada", "error_dataAlreadyExists": "Los datos proporcionados ya están creados", "success_flowActionUpdated": "La acción proporcionada se actualizó con éxito", "ERROR_PERMISSION_DENIED": "Per<PERSON>o denegado, no puede realizar esta acción", "ERROR_WRONGCREDENTIALS": "Credenciales incorrectas", "ERROR_BIOGRAFIC_REMOVEIDENTITY_NOT_COMPLETED": "Error en la eliminación automática de datos biométricos: biographic/removeIdentity no completado", "ERROR_BIOMETRIC_UNENROLLSUBJECT_NOT_COMPLETED": "Error en la eliminación automática de datos biométricos: biometric/unenrollSubject no completado", "ERROR_EMPTY_PARAMETER": "Parámetro vacío en la llamada", "ERROR_USERS_TENANT_CONFIG_NOT_FOUND": "ERROR_USERS_TENANT_CONFIG_NOT_FOUND", "ERROR_CONFIG_NOT_FOUND": "Configuración no encontrada", "ERROR_CONFIG_DATA_NOT_FOUND": "Datos de configuración no encontrados", "confirmPasswordFailed": "La contraseña no coincide con la confirmación", "exitingWithoutSaving": "No ha guardado los cambios. ¿Seguro que quiere salir?", "continueWithoutSaving": "No ha guardado los cambios. ¿Seguro que quiere continuar?", "selectTechnology": "Seleccione una tecnología biométrica para realizar la verificación para el sujeto:", "selectTechnologyUserVerification": "Seleccione una tecnología biométrica para realizar la verificación de usuario", "error_dateRangeError": "La fecha de inicio no puede ser mayor que la fecha de fin", "ERROR_USER_IS_BLOCKED": "El usuario está bloqueado", "ERROR_BIOMETRIC_VERIFICATION_FAILED": "La verificación biométrica ha fallado", "ERROR_INVALID_NUMID": "Número de ID inválido", "confirm_delete_profile_picture": "¿Está seguro que desea eliminar la foto de perfil?", "will_be_deleted_profile_picture": "Se eliminará la foto de perfil del sujeto con número de ID:", "user_without_samples_tec_selected": "El usuario no tiene muestras biométricas para la tecnología seleccionada", "user_without_samples": "El usuario no tiene muestras biométricas", "quality_error": "La calidad de la imagen es insuficiente para identificar", "invalidPassword": "La contraseña debe tener al menos 12 caracteres, una letra mayúscula, una letra minúscula, un número y un carácter especial", "invalidPasswordLength": "El tamaño mínimo de la contraseña es de ", "invalidPasswordComplexity": "La contraseña no cumple con los requisitos de complejidad", "invalidAccessKey": "Clave de acceso inválida", "accessKeyDisabled": "Clave de acceso deshabilitada, por favor contacte a su administrador", "disableOnNextAttempt": "La clave de acceso se deshabilitará en el próximo intento", "needLocationToEntry": "El sujeto necesita una ubicación pendiente para entrar", "noLocationToEnterHere": "El sujeto no tiene ninguna ubicación para entrar aquí", "noLocationToEnterTransfer": "El sujeto no tiene ninguna ubicación válida para entrar en el traslado", "subjectMustReturnToSameLocation": "El sujeto debe regresar a la misma ubicación", "noAuthScheduleForRole": "No hay horario de autorización para el rol", "noAuthScheduleForLocationAndRole": "No hay horario de autorización para la ubicación y el rol del sujeto", "noValidAuthScheduleForLocationAndRole": "No hay horario de autorización válido para la ubicación y el rol del sujeto", "noRelatedSubjectsOfEntryRole": "No tiene sujetos relacionados con el rol necesario para entrar: ", "exitLocationNotFound": "No se encontró la ubicación de salida", "needRoleToEntry": "El sujeto necesita un rol pendiente para entrar", "noEntryOrExitUser": "El suejto no tiene permiso para entrar o salir", "noEntryOrExitUserLocation": "El sujeto no tiene permiso para entrar o salir de esta ubicación", "noCreateLocationBecausePending": "No se puede crear una ubicación porque hay una pendiente", "message_remove": "¿Está seguro que desea eliminar ", "message_update": "¿Está seguro que desea actualizar ", "invalid_shedule_type": "Por favor seleccione un tipo de horario válido.", "error_range_dates": "La hora de inicio no puede ser mayor a la hora de finalización.", "no_days_selected": "Por favor seleccione al menos un día.", "group_same_name": "Ya existe un grupo con el mismo nombre.", "assignment_same_name": "Ya existe una asignación con el mismo nombre.", "category_flow_required": "Para crear asignaciones, se debe tener al menos un flujo y una categoría.", "flow_required": "Para crear asignaciones, se debe tener al menos un flujo.", "category_required": "Para crear asignaciones, se debe tener al menos una categoría.", "warning": "Advertencia", "user_password_error": "Email o contraseña incorrecto.", "error_username_password_required": "El nombre de usuario y la contraseña son requeridos.", "error_email_password_required": "El email y la contraseña son requeridos.", "delete_confirmation_header": "Confirmación de eliminación", "delete_multiple_records": "¿Está seguro de que desea eliminar los registros seleccionados?", "delete_single_record": "¿Estás seguro de que desea eliminar el registro:", "page_not_found": "Lo sentimos, la página no fue encontrada.", "unauthorized": "<PERSON> sentimo<PERSON>, usted no tienen permiso para acceder a este recurso.", "widget_error": "Ha ocurrido un error con el widget", "tenant_id_not_found": "Tenant ID no encontrado", "konektor_properties_not_found": "Propiedades de Konektor no encontradas", "konektor_connection_error": "Error de conexión con Konektor", "get_konektor_properties_error": "Error al obtener las propiedades de Konektor", "konektor_properties_error": "Error en las propiedades de Konektor", "must_select_reason": "Debe seleccionar un motivo", "password_not_match": "Las contraseñas no coinciden.", "error_updating_password": "Error al intentar actualizar la contraseña.", "password_updated": "Contraseña actualizada exitosamente!", "no_cam_detected": "No se detectó ninguna cámara", "no_cam_selected": "No se seleccionó ninguna cámara", "no_data_to_save": "No hay datos para guardar", "no_data_to_search_coincidence": "No hay datos para buscar coincidenca", "subject_inside_time_left": "El sujeto está dentro del centro, tiempo restante: ", "subject_outside_time_left": "El sujeto está fuera del centro, tiempo restante: ", "verification_required": "Si desea editar o eliminar datos de sujeto, debe verificarse por biometría como usuario.", "verification_required_user": "Si desea editar o eliminar datos de usuario, debe verificarse por biometría como usuario.", "verification_required_data": "Si desea acceso a editar estos datos, debe verificarse por biometría como usuario.", "verification_required_enroll": "Si desea enrolar un sujeto, debe verificarse por biometría como usuario.", "subject_verification_required_data": "Si desea acceso a editar estos datos, debe verificar al sujeto por biometría.", "error_getting_access": "Error al obtener acceso", "error_login": "Error al iniciar sesión", "duplicate_subject_numId": "El número de ID ya existe", "duplicate_user_email": "El email ya existe", "duplicate_user_numId_or_email": "El número de ID o email ya existe", "error_technology_not_allowed": "La tecnología seleccionada no está permitida", "no_techs_available": "No hay tecnologías disponibles", "window_used": "Esta ventana ya está en el flujo.", "saved_successfully": "Guardado satisfactoriamente!!!", "updated_successfully": "Actualizado satisfactoriamente!!!", "removed_successfully": "Eliminado satisfactoriamente!!!", "error_retrieving_data": "Error al recuperar los datos", "error_delete": "Error trantando de eliminar", "error_delete_record": "Error tratando de eliminar el registro", "error_save": "Error tratando de guardar", "error_save_record": "Error tratando de guardar el registro", "error_update": "Error tratando de actualizar", "error_update_record": "Error tratando de actualizar el registro", "success_delete_record": "Registro eliminado satisfactoriamente", "success_save_record": "Registro guardado satisfactoriamente", "success_update_record": "Registro actualizado satisfactoriamente", "message_update_data_source": "¿Está seguro de que desea actualizar ", "message_add_parameters": "¿Está seguro de que desea agregar el parámetro a ", "message_remove_subject_app": "¿Está seguro de que desea eliminar la aplicación del usuario: ", "api_connection_error": "Error en la conexión con la API ", "error_subject_has_user": "El sujeto tiene un usuario asociado", "error_resetting_password": "Error al restablecer la contraseña", "password_reset_email_sent": "Se ha enviado un correo electrónico con instrucciones para restablecer la contraseña", "error_license": "Error en la licencia por favor contacte a su administrador", "error_license_no_access": "Su licencia no permite el acceso a esta aplicación", "error_license_not_configured_konektor": "La licencia no está configurada en Konektor", "error_cannot_access_without_license": "No puede acceder a esta aplicación sin una licencia, por favor contacte a su administrador", "cannot_delete_subject_user": "No se puede eliminar el sujeto porque tiene un usuario asociado", "cannot_save_subject_user_ds": "El sujeto no puede ser guardado porque el origen de datos seleccionado no lo permite", "cannot_create_subject_user_ds": "El sujeto no puede ser creado porque el origen de datos seleccionado no lo permite", "cannot_update_subject_user_ds": "El sujeto no puede ser actualizado porque el origen de datos seleccionado no lo permite", "cannot_delete_subject_user_ds": "El sujeto no puede ser eliminado porque el origen de datos seleccionado no lo permite", "ContactAdminCamera": "Compruebe que la cámara tenga permiso para ser utilizada en esta web", "error_widget_url": "Error en la URL del widget, por favor contacte a su administrador", "timeout": "El tiempo límite tiene que ser mayor a 10 seg. y menor a 30 min.", "new_theme_added": "Nuevo tema agregado!", "method_added": "Método agregado!", "mapping_pair_added": "Mapeo agregado!", "connection_added": "Nueva conexión API agregada!", "error_unlink_license": "Error al desvincular la licencia", "error_license_scope": "El número de licencias excedido para los siguientes aplicaciones: ", "passlink_not_available": "PassLink no está disponible. <PERSON><PERSON> favor revise la conexión e intente de nuevo.", "no_match": "Las contraseñas no coinciden!", "error_obtaining_subject_apps": "Error tratando de obtener las aplicaciones del sujeto. Por favor contacte con el administrador del sistema.", "adding_credentials": "Agregando credenciales, por favor espere.", "no_credentials_to_update": "No hay credenciales para actualizar.", "executing_events": "<PERSON><PERSON><PERSON><PERSON><PERSON> eventos, por favor espere.", "error_updating_credentials": "Error al actualizar las credenciales.", "credentials_updated": "Credenciales actualizadas.", "subject_without_apps": "Aplicaciones no asignadas.", "web_not_valid": "Página web no valida.", "unable_to_login": "No se pudo iniciar sesión.", "konektor_properties_missed": "Faltan algunos ajustes en Konektor. Por favor, rev<PERSON>elos e intente de nuevo.", "confirm_element_not_found": "El elemento de confirmación no se ha encontrado por lo que no se van a actualizar las credenciales", "no_apps_assigned": "No hay aplicaciones asignadas en Pass.", "no_apps_assigned_web": "No hay aplicaciones web asignadas a su identidad para Verázial ID Pass. Por favor, asigne una aplicación web en la sección Pass de Verázial ID Admin para continuar.", "no_apps_assigned_passlink": "No hay aplicaciones asignadas en PassLink o no coinciden con las aplicaciones asignadas en Pass.", "pageNotFound": "Página no encontrada", "pageNotFoundDescription": "La página que busca no existe.", "unauthorised": "No autorizado", "unauthorised_description": "No está autorizado para acceder a esta página.", "touchTheScreen": "Toque la pantalla", "toStart": "para ", "start": "comenzar", "error_auth_token": "Error al obtener el token de autorización", "noTechSelected": "No se ha seleccionado tecnología biométrica principal", "noTechSelectedDescription": "Por favor, establezca una tecnología principal en Konektor.", "noTechsAvailable": "No hay tecnologías biométricas disponibles", "noTechsAvailableDescription": "No hay tecnologías biométricas disponibles. Por favor, contacte con el administrador del sistema.", "generalErrorTitle": "Se ha producido un error", "errorUpdatingTransferUser": "Error al actualizar el usuario de la autorización de traslado", "errorUpdatingTransferAuth": "Error al actualizar la autorización de traslado", "errorUpdatingEntryExitAuth": "Error al actualizar la autorización de entrada y salida", "generalErrorDescription": "Se ha producido un error inesperado.", "settingsErrorTitle": "Error al recuperar la configuración", "settingsErrorDescription": "Se produjo un error al intentar recuperar la configuración.", "konektorErrorTitle": "Error con las configuraciones de Konektor", "widgetSettingsErrorTitle": "Error al recuperar la configuración del widget", "widgetSettingsErrorDescription": "Se produjo un error al intentar recuperar la configuración del widget.", "widgetURLSettingsErrorTitle": "Error en la configuración de la URL del widget", "widgetURLSettingsErrorDescription": "Se produjo un error al intentar utilizar la URL del widget configurado.", "techErrorTitle": "Error de tecnología biométrica", "techErrorDescription": "No hay tecnologías biométricas establecidas.", "techErrorPayedDescription": "No hay tecnologías biométrica contratada establecida.", "techErrorAllowSearchDescription": "No hay tecnologías biométricas establecidas para identificación.", "fingerprintTechErrorTitle": "Error de tecnología de huellas dactilares", "fingerprintTechErrorDescription": "La tecnología de huellas dactilares no está establecida.", "fingerprintTechErrorPayedDescription": "La tecnología de huellas dactilares no está establecida como tecnología contratada.", "fingerprintTechErrorAllowSearchDescription": "La tecnología de huellas dactilares no está establecida como tecnología de identificación.", "fingerprintTechErrorAllowVerifyDescription": "La tecnología de huellas dactilares no está establecida como tecnología de verificación.", "irisTechErrorTitle": "Error de tecnología de iris", "irisTechErrorDescription": "La tecnología de iris no está establecida.", "irisTechErrorPayedDescription": "La tecnología de iris no está establecida como tecnología contratada.", "irisTechErrorAllowSearchDescription": "La tecnología de iris no está establecida como tecnología de identificación.", "irisTechErrorAllowVerifyDescription": "La tecnología de iris no está establecida como tecnología de verificación.", "facialTechErrorTitle": "Error de tecnología facial", "facialTechErrorDescription": "La tecnología facial no está establecida.", "facialTechErrorPayedDescription": "La tecnología facial no está establecida como tecnología contratada.", "facialTechErrorAllowSearchDescription": "La tecnología facial no está establecida como tecnología de identificación.", "facialTechErrorAllowVerifyDescription": "La tecnología facial no está establecida como tecnología de verificación.", "geolocationNotSupported": "La geolocalización no es compatible con este navegador.", "dataInconsistencyErrorTitle": "Inconsistencia de datos", "dataInconsistencyErrorDescription": "La identidad existe en el servidor de Verázial ID, pero no en su servidor biométrico. Por favor, póngase en contacto con su administrador del sistema.", "saveActionErrorTitle": "Error al guardar la acción", "saveActionErrorDescription": "Se produjo un error al intentar guardar la acción.", "getActualTimeErrorTitle": "Error al obtener la hora actual", "getActualTimeErrorDescription": "Se produjo un error al intentar obtener la hora actual.", "langModifiedSuccess": "Idioma modificado con éxito", "abortOperation": "El usuario canceló la operación", "searchAssignmentsErrorTitle": "Error al buscar las asignaciones", "searchAssignmentsErrorDescription": "Se produjo un error al intentar buscar las asignaciones.", "searchAssignmentsWarningTitle": "No se encontraron asignaciones de flujo de acciones", "searchAssignmentsWarningDescription": "No se encontraron asignaciones de flujo de acciones para el sujeto y/o ubicación.", "noActionsAvailableErrorTitle": "No hay acciones disponibles", "noActionsAvailableErrorDescription": "No hay acciones disponibles para el sujeto, dispositivo u horario actual.", "noFlowsAssignedErrorTitle": "No hay flujos asignados", "noFlowsAssignedErrorDescription": "No hay flujos asignados a el sujeto, dispositivo u horario actual.", "noFlowsAssignedErrorBothDescription": "No hay flujos asignados a el sujeto y la ubicación.", "noFlowsAssignedErrorBothScheduleDescription": "No hay flujos asignados para este sujeto, ubicación y horario.", "noFlowsAssignedErrorLocationDescription": "No hay flujos asignados a la ubicación.", "noFlowsAssignedErrorLocationScheduleDescription": "No hay flujos asignados a la ubicación en este horario.", "noFlowsAssignedErrorSubjectDescription": "No hay flujos asignados al sujeto.", "noFlowsAssignedErrorSubjectScheduleDescription": "No hay flujos asignados al sujeto en este horario.", "noFlowsAssignedErrorSubjectLocationDescription": "No hay flujos asignados al sujeto y la ubicación.", "noFlowsAssignedErrorSubjectLocationScheduleDescription": "No hay flujos asignados al sujeto y la ubicación en este horario.", "noFlowsInAssignmentTitle": "No hay flujos en la asignación", "noFlowsInAssignmentBothDescription": "No hay flujos en la asignación para el sujeto y la ubicación.", "noFlowsInAssignmentLocationDescription": "No hay flujos en la asignación para la ubicación.", "noFlowsInAssignmentSubjectDescription": "No hay flujos en la asignación para el sujeto.", "noValidFlowsFoundTitle": "No se encontraron flujos válidos", "noValidFlowsFoundDescription": "No se encontraron flujos válidos para el sujeto, dispositivo u horario actual.", "noValidFlowsFoundBothDescription": "No hay flujos válidos en la asignación para el sujeto y la ubicación.", "noValidFlowsFoundBothScheduleDescription": "No hay flujos válidos en la asignación para el sujeto y la ubicación en este horario.", "noValidFlowsFoundLocationDescription": "No hay flujos asignados para esta ubicación.", "noValidFlowsFoundLocationScheduleDescription": "No hay flujos asignados para esta ubicación y horario.", "noValidFlowsFoundSubjectDescription": "No hay flujos válidos en la asignación para el sujeto.", "noValidFlowsFoundSubjectScheduleDescription": "El acceso está restringido en este momento. Inténtelo dentro del horario autorizado.", "noValidFlowsFoundSubjectLocationDescription": "No hay flujos válidos en la asignación para el sujeto y la ubicación.", "noValidFlowsFoundSubjectLocationScheduleDescription": "No hay flujos válidos en la asignación para el sujeto y la ubicación en este horario.", "noValidAssignmentsFoundTitle": "No se encontraron asignaciones válidas", "noValidAssignmentsFoundDescription": "No hay asignaciones válidas para el sujeto y/o ubicación.", "noValidAssignmentFoundBothDescription": "No hay asignaciones válidas para el sujeto y la ubicación.", "noValidAssignmentFoundLocationDescription": "No hay asignaciones válidas para la ubicación.", "noValidAssignmentFoundSubjectDescription": "No hay asignaciones válidas para el sujeto.", "fireKonektorRelayErrorTitle": "Error al activar el relé de Konektor", "fireKonektorRelayErrorDescription": "Se produjo un error al intentar activar el relé de Konektor.", "noValidFlowsErrorTitle": "No se encontraron flujos válidos", "noValidFlowsErrorDescription": "No se encontraron flujos válidos para el sujeto, dispositivo u horario actual.", "authServerConnectionErrorTitle": "Error al conectar con el servidor de autenticación", "authServerConnectionErrorDescription": "Se produjo un error al intentar conectarse con el servidor de autenticación.", "managerSettingsErrorTitle": "Error al recuperar la configuración del Manager", "managerSettingsErrorDescription": "Se produjo un error al intentar recuperar la configuración del Manager.", "systemSettingsErrorTitle": "Error al recuperar las configuraciones del sistema", "systemSettingsErrorDescription": "Se produjo un error al intentar recuperar las configuraciones del sistema.", "systemSettingsErrorNotFound": "No se encontraron las configuraciones del sistema", "enterNumID": "Ingrese el número de ID", "invalidNumId": "Número de ID inválido", "licenseErrorDescription": "Error con la licencia, por favor contacte a su administrador del sistema", "no_data_found": "No hay datos disponibles", "no_data": "Sin datos", "DisabledLicenseTitle": "Licencia deshabilitada", "NoLicenseMessage": "No se encuentra una licencia activa para este software. Por favor, contacte con su administrador de sistemas", "error_change_tenant": "Ha ocurrido un error al cambiar de tenant", "user_created_success": "Usuario creado con éxito", "a_subject_was_created_based_on_the_user": "Usuario creado con éxito. Se creó un sujeto basado en el usuario", "location_updated": "Ubicación actualizada con éxito", "location_updated_error": "Error actualizando la ubicación", "location_created": "Ubicación creada con éxito", "location_created_error": "Error creando la ubicación", "location_deleted": "Ubicación eliminada con éxito", "location_deleted_error": "Error eliminando la ubicación", "cant_delete_actual_location": "No se puede eliminar la ubicación actual", "delete_location": "¿Está seguro de que desea eliminar la ubicación: ", "no_delete_location": "No se puede eliminar la ubicación porque está ocupada por algun sujeto", "action_created": "Acción creada con éxito", "action_created_error": "Error creando la acción", "error_locations": "Error en las ubicaciones", "error_origin_destiny_locations": "La ubicación de origen y destino no pueden ser la misma", "error_dates": "Error en las fechas", "error_departure_arrival_dates": "La fecha de salida no puede ser mayor que la fecha de llegada", "error_list_of_prisoners": "Error en la lista de prisioneros", "error_at_least_one_subject": "Debe seleccionar al menos un sujeto", "error_list_of_responsible_personel": "Error en la lista de personal responsable", "error_transfer_authorization": "Error en la autorización de traslado", "all_required_fields_details_prisoners_responsible_personnel": "Todos los campos requeridos deben ser completados, al menos un sujeto prisionero y un sujeto de personal responsable deben ser seleccionados", "success_transfer_authorization_created": "Autorización de traslado creada con éxito", "success_transfer_authorization_updated": "Autorización de traslado actualizada con éxito", "error_must_select_cancel_reason": "Debe seleccionar un motivo de cancelación", "error_subject_not_allowed_to_sign": "El sujeto no tiene el rol adecuado para firmar con biometria esta accion", "error_start_end_dates": "La fecha de inicio no puede ser mayor que la fecha de fin", "error_entry_exit_authorization": "Error en la autorización de entrada y salida", "success_entry_exit_authorization_created": "Autorización de entrada y salida creada con éxito", "success_entry_exit_authorization_updated": "Autorización de entrada y salida actualizada con éxito", "duplicate_nif": "El CID ya existe", "error_current_tenant_not_same_as_configured_in_konektor": "El tenant actual no es el mismo que el configurado en Konektor", "error_jobNotActive": "La tarea no está activa", "error_jobExecutionFailed": "La ejecución de la tarea falló, revise los logs en el servidor", "success_jobExecutionSuccess": "La tarea se ejecutó con éxito", "error_cronServiceSchedulerInit": "Error al iniciar el planificador de servicios cron, revise los logs en el servidor", "cronServiceSchedulerInitSuccess": "Planificador de servicios cron iniciado con éxito", "success_jobExecutionScheduled": "La tarea se programó para ejecutarse", "error_jobExecutionScheduled": "Error al programar la tarea, revise los logs en el servidor", "success_removingJobScheduled": "La tarea programada se eliminó con éxito", "error_removingJobScheduled": "Error al eliminar la tarea programada, revise los logs en el servidor", "error_deleting_roles": "Error al eliminar los roles", "error_adding_roles": "Error al agregar los roles", "error_retrieving_roles": "Error al obtener los roles", "error_deleting_profiles": "Error al eliminar los perfiles", "error_adding_profiles": "Error al agregar los perfiles", "error_retrieving_profiles": "Error al obtener los perfiles", "error_creating_user": "Error al crear el usuario", "error_creating_subject": "Error al crear el sujeto", "error_updating_user": "Error al actualizar el usuario", "error_updating_subject": "Error al actualizar el sujeto", "error_deleting_user": "Error al eliminar el usuario", "error_deleting_subject": "Error al eliminar el sujeto", "error_uploading_image": "Error al subir la imagen", "error_downloading_image": "Error al descargar la imagen", "error_removing_image": "Error al eliminar la imagen", "error_saving_extended_biographic_data": "Error al guardar los datos biográficos extendidos", "error_obtaining_extended_biographic_data": "Error al obtener los datos biográficos extendidos", "error_retrieving_criminal_cases": "Error al obtener los casos penales", "error_retrieving_users": "Error al obtener los usuarios", "error_retrieving_subjects": "Error al obetener los sujetos", "error_retrieving_user": "Error al obtener el usuario", "error_retrieving_subject": "Error al obtener el sujeto", "error_retrieving_user_subject_data": "Error al obtener los datos del sujeto del usuario", "error_processing_subject_deletion": "Error al procesar la eliminación del sujeto", "error_retrieving_system_report": "Error al obtener el informe del sistema", "error_retrieving_biometric_sample_count": "Error al obtener el conteo de muestras biométricas", "error_retrieving_license_report": "Error al obtener el informe de licencia", "error_retrieving_number_of_users": "Error al obtener el número de usuarios", "error_retrieving_number_of_subjects": "Error al obtener el número de sujetos", "error_retrieving_system_settings": "Error al obtener la configuración del sistema", "error_no_system_settings_found_for_application": "No se encontró la configuración del sistema para la aplicación", "error_retrieving_konektor_properties": "Error al obtener las propiedades de Konektor", "error_deleting_related_subjects": "Error al eliminar los sujetos relacionados", "error_deleting_entry_exit_auth": "Error al eliminar la autorización de entrada y salida", "error_updating_location": "Error al actualizar la ubicación", "error_creating_tenant": "Error al crear el tenant", "error_updating_tenant": "Error al actualizar el tenant", "error_deleting_tenant": "Error al eliminar el tenant", "error_creating_role_profile": "Error al crear el perfil/rol", "error_updating_role_profile": "Error al actualizar el perfil/rol", "error_deleting_role_profile": "Error al eliminar el perfil/rol", "error_retrieving_role_profile": "Error al obtener el perfil/rol", "error_deleting_transfer_auth": "Error al eliminar la autorización de traslado", "error_deleting_belongings": "Error al eliminar las pertenencias", "error_deleting_auth_schedule": "Error al eliminar el horario de autorización", "error_deleting_category": "Error al eliminar la categoría", "error_updating_task_flow": "Error al actualizar el flujo de tareas", "error_deleting_judicial_file": "Error al eliminar el archivo judicial", "locations_available_in_a_future_date": "No hay ubicaciones disponibles, pero hay {var} ubicaciones disponibles en una fecha futura: {1}", "location_available_in_a_future_date": "No hay ubicaciones disponibles, pero hay 1 ubicación disponible en una fecha futura: {1}", "error_translation_already_exists_for": "La traducción ya existe para el idioma {0}", "error_translation_record_already_exists_for_key": "El registro de traducción ya existe para la clave {0}", "user_sessions_exceeded_description": "El usuario ha excedido el número máximo de sesiones permitidas. El número máximo de sesiones permitidas es {0}.", "user_blocked_description": "El usuario está bloqueado. El usuario será desbloqueado después de {0}", "user_remaining_attempts_description": "Tiene {0} intentos restantes antes de que el usuario sea bloqueado.", "user_lock_on_next_attempt": "El usuario se bloqueará si el próximo intento de acceso es incorrecto.", "file_size_exceeded": "{0}: Tamaño de archivo no válido", "file_size_exceeded_detail": "El tamaño del archivo supera el tamaño máximo permitido de {0}", "invalid_file_type": "{0}: Tipo de archivo no válido", "invalid_file_type_detail": "Los tipos de archivo permitidos son {0}", "invalid_file_limit_detail": "El límite es de {0} archivos como máximo", "invalid_file_limit": "Se ha excedido el número máximo de archivos", "fileUploadedSuccess": "Archivo {0} subido con éxito", "fileUploadedError": "Error al subir el archivo {0}", "previewNotAvailable": "Vista previa no disponible", "downloadNotAvailable": "Descarga no disponible", "fileDownloadedSuccess": "Archivo {0} des<PERSON><PERSON> con éxito", "fileContentNotFound": "Contenido del archivo no encontrado", "error_downloading_file": "Error al descargar el archivo", "data_update_sent": "Datos actualizados localmente y enviados a la aplicación de destino.", "error_creating_case": "Error al crear el caso criminal", "error_updating_case": "Error al actualizar el caso criminal", "error_deleting_case": "Error al eliminar el caso criminal", "invalid_max_time": "El tiempo máximo no puede ser 00:00", "noValidSampleTypeSelected": "Por favor, seleccione un tipo de muestra válido para cada muestra.", "error_enrolling_subject": "No se pudo enrolar el sujeto", "duplicate_sample": "La muestra ya está enrolada en un sujeto diferente", "BAD_FILTER": "Filtro <PERSON>o", "EXTRACTION_UNKNOWN": "Extracción desconocida", "EXTRACTION_NOT_ENOUGH_QUALITY": "La muestra no cumple con los requisitos de calidad", "UNKNOWN": "Error descon<PERSON>", "NO_LICENSE": "No hay licencia disponible", "must_contain_at_least_one_mapping": "Debe contener al menos un mapeo", "index_already_used": "El índice ya está en uso", "type_already_used": "El tipo ya está en uso", "error_retrieving_system_user_profile": "Error al obtener el perfil de usuario del sistema", "cannot_delete_self": "No puede eliminarse a sí mismo", "": ""}, "options": {"true": "Sí", "false": "No", "male": "Hombre", "female": "<PERSON><PERSON>", "M": "Hombre", "F": "<PERSON><PERSON>"}, "reasons": {"biometric_auth": "Autenticación biométrica", "biometric_id": "Identificación biométrica", "biometric_login": "Login biométrico", "pass_login": "Login con contraseña", "pin_login": "Identificación con ID", "key_login": "Login con clave de un sólo uso", "no_dev": "Dispositivo no detectado", "new_subject": "<PERSON><PERSON><PERSON>", "new_sample": "<PERSON><PERSON>dir muestra biométrica", "new_picture": "Añadir foto de perfil", "update_pass": "Actualizar contraseña del usuario"}, "role": {"roles": "Roles", "new_role": "Nuevo Rol", "new_profile_role": "Nuevo Perfil/Rol", "general": "General", "accesses": "Accesos", "selected_roles": "Roles sele<PERSON>", "select_role": "Selecciona el rol", "change_role": "Cambio de rol", "select_as_default_role": "Seleccionar como rol por defecto", "update_role": "Actualizar rol", "update_profile_role": "Actualizar perfil/rol", "no_roles_available": "No hay roles disponibles"}, "role_names": {"SYSTEM_USER": "Usuario del sistema"}, "pass_application": {"applications": "Aplicaciones", "application_flows": "Procesos de Aplicación", "technology": "Tecnología", "application_type": "Tipo", "data_source": "Origen de datos", "new_application": "Nueva aplicación", "new_application_flow": "Nuevo proceso", "save_application": "Guardar aplicación", "save_application_flow": "Guardar proceso de aplicación", "full_path": "Ruta o URL", "the_window": "la ventana", "new_window": "Nueva ventana", "add_window": "<PERSON><PERSON><PERSON><PERSON>", "application": "Aplicación", "no_windows": "No hay ventanas", "target": "<PERSON><PERSON>", "edit_window": "<PERSON><PERSON> ventana", "window": "Ventana", "window_components": "Componentes de la ventana", "new_component": "Nuevo componente", "attribute_name": "Nombre del atributo", "component_type": "Tipo de componente", "trigger_order": "Orden de ejecución", "position": "Posición", "event": "Evento", "display_component_user": "Mostrar componente al usuario?", "update_application": "Actualizar aplicación", "update_application_flow": "Actualizar proceso de aplicación", "remove_application": "Eliminar aplicación", "remove_application_flow": "Eliminar proceso de aplicación", "no_applications_available": "No hay aplicaciones disponibles", "authentication": "Autenticación", "process": "Proceso", "update_credentials": "Actualizar credenciales", "appRegistry": "Aplicación", "flow_type": "Tipo de Flujo"}, "pass_datasource": {"datasources": "Origen de datos", "new_datasource": "Nuevo origen de datos", "method": "Método/URL", "parameters": "Parámetros", "update_datasource": "Act<PERSON><PERSON><PERSON>", "add_datasource": "<PERSON><PERSON><PERSON><PERSON>", "datasource": "Origen de datos", "add_parameter": "Agregar Parámetro", "parameter": "Parámetro", "add_parameters": "Agregar <PERSON>", "remove_data_source": "Eliminar origen de datos", "no_datasource_available": "No hay origen de datos disponibles"}, "pass_assigment": {"application_assigment": "Asignación de Aplicaciones", "Application_identifier": "ID de Aplicación", "authorization_id": "ID de Autorización", "application": "Aplicación", "application_flow": "Procesos de Aplicaciones", "application_flow_name": "Nombre del Proceso", "connection_type": "Tipo de conexión", "can_user_update": "Actualizar contraseña", "initialised": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "host": "Host", "subject_application": "Aplicaciones del Sujeto", "credentials": "Credenciales", "parameter": "Parámetro", "value": "Valor", "fill_credential": "Agregar Credenciales y Datos", "select_apps": "Por favor seleccione una aplicación.", "select_apps_flow": "Por favor seleccione un proceso.", "allow_user_update_password": "Permitir al usuario actualizar su contraseña.", "must_user_update_credentials": "¿Las credenciales deben ser actualizadas?", "host_name": "Dirección IP/Nombre del Host", "datagrip_row": "Ingrese el número de la fila en la tabla.", "option_dropdown": "Ingrese el texto de la opción a seleccionar en el desplegable.", "option_list": "Ingrese el texto de la opción a seleccionar en la lista.", "window": "Ventana", "application_credential": "Aplicación y Credenciales", "host_name_required": "Dirección IP/Nombre del Host es requerido."}, "headers": {"contracted_bio_tech": "Tecnologías Biométricas Contratadas", "after_searching_by_id": "Después de Buscar por ID, Permitir", "allow_searching_by": "Permitir <PERSON> por", "tech_required_enroll": "Tecnología Requerida para Enrolar", "basic_bio_fields": "Campos Biográficos Básicos", "min_quality_samples": "Calidad Mínima de Muestras", "avg_quality_samples": "Calidad Media de Muestras", "allow_verify_by": "<PERSON><PERSON><PERSON> por", "num_iris_one_n": "Num. de Iris en 1:N", "sensors_capacity": "Capacidad de los sensores", "num_fp_one_n": "<PERSON><PERSON><PERSON> en 1:N", "num_fp_roll_one_n": "<PERSON><PERSON><PERSON> en 1:N", "num_palm_one_n": "Palmas en 1:N", "min_matching_fp": "<PERSON><PERSON><PERSON> Coincid<PERSON>", "reason_uncollect_samples": "Motivo de Muestras no Capturables", "min_iris_match": "<PERSON><PERSON><PERSON> Coincidentes", "one_n_when_enrolling": "1:<PERSON> al Enrolar", "profile_type": "Tipo de Perfil", "locations": "Ubicaciones", "segmented_search": "Búsqueda Segmentada", "enroller_session_timeout": "Timeout <PERSON><PERSON><PERSON> (ms)", "licences": "Licencias", "licenses": "Licencias", "access_key": "Claves de Acceso", "external_bio_server": "<PERSON><PERSON><PERSON> (Legacy)", "config_access": "Configuración de Acceso", "access_action_reg": "Servidor de Registro de Acciones", "templates_info": "Información Sobre Templates", "verazial_id_server": "Verázial ID Server", "verazial_id_modules": "<PERSON><PERSON><PERSON><PERSON>", "external_servers": "<PERSON><PERSON><PERSON>", "biometric_server": "<PERSON><PERSON><PERSON>", "biografic_server": "<PERSON><PERSON><PERSON>", "storage_server": "<PERSON><PERSON><PERSON>", "clock_server": "Servidor Clock", "extended_bio_fields": "Campos Biográficos Extendidos", "user": "Usuarios", "roles": "Roles", "asign_modules": "Asignación de Módulos", "modules": "<PERSON><PERSON><PERSON><PERSON>", "role": "Rol", "external_guess_fields": "Campos Externo Visitante", "external_employee_fields": "Campos Externo Empleado", "tenant": "Tenant", "user_verification_userpass": "Usuario login con autenticación básica", "user_verification_biometric": "Usuario login con biometría", "password_recovery_token_expiration": "Expiración del Token de Recuperación de Contraseña", "password_expiration": "Expiración de Contraseña", "biographic_data_management_reasons": "Motivos - Gestión Datos Biográficos", "biometric_samples_management_reasons": "Motivos - Gestión Muestras Biométricas", "config_management_reasons": "Motivos - Gestión de Configuraciones", "password_management_reasons": "Motivos - Gestión Contraseñas", "location_management_reasons": "Motivos - Gestión Ubicaciones", "tenant_license_management_reasons": "Motivos - Gestión Licencias del Tenant", "role_management_reasons": "Motivos - Gestión Roles", "export_reports_management_reasons": "Motivos - Exportar Reportes", "user_management_reasons": "Motivos - Gestión Usuarios", "pass_app_management_reasons": "Motivos - Gestión Aplicaciones de Pass", "concurrent_sessions": "<PERSON><PERSON><PERSON>", "audit_trail_config": "Configuración Audit Trail", "intern_fields": "Campos Interno", "extern_provider_fields": "Campos Externo Suministrador", "physical_fields": "Campos Físicos", "visiting_time_visitors": "Tiempo Visita Visitantes", "supplier_visit_time": "Tiempo Visita Suministradores", "reasons_exit": "Motivos Salida", "allow_entry_exit": "Permit<PERSON> y Salida", "update_password": "Actualizar contraseña", "modify": "Modificar", "delete": "Eliminar", "widget_options": "Configuración de Acceso al Widget", "subjectTabsConfig": "Configuración de las Pestañas de Sujetos", "debugMode": "Modo Depuración", "streamingSamples": "Streaming de Muestras", "general": "General", "general_widget": "Configuraciones específicas del Widget", "general_clock": "Configuraciones específicas de Clock", "general_pass": "Configuraciones específicas de Pass", "segment": "Segmentos", "devices": "Dispositivo", "security": "Seguridad", "verification": "Verificación", "application_login": "Login de Aplicación", "audit_trail": "Auditoría", "page_not_found": "Página no encontrada", "unauthorized": "No Autorizado", "theme": "<PERSON><PERSON>", "subject_data_origen": "Origen de Datos de Sujetos", "api_gateway": "API Gateway", "actions_extra_data": "Acciones - Datos Extra", "new_license": "Nueva licencia", "new_api_connection": "Nueva conexión API", "update_api_connection": "Actualizar conexión API", "confirm_reduce_num_licenses": "Reduce number of licenses", "update_ldap_connection": "Actualizar conexión LDAP", "new_ldap_connection": "Nueva conexión LDAP", "tenant_licenses": "Licencias del Tenant", "user_verification": "Verificación de Usuario", "client_data_source": "Origen de Datos Cliente", "applications": "Aplicaciones", "application": "Aplicación", "application_window": "Ventana de la Aplicación", "window_components": "Componentes de la Ventana", "remove_component": "Eliminar component", "edit_window": "<PERSON><PERSON>", "save_window": "Guardar V<PERSON>", "remove_window": "Elimina<PERSON>", "save_application": "Guardar aplicación", "remove_application": "Eliminar Aplicación", "update_application": "Actualizar Aplicación", "report_error": "Reportar un Error", "data_sources": "Origen de Datos", "remove_data_source": "Eliminar Or<PERSON>n <PERSON>", "update_data_source": "Act<PERSON><PERSON><PERSON>", "add_parameters": "Agregar <PERSON>", "create_data_source": "<PERSON><PERSON><PERSON>", "assign_application": "Asignar Aplica<PERSON>", "select_language": "Seleccione un idioma", "selectLanguage": "Seleccione un idioma", "selectFlow": "Seleccione un flujo", "flowSelection": "Selección de flujo", "catalogs": "Catálogos", "passwords_complexity": "Complejidad de Contraseñas", "prisonsSettings": "Configuración de Prisiones", "belongings": "<PERSON>ten<PERSON><PERSON><PERSON>", "belonging": "Pertenencia", "belonging_record": "Registro de Pertenencia", "judicial_files": "Expedientes Judiciales", "judicial_file": "Expediente Judicial", "prisons_visits_list": "Lista de Visitas a Prisiones", "prisons_authorization_schedules": "Horarios de Autorización en Prisiones", "custom_partner_logo": "Logo Personalizado", "cron_service": "<PERSON><PERSON><PERSON>", "cron_service_config": "Configuración del Servicio Cron", "cron_service_jobs": "Ta<PERSON>s del Servicio Cron", "threshold_configs": "Configuración de Umbrales", "inputTextAreaThreshold": "Umbral de Texto de Entrada", "prometheusCredentials": "Credenciales de Prometheus", "authentication": "Autenticación", "pass_app_flow_types": "Pass - Tipos de Flujos de Aplicaciones", "criminalistics_settings": "Configuración de Criminalística", "services": "<PERSON><PERSON><PERSON>", "application_configs": "Configuraciones de Aplicación", "pass_settings": "Configuración de Pass", "clock_settings": "Configuración de Clock"}, "content": {"username": "Usuario", "password": "Contraseña", "fingerprint": "<PERSON><PERSON>", "face": "Facial", "facial": "Facial", "iris": "Iris", "primary": "Primario", "secondary": "Secundario", "name": "Nombre", "key": "Clave", "value": "Valor", "drop_down": "Desplegable", "search": "Buscar", "date": "<PERSON><PERSON>", "show_table": "Mostrar tabla", "option": "Opción", "known_position": "Posición conocida", "no_match_search_db": "Sin coincidencia, buscar en toda la DB", "search_two_fp": "Búsqueda de 2 huellas", "search_four_fp": "Búsqueda de 4 huellas", "reason": "Motivo", "search_two_iris": "Búsqueda de 2 iris", "take_one_iris": "Tomar 1 iris", "take_two_iris": "Tomar 2 iris", "min_one_iris": "Mínimo 1 iris", "min_two_iris": "Minimum 2 iris", "min_one_fp": "Mínimo 1 huella", "min_two_fp": "Mínimo 2 huellas", "min_three_fp": "Mínimo 3 huellas", "min_four_fp": "Mínimo 4 huellas", "take_one_fp": "Tomar 1 huella", "take_two_fp": "Tomar 2 huellas", "take_four_fp": "Tomar 4 huellas", "add_profile_type": "Agregar tipo de perfil", "enroller": "Enrol<PERSON>", "employee": "Empleado", "intern": "Interno", "code": "Código", "default_profile_type": "Tipo de perfil predeterminado", "location": "Ubicación", "segment": "Segmento", "relation_location": "Relación con ubicación", "device": "Dispositivo", "relation_segment": "Relación con segmento", "segmented_search": "Búsqueda segmentada", "num_available_lic": "Número de licencias disponibles", "show_licenses": "Mostrar licencias", "modify_key": "Modificar clave", "available": "Disponibles", "selected": "Seleccionados", "available_access_permissions": "Permisos de acceso disponibles", "selected_access_permissions": "Permisos de acceso seleccionados", "url": "URL", "delete_entity": "Eliminar entidad", "edit_entity": "Editar entidad", "verify": "Verificar", "related_with": "Relacionado con", "profile": "Perfil", "id_number": "Número de ID", "nothing": "<PERSON><PERSON><PERSON>", "role": "Rol", "roles": "Roles", "module": "<PERSON><PERSON><PERSON><PERSON>", "modules": "<PERSON><PERSON><PERSON><PERSON>", "show_roles": "Mostrar roles", "show_users": "Mostrar usuarios", "show_assigments": "<PERSON><PERSON> tareas", "left_iris": "<PERSON>", "right_iris": "<PERSON> derecho", "left_hand": "<PERSON><PERSON>", "right_hand": "<PERSON><PERSON> der<PERSON>a", "tenant": "Tenant", "select": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "enable_edit": "Habilitar edición", "errorTitle": "Error", "successMessage": "Guardado o eliminado satisfactoriamente!", "successTitle": "<PERSON><PERSON><PERSON><PERSON>", "download": "<PERSON><PERSON><PERSON>", "tenant_name": "Nombre del tenant", "show_tenants": "Mostrar tenants", "version": "Versión", "description": "Descripción", "new_version": "Nueva versión", "show_access": "Mostrar Accesos", "user_lock_time": "Tiempo de Bloqueo", "expiration_time": "Tiempo de Expiración", "user_num_attempts": "Número de Intentos", "is_enable_lock_user": "Habilitar bloqueo de usuarios", "user_lock_time_by": "Tiempo de Bloqueo en", "expiration_time_in": "Tiempo de Expiración en", "second": "<PERSON><PERSON><PERSON>", "minute": "<PERSON><PERSON><PERSON>", "hour": "<PERSON><PERSON>", "day": "Días", "number_sessions": "Número de sesiones", "action_name": "Nombre de la acción", "is_enable": "Está habilitado?", "new_action": "Nueva Acción", "ecualize_images": "<PERSON><PERSON><PERSON><PERSON> fotos", "identify_with_template": "Utilizar templates en búsquedas", "copy_data": "<PERSON><PERSON><PERSON> da<PERSON>", "duplicate_version": "Este producto ya existe en la BBDD.", "file_not_supported": "Archivo no soportado. Los archivos permitidos son .zip, .exe and .msi.", "successfully_removed": "Eliminado satisfactoriamente.", "number": "Número", "allow": "<PERSON><PERSON><PERSON>", "device_used": "Dispositivos usados", "selectColumns": "Seleccionar columnas", "columnsSelected": "<PERSON>umnas selecci<PERSON>", "selectedColumns": "<PERSON>umnas selecci<PERSON>", "biometricSearch": "Búsqueda biométrica", "Identification": "Identificación", "subjectIdentification": "Identificación de sujeto", "userIdentification": "Identificación de usuario", "schedule": "<PERSON><PERSON><PERSON>", "start": "<PERSON><PERSON>o", "end": "Fin", "monday": "<PERSON><PERSON>", "tuesday": "<PERSON><PERSON>", "wednesday": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "thursday": "<PERSON><PERSON>", "friday": "Viernes", "saturday": "Sábado", "sunday": "Domingo", "subject": "Sujeto", "subject_profile": "Perfil de sujeto", "user": "Usuario", "user_role": "Rol de usuario", "monday_to_friday": "Lunes a viernes", "customised": "Personalizado", "everyday": "Todos los días", "type": "Tipo", "to": "a", "level": "<PERSON><PERSON>", "must_update": "El usuario debe cambiar la contraseña en el próximo inicio de sesión.", "email": "Email", "repeat_password": "Repetir la contraseña", "welcome": "Bienvenido", "lastAccess": "Último acceso", "select_reason": "Seleccione un motivo", "new_password": "Nueva contraseña", "active": "Activo", "inactive": "Inactivo", "under_verification": "Bajo verificación", "documents": "Documentos", "profiles": "<PERSON><PERSON><PERSON>", "tattoos": "<PERSON><PERSON><PERSON><PERSON>", "scars": "Cicatrices", "select_camera": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "enterNumID": "Ingrese el número de ID", "replace_image": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "general": "General", "server": "<PERSON><PERSON><PERSON>", "hardDrive": "Disco duro", "ram": "Memoria RAM", "biometricServer": "Servidor biométrico", "subjectsDB": "Sujetos BD", "numSubjects": "Número de sujetos", "usersDB": "Usuarios BD", "numUsers": "Número de usuarios", "numRecords": "Número de registros", "status": "Estado", "noDataAvailable": "No hay datos disponibles", "noPhotosAdded": "No hay fotos añadidas", "showing": "Mostrando", "of": "de", "records": "registros", "requiredFields": "Campos requeridos", "forgot_password": "¿Olvidaste tu contraseña?", "oneTimePasswordVerification": "Verificación con contraseña de un solo uso", "enterAccessKey": "Ingrese la clave de acceso", "accessKey": "Clave de acceso", "createSubjectToContinue": "Cree al sujeto para continuar", "takePhoto": "Tomar foto", "changePhoto": "Cambiar foto", "rolledFingerprint": "<PERSON><PERSON> rolada", "palm": "Palma", "minimumCoincidence": "Coincidencia <PERSON>", "match": "Match", "enroll": "Enroll", "error_title": "Error", "error_message_required_fields": "Por favor, complete los campos requeridos.", "error_duplicate_location": "La ubicación ya existe.", "error_duplicate_segment": "El segmento ya existe.", "error_duplicate_device": "El dispositivo ya existe.", "error_duplicate_option": "La opción ya existe.", "some_duplicate_option": "Alguna opción ya existe.", "success_message": "Procesado satisfactoriamente!", "success_title": "<PERSON><PERSON><PERSON><PERSON>", "try_again": "Intentar de nuevo", "auto_enroll": "Auto enrolar", "option_not_valid": "Opción no válida", "no_reasons_configured": "Motivos no configuradas", "data_inconsistent": "Datos inconsistentes. Por favor, actualice la página.", "reason_change": "Motivo para modificar", "showAdditionalTabs": "Mostrar Pestañas Adicionales", "restrictTabToSpecificRoles": "Restringir Pestaña a Perfiles Específicos", "specificRoles": "<PERSON><PERSON><PERSON>ecí<PERSON>", "showPrisonTab": "Mostrar Pestaña de Prisons", "showProfilePictureTab": "Mostrar Pestaña de Historial de Fotos", "showExtendedBioFieldsTab": "Mostrar Pestaña de Campos Biográficos Extendidos", "showPhysicalDataTab": "Mostrar Pestaña de Datos Físicos", "showRelatedSubjectsTab": "Mostrar Pestaña de Sujetos Relacionados", "showLocationsTab": "Mostrar Pestaña de Ubicaciones", "showEntriesExitsTab": "Mostrar Pestaña de Entradas y Salidas", "showEntryExitAuthorizationsTab": "Mostrar Pestaña de Autorizaciones de Entrada y Salida", "enableDebugMode": "Habilitar Modo <PERSON>", "autoDelete": "Eliminación automática de datos biométricos", "api_credentials": "Credenciales API", "role_assigned": "Los siguientes roles han sido asignados a los siguiente usuarios:", "confirmation_continue": "¿Está seguro de continuar?", "tenant_not_valid": "Tenant no valido.", "confirmation": "Confirmación", "show_actions": "Mostrar acciones", "field_required": "Campo requerido", "options": "Opciones", "capture_one_iris": "Tomar 1 iris", "capture_two_iris": "Tomar 2 iris", "one_fingerprint": "1 huella", "two_fingerprints": "2 huellas", "three_fingerprints": "3 huellas", "four_fingerprints": "4 huellas", "timeoutGeneral": "Timeout General (s.)", "timeoutInactivity": "Timeout de Inactividad (s.)", "timeoutCapture": "Timeout de <PERSON> (s.)", "timeoutStaticMessage": "Timeout de Mensajes <PERSON> (s.)", "timeoutNotification": "Timeout de Notificaciones (s.)", "timeoutRequestsServer": "Timeout de Peticiones al Servidor (s.)", "faceWidthMin": "<PERSON><PERSON><PERSON> (px.)", "matchAutoFaceCapture": "Captura Facial automática (Verázial-ID Match)", "matchAutoFaceCaptureTime": "Tiempo de Captura Facial automática (s.)", "autoPalmCapture": "Captura de Palma automática", "showSuccess": "<PERSON><PERSON>ar mensaje de éxito", "autoPalmCaptureTime": "Tiempo de Captura de Palma automática (s.)", "minQualityFile": "Usar calidad mínima para muestras de fichero", "matchAutoOn": "Auto Encendido (Verázial-ID Match)", "language": "Idioma", "languageForAlerts": "Idioma para Alertas", "logLevel": "<PERSON><PERSON>", "checkWithKonektor": "Comprobar con Konektor", "version1": "Versión 1.0", "sipCompatibility": "Compatibilidad SIP", "admin_profile_type": "Perfil de Administrador", "enroller_profile_type": "Perfil de Enrolador", "show_fields": "Mostrar campos", "show_data": "<PERSON><PERSON> datos", "show_jobs": "<PERSON><PERSON> tareas", "show_field_groups": "Mostrar grupos de campos", "extended_fields": "Campos extendidos", "quality_thumbnail": "Calidad de la miniatura", "config_quality_thumbnail": "Modificar la calidad de la miniatura", "config_size_thumbnail": "Modificar el tamaño máximo de la miniatura", "size_thumbnail": "Tamaño máximo de la miniatura", "left_little": "<PERSON><PERSON><PERSON>", "left_ring": "<PERSON><PERSON>", "left_middle": "Medio izquierdo", "left_fore": "<PERSON><PERSON><PERSON>", "left_thumb": "<PERSON><PERSON><PERSON>", "right_thumb": "Pulgar derecho", "right_fore": "<PERSON>ndice derecho", "right_middle": "Medio derecho", "right_ring": "Anular derecho", "right_little": "<PERSON><PERSON><PERSON> derecho", "left_little_ring": "Meñique y anular i<PERSON>", "left_middle_fore": "Medio e índice izquierdos", "thumbs": "<PERSON>ul<PERSON><PERSON>", "right_middle_fore": "Medio e índice derechos", "right_little_ring": "Meñique y anular derechos", "left_upper_palm": "Palma superior izquierda", "left_lower_palm": "Palma inferior izquierda", "left_writer_palm": "Palma lateral izquierda", "right_upper_palm": "Palma superior derecha", "right_lower_palm": "Palma inferior derecha", "right_writer_palm": "Palma lateral derecha", "second_search": "2da búsqueda", "related_to": "Relacionado a", "values": "Valores", "delete_title": "Eliminar", "delete_license": "¿Está seguro que desea eliminar la/las licencia(s) seleccionada(s)? Esta acción también eliminará las licencias asignadas. Esta ação também removerá todas as licenças atribuídas.", "installer": "Instalador", "upload": "Archivo", "delete_application": "¿Está seguro de eliminar la aplicación seleccionada?", "delete_confirmation": "¿Está seguro de eliminar el registro seleccionado?", "getSubjectInfoEndpoint": "Endpoint Información de Sujetos", "getActionsInfoEndpoint": "Endpoint Información de Acciones", "mainStrongBioTech": "Tecnología Biométrica Principal", "show_reasons": "Mostrar motivos", "show_profiles": "<PERSON>rar perfiles", "show_segments": "Mostrar segmentos", "guid": "GUID", "serial_number": "Núm. Serie", "mac": "MAC", "ip": "IP", "created_at": "<PERSON><PERSON><PERSON>", "show_locations": "Mostrar ubicaciones", "show_relations": "Mostrar relaciones", "show_devices": "Mostrar dispositivos", "show_used_devices": "Mostrar dispositivos usados", "relation_add": "Relación agregada", "relations_add": "Relaciones agregadas", "cannot_remove_device_used": "No se puede eliminar el dispositivo porque está vinculado a alguna licencia", "cannot_remove_segment_used": "No se puede eliminar el segmento porque está vinculado a alguna licencia", "cannot_remove_location_used": "No se puede eliminar la ubicación porque está vinculado a alguna licencia", "devices": "Dispositivos", "segments": "Segmentos", "relationships": "Relaciones", "field_type": "Tipo de campo", "field_already_exists": "El campo ya existe", "parameter": "Parámetro", "must_not_contain_spaces_or_special_characters": "No debe contener espacios o caracteres especiales", "input": "Input", "min_characters": "<PERSON><PERSON><PERSON>", "must_be_less_than_max": "Debe ser menor que el máximo", "max_characters": "Máximo de caracteres", "must_be_greater_than_min_and_0": "Debe ser mayor que el mínimo y 0", "required": "Requerido", "dropdown": "Dropdown", "option_already_exists": "La opción ya existe", "at_least_one_option_required": "Al menos una opción es requerida", "toggle": "Toggle", "api_gateway": "API Gateway", "modify_api_gateway": "Modificar API Gateway", "theme": "<PERSON><PERSON>", "default_theme": "<PERSON><PERSON> por defecto", "show_themes": "Mostrar temas", "data_origen": "Origen de datos", "ldap_host": "Servidor LDAP", "ldap_port": "Puerto LDAP", "bind_dn": "Link DN", "search_base": "Base de búsqueda", "local": "Local", "external_api": "API externa", "ldap": "LDAP", "admin_user_guid": "GUID Usuario Administrador", "applied_to": "Aplicado a", "both": "Ambos", "criminology": "Mostrar imagenes", "nist_score": "Puntuación NIST", "other": "<PERSON><PERSON>", "assigned": "<PERSON><PERSON><PERSON><PERSON>", "used": "Usadas", "used_by_application": "Por aplicación", "maintenance": "Mantenimiento", "appsStatus": "Estado Apps", "mServiceStatus": "Estado de los micro servicios", "secuential_enroll": "Enroll secuencial", "collapse_all": "Colapsar Todos", "expand_all": "Expandir <PERSON>", "total_licenses": "Total Licencias", "licenses": "Licencias", "application": "Aplicación", "license": "Licencia", "enable": "Habilitado", "last_used": "Último uso", "available_licenses": "Licencias disponibles: ", "first_used": "Primer uso", "updated_at": "Actualizado el", "delete_single_license": "¿Está seguro que desea eliminar la licencia seleccionada?", "delete_multiple_license": "¿Está seguro que desea eliminar las licencias seleccionadas?", "user_default_datasource": "Origen de datos de usuarios por defecto", "subject_default_datasource": "Origen de datos de sujetos por defecto", "show_api_connections": "Mostrar conexiones API", "show_ldap_connections": "Mostrar conexiones LDAP", "id": "ID", "api_token": "Token API", "admin_user_object_guid": "ID del usuario administrador", "admin_user_local_role_id": "Rol ID del usuario administrador", "api_methods": "Métodos API", "connection_name": "Nombre", "allow_updating": "Actualizar", "allow_deleting": "Eliminar", "allow_creating": "<PERSON><PERSON><PERSON>", "allow_reading": "<PERSON><PERSON>", "is_active": "Activo", "user_subject_field_id": "Campo Index", "credentials": "Credenciales", "method": "<PERSON><PERSON><PERSON><PERSON>", "action": "Acción", "header_parameters_mapping": "Parámetros de cabecera", "api_request_mapping": "Parámetros de la petición", "api_response_mapping": "Parámetros de la respuesta", "source": "Origen", "target": "<PERSON><PERSON>", "HTTP_GET": "GET", "HTTP_POST": "POST", "HTTP_PUT": "PUT", "HTTP_DELETE": "DELETE", "API_READ": "LEER", "API_WRITE": "ESCRIBIR", "API_UPDATE": "ACTUALIZAR", "API_CREATE": "CREAR", "API_DELETE": "ELIMINAR", "info": "Información", "show_methods": "<PERSON><PERSON>", "local_method_name": "Nombre del método local", "camerasFromKonektor": "<PERSON><PERSON><PERSON><PERSON> Ko<PERSON>kt<PERSON>", "autoOnWidget": "Auto On (Widget)", "autoCaptureWidget": "Captura facial automática (Widget)", "autoCaptureTimeFacialWidget": "Tiempo de captura facial automática (Widget)", "readingWaitTimePass": "Tiempo de espera de lectura", "request_json_string": "Petición (formato JSON)", "response_json_string": "Respuesta (formato JSON)", "licenses_used": "Licencias usadas", "verazial_loading": "Verázial ID Pass se está iniciando ....", "select_tech": "Seleccione una tecnología", "dear": "Estimad(a)", "select_app": "Por favor seleccione el proceso de aplicación que desea abrir.", "select_process": "Por favor seleccione el proceso que desea ejecutar.", "success_login": "Indentidad encontrada", "error_login": "Identidad no encontrada", "loading_apps": "Cargando Aplicaciones", "technology": "Tecnología", "application_type": "Tipo de aplicación", "data_source_type": "Tipo de origen", "data_source": "Origen de datos", "attribute_name": "Nombre del atributo", "component_type": "Tipo de componente", "position": "Posición", "event": "Evento", "saved_successfully": "Guardado satisfactoriamente!!!", "updated_successfully": "Actualizado satisfactoriamente!!!", "removed_successfully": "Eliminado satisfactoriamente!!!", "select_apps": "Por favor seleccione una aplicación.", "full_path": "Path de la aplicación", "display_name": "Nombre a mostrar", "credentials_not_found": "Credenciales no encontradas en el origen de datos.", "window_title": "<PERSON><PERSON><PERSON><PERSON> de la ventana", "window": "Ventana", "trigger_order": "Orden de ejecución", "first_name": "Primer <PERSON>mbre", "second_name": "<PERSON><PERSON><PERSON>", "last_name": "Primer <PERSON><PERSON><PERSON><PERSON>", "connection_type": "Tipo de Conexión", "subjects": "Sujetos", "subject_application": "Aplicaciones del Sujeto", "application_name": "Nombre de la Aplicación", "application_information": "Información de la Aplicación", "application_identifier": "Identificador de la Aplicación", "host": "Host", "fill_credential": "Agregar credenciales y datos", "fields_not_valid": "Campos no válidos", "order": "Orden", "loading_user_info": "Cargando información del usuario...", "loading_app_data": "Cargando datos de la aplicación...", "loading_cred_ext": "Cargando credenciales - externo...", "loading_cred_int": "Cargando credenciales - internal...", "can_user_update": "¿Actualizar contraseña?", "method_not_implemented": "Método no implementado", "update_password": "Actualizar <PERSON>", "password_requirements": "Requisitos de la contraseña", "password_req_1": "12 caracteres", "password_req_2": "1 may<PERSON><PERSON>", "password_req_3": "1 minúscula", "password_req_4": "1 caracter especial", "password_req_5": "1 valor numérico", "confirm_change_password": "¿Está seguro que quiere cambiar su contraseña?", "update_credentials": "Actualizar credenciales", "admin_application": "aplicación de administración", "user_application": "User Application", "host_name": "Dirección IP/Nombre del host", "administration": "Administración", "couldnt_connect_server": "No se pudo conectar al servidor.", "lastname": "Apellido", "num_id": "NumID", "display_component_user": "Mostrar el componente al usuario?", "show_component": "Mostrar componente", "credentials_updated": "Credenciales actualizadas satisfactoriamente!", "credentials_not_updated": "No se pudo actualizar las credenciales.", "select_user": "Por favor seleccione al menos un usuario.", "restarting_pass": "Verázial ID Pass se recargará en unos segundos.", "must_user_update_credentials": "¿Deben ser las credenciales inicializadas?", "no_applications_available": "Aplicaciones no disponibles.", "apps_available": "Aplicaciones disponibles", "windows_available": "Ventanas disponibles", "drag_drop": "Arrastrar y soltar ", "the_window": "la ventana", "message_remove": "¿Está seguro que quiere eliminar ", "window_exist": "Esta ventana ya esiste.", "publish": "Publicar", "published": "Publicado", "unpublish": "Despublicar", "no_published": "No publicado", "window_used": "Esta ventana ya está en el flujo", "error_delete": "Error tratando de eliminar", "error_save": "Error tratando de guardar", "error_update": "Error tratando de actualizar", "error_web_app": "Web Login y firma digital se permite unicamente con Pass Web Extension.", "user_not_found": "Usuario no encontrado en Admin.", "report_login_error": "Reportar un error de login", "observation": "Observación", "no_datasource_available": "No hay origenes de datos disponibles", "edit": "<PERSON><PERSON>", "parameters": "Parámetros", "message_update_data_source": "¿Está seguro de que quiere actualizar ", "message_add_parameters": "¿Está seguro de agregar los nuevos parámetros a ", "no_initialised": "¿Inicializado?", "please_wait": "Por favor espere", "is_starting": "está iniciando", "allow_user_update_password": "Permitir al usuario actualizar su contraseña", "input_user_id": "Escribir el número de ID", "min_lowercase": "Debe contener al menos 1 letra minúscula", "min_uppercase": "Debe contener al menos 1 letra mayúscula", "min_digits": "<PERSON>be contener al menos 1 número", "min_special_char": "Debe contener al menos 1 caracter especial", "API_TOKEN": "Token API", "API_ENDPOINT_PARAMETER": "Parámetro de Endpoint API", "API_USERNAME": "Nombre de usuario API", "API_PASSWORD": "Contraseña API", "API_RESULT_PARAM": "Parámetro de Resultado API", "API_SEARCH_FIELD": "Campo de Búsqueda API", "LDAP_PASSWORD": "Contraseña LDAP", "LDAP_USERNAME": "Nombre de usuario LDAP", "LDAP_BIND_DN": "DN de enlace LDAP", "LDAP_SEARCH_BASE": "Base de Búsqueda LDAP", "LDAP_DOMAIN": "Dominio LDAP", "LDAP_PORT": "Puerto LDAP", "LOCAL_METHOD": "Metodo local", "LOGIN_USERNAME": "Login - Nombre de usuario", "LOGIN_PASSWORD": "Login - Contraseña", "month01": "enero", "month02": "febrero", "month03": "marzo", "month04": "abril", "month05": "mayo", "month06": "junio", "month07": "julio", "month08": "agosto", "month09": "septiembre", "month10": "octubre", "month11": "noviembre", "month12": "diciembre", "availableActions": "Acciones disponibles", "submit": "Enviar", "flow": "F<PERSON>jo", "selectFlowToContinue": "Seleccione un flujo para continuar", "verification_1to1_enabled": "Verificación de ID", "LDAP_SSL": "SSL LDAP", "ssl_connection": "Conexión SSL", "group": "Grupo", "select_group": "Seleccione un grupo", "new_related_subject": "Nuevo sujeto relacionado", "related_subject": "Sujeto relacionado", "relationship": "Relación", "isVisitor": "Es visitante", "comments": "Comentarios", "see_who_relates_to_this_subject": "Ver quién está relacionado con este sujeto", "subject_relationships": "Relaciones del sujeto", "new_catalog": "Nuevo catálogo", "new_location": "Nueva ubicación", "regex": "Expresión regular", "isPrisonsEnabled": "Prisons habilitado", "prisonerProfileId": "ID de perfil de prisionero", "showBelongingsTab": "Mostrar pestaña de pertenencias", "showJudicialFileTab": "Mostrar pestaña de expediente judicial", "belongings": "<PERSON>ten<PERSON><PERSON><PERSON>", "new_belonging": "Nueva pertenencia", "new_belonging_record": "Nuevo registro de pertenencia", "belongingType": "Tipo", "belongingDescription": "Descripción", "registrationDate": "<PERSON>cha de registro", "subjectReceptionSignatureDate": "Fecha firma de entrega de pertenencias", "subjectReceptionSignatureTech": "Tecnología de firma de entrega de pertenencias", "subjectReturnSignatureDate": "Fecha firma de recuperación de pertenencias", "subjectReturnSignatureTech": "Tecnología de firma de recuperación de pertenencias", "receptionUserSignatureDate": "Fecha firma de recepción de pertenencias por el usuario", "receptionUserSignatureTech": "Tecnología de firma de recepción de pertenencias por el usuario", "receptionUserSignatureNumId": "Número de ID del usuario que recibe", "returnUserSignatureDate": "Fecha firma de devolución de pertenencias", "returnUserSignatureTech": "Tecnología de firma de devolución depertenencias", "returnUserSignatureNumId": "Número de ID de del usuario que realizó la devolución", "new_record": "Nuevo registro", "returned": "Devuel<PERSON>", "received": "Recibido", "registered": "Registrado", "subject_reception_signature": "Firma entrega de pertenencias", "signature_of": "Firma del sujeto", "signature_responsible_of": "Firma del usuario responsable", "user_reception_signature": "Firma recepción de pertenencias", "subject_return_signature": "Firma recuperación de pertenencias", "user_return_signature": "Firma devolución de pertenencias", "user_coincidence_signature": "Firma de confirmación de coincidencia", "sign": "<PERSON><PERSON><PERSON>", "judicial_file_fields": "Campos de expediente judicial", "judicialFile": "Expediente judicial", "new_judicial_file": "Nuevo expediente judicial", "new_schedule": "Nuevo horario", "edit_schedule": "<PERSON><PERSON> ho<PERSON>", "roleId": "ID de rol", "locationId": "ID de ubicación", "details": "Detalles", "configuration": "Configuración", "uploaded": "Subido", "partner_login_logo_enabled": "Habilitar logo personalizado en la pantalla de login", "partner_topbar_logo_enabled": "Habilitar logo personalizado en la barra superior", "entry-exit-control-role-relation-restrictions": "Restricciones de relación de perfil para control de entradas y salidas", "show-entry-exit-control-role-relation-restrictions": "Mostrar restricciones de relación de perfiles para el control de entradas y salidas", "entry-exit-settings": "Configuración de control de entradas y salidas", "new_restriction": "Nueva restricción", "new_role_relation_restriction": "Nueva restricción de relación de roles", "entryRole": "Rol de entrada", "subjectWithRole": "Sujeto con perfil", "relatedRole": "Rol relacionado", "hasToHaveARelatedSubjectWithRole": "Tiene que tener un sujeto relacionado con perfil", "new_transfer": "Nuevo traslado", "new_transfer_auth": "Nueva autorización de traslado", "edit_transfer_auth": "Editar autorización de traslado", "execute_transfer_exit": "Ejecutar salida de traslado", "execute_transfer_entry": "Ejecutar entrada de traslado", "execute": "<PERSON><PERSON><PERSON><PERSON>", "execute_schedule": "Programar", "remove_schedule": "Eliminar programación", "authCode": "Código de autorización", "authReason": "Motivo de traslado", "authRegistrationDate": "Fecha de registro de autorización", "authExpirationDate": "Fecha de expiración de autorización", "originLocation": "Ubicación de origen", "destinyLocation": "Ubicación de destino", "plannedDepartureDateTime": "Fecha y hora de salida planificada", "plannedArrivalDateTime": "Fecha y hora de llegada planificada", "actualDepartureDateTime": "<PERSON><PERSON> y hora de salida actual", "actualArrivalDateTime": "<PERSON><PERSON> y hora de llegada actual", "listOfPrisoners": "Lista de prisioneros", "listOfResponsiblePersonel": "Lista de personal responsable", "listOfResponsible": "Lista de responsables", "authDate": "Fecha de autorización", "authUser": "Usuario autorizador", "isCompleted": "Completado", "isCancelled": "Cancelado", "cancelDate": "Fecha de cancelación", "cancelUser": "Usuario que canceló", "createdBy": "<PERSON><PERSON>o por", "updatedBy": "Actualizado por", "createdAt": "Creado el", "updatedAt": "Actualizado el", "obtainedAt": "<PERSON><PERSON> reco<PERSON>o", "signatures": "<PERSON><PERSON><PERSON>", "enter_numId_to_verify": "Ingrese el número de ID para verificar de lo contrario presione cualquier tecnología para identificar", "signedOn": "Firmado el", "authSignature": "Firma de autorización", "transferExitSignature": "Firma de salida de traslado", "transferEntrySignature": "Firma de entrada de traslado", "authUserSignatureTech": "Tecnología de firma de usuario autorizador", "subjectWhoAuthrizes": "Sujeto que autoriza", "transferSubject": "Sujeto de traslado", "transferResponsible": "Responsable de traslado", "cancelSignature": "Firma de cancelación", "cancelUserSignatureTech": "Tecnología de firma de usuario que canceló", "subjectWhoCancels": "Sujeto que cancela", "cancelReason": "Motivo de cancelación", "cancelObservation": "Observación de cancelación", "prisonerTabs": "Pestañas de prisionero", "transferAuthConfig": "Configuración de autorización de traslado", "biometricSignaturesConfig": "Configuración de firmas biométricas", "transferAuthExpiration": "Expiración de autorización de traslado", "transferAuthExpirationConfig": "Configuración de expiración de autorización de traslado", "responsibleSubjectsRoles": "Perfiles de sujetos responsables", "showTransferAuthDetails": "Mostrar detalles de autorización de traslado", "transfer_auth_details_fields": "Campos de detalles de autorización de traslado", "authorized_subject_roles_to": "Perfiles de sujeto autorizados para", "authorized_subject_roles_for": "Perfiles de sujeto autorizados para", "authorized_subject_roles_to_sign_on": "Perfiles de sujeto autorizados para firmar en", "belongingsReception": "Recepción de pertenencias", "belongingsReturn": "Devolución de pertenencias", "authorizeTransferAuths": "Autorizar autorizaciones de traslado", "cancelTransferAuths": "Cancelar autorizaciones de traslado", "authorizePrisonerEntryExit": "Autorizar entrada/salida de prisionero", "signPrisonerEntryExit": "Firmar entrada/salida de prisionero", "biometricSignaturesAuthorizedSubjectRoles": "Perfiles de sujeto autorizados para firmas biométricas", "max_inside_time": "Tiempo máximo dentro (hh:mm)", "actual_inside_time": "Tiempo dentro (hh:mm)", "signature": "Firma", "new_authorization": "Nueva autorización", "edit_authorization": "Editar autorización", "authStartDateTime": "Fecha y hora de inicio de autorización", "authEndDateTime": "Fecha y hora de fin de autorización", "entry": "Entrada", "exit": "Salida", "showEntryExitAuthDetails": "Mostrar detalles de autorización de entrada/salida", "entry_exit_auth_details_fields": "Campos de detalles de autorización de entrada/salida", "isRequiredWithinSchedule": "Es requerido dentro del horario", "requiredSchedule": "<PERSON><PERSON><PERSON>", "alertIfAllPerformedWithinSchedule": "Alertar si todos han fichado dentro del horario", "alertIfNotPerformedWithinSchedule": "Alertar si no se ha realizado dentro del horario", "alertIfPerformedOutsideSchedule": "Alertar si se realiza fuera del horario", "usersToAlert": "Usuarios a alertar", "new_job": "Nueva tarea", "edit_job": "<PERSON><PERSON>", "time": "Tiempo", "timeIn": "Tiempo en", "limitReached": "Lí<PERSON>", "completed_on_time": "Completado", "completed_late": "Completado", "on_time": "Pendiente", "late": "Pendiente", "timeInit": "Hora de inicio", "timeEnd": "Hora de fin", "timeInitEnd": "Hora de inicio y fin", "dailyWithinTimeRange": "Diario dentro de rango de tiempo", "presEnterToAdd": "Presione Enter para agregar", "record_already_exists": "El registro ya existe", "showInMenu": "Mostrar en menú", "triggerInitJobScheduler": "Iniciar programador de tareas", "scheduleJob": "Programar tarea", "removeJobSchedule": "Eliminar programación de tarea", "specificDateTimeStart": "Fecha y hora específica de inicio para la tarea", "dateTimeStart": "Fecha y hora de inicio", "baseIdentifier": "Identificador (Proceso/URL)", "appRegistry": "Aplicación", "errorMessageToDisplay": "<PERSON><PERSON><PERSON> a mostrar", "translations": "Traducciones", "show_translations": "<PERSON><PERSON> traducci<PERSON>", "languageCode": "Código de idioma", "inputTextAreaThreshold": "Número mínimo de caracteres para mostrar el área de texto en formularios personalizados", "userSubjectLazyLoadThreshold": "Número mínimo de caracteres para cargar los sujetos/usuarios con lazy load", "up": "Activo", "down": "Inactivo", "current_password": "Contraseña actual", "show_app_flow_types": "Mostrar tipos de flujos", "flow_types": "Tipo de flujos", "allowCopyPaste": "<PERSON><PERSON><PERSON> cop<PERSON>/pegar", "datasource_link_field": "Campo de enlace con el origen de datos", "name_translations": "Traducciones de nombre", "showFilesTab": "Mostrar Pestaña de Archivos", "showSubjectFileTypesList": "Mostrar Lista de Tipos de Archivos de Sujeto", "subjectFileTypes": "Tipos de Archivos de Sujeto", "showSubjectFileGroupList": "Mostrar Lista de Grupos de Archivos de Sujeto", "subjectFileGroups": "Grupos de Archivos de Sujeto", "noSubjectFileGroupsConfigured": "No hay grupos de archivos de sujeto configurados, por favor contacte a su administrador.", "evidenceFilesTab": "Pestaña de Evidencia", "comparatorTab": "Pestaña de comparador", "showEvidenceFileTypesList": "Mostrar Lista de Tipos de Archivos para Evidencia", "evidenceFileTypes": "Tipos de Archivos para Evidencia", "showEvidenceFileGroupList": "Mostrar Lista de Grupos de Archivos para Evidencia", "evidenceFileGroups": "Grupos de Archivos para Evidencia", "noEvidenceFileGroupsConfigured": "No hay grupos de archivos para evidencia configurados, por favor contacte a su administrador.", "uploadNewFile": "Subir Nuevo Archivo", "no_files_available": "No hay archivos disponibles", "alias": "<PERSON><PERSON>", "subjectFileRestrictions": "Restricciones de Archivos de Sujeto", "acceptedFileTypes": "Tipos de Archivos Aceptados", "acceptedFileTypesToolTip": "Lista separada por comas de patrones para restringir los tipos de archivos permitidos. Puede ser cualquier combinación de los tipos MIME (como 'image/*') o las extensiones de archivo (como '.jpg').", "maxFileSize": "Tamaño máximo de archivo (bytes)", "maxFileSizeTooltip": "Tamaño máximo de archivo en bytes.", "maxResults": "Máximo de Resultados", "maxResultsTooltip": "<PERSON><PERSON><PERSON><PERSON> entre 0-100", "coincidencePercentage": "Porcentaje mínimo de coincidencia", "coincidencePercentageTooltip": "<PERSON><PERSON><PERSON><PERSON> entre 0-100", "dragAndDropFilesHereToUpload": "Arrastre y suelte los archivos aquí para subir", "pending": "Pendiente", "completed": "Completado", "file": "Archivo", "remove": "Eliminar", "preview": "Vista previa", "mimeType": "Tipo de archivo", "uploadFile": "Subir Archivo", "selectOption": "Seleccionar opción", "isCriminalisticsEnabled": "Criminalística habilitada", "isEnrollByFileEnabled": "Habilitar enrolamiento por archivo", "isManageSubjectWithoutVerificationEnabled": "Habilitar gestión de sujetos sin verificación", "subjectMustExistToAccessThisData": "El sujeto debe existir para acceder a estos datos", "unavailable": "No disponible", "liveSamples": "Muestras en vivo", "fileSamples": "Muestras por fichero", "fileAndLiveSamples": "Muestras por fichero y en vivo", "captureMode": "Modo de captura de muestras (Enroll)", "captureModeMatch": "Modo de captura de muestras (Match)", "criminalistic_case_details_fields": "Campos de información adicional de creación de casos", "selectCase": "<PERSON><PERSON><PERSON><PERSON><PERSON>(s)", "selectSubject": "<PERSON><PERSON><PERSON><PERSON><PERSON>(s)", "selectUser": "<PERSON><PERSON><PERSON><PERSON><PERSON>(s)", "no_created_cases": "No tienes casos creados", "no_created_comments_case": "No tienes comentarios relacionados a este caso", "no_created_evidences": "No tienes ninguna evidencia", "no_created_coincidences": "No tienes ninguna coincidencia", "add_from_evidences": "Por favor agregala desde la sección de evidencias", "case_must_exist_to_access_data": "El case debe existir para acceder a estos datos", "isPassEnabled": "Pass habilitado", "isClockEnabled": "Clock habilitado", "enroll_live_samples": "Enrolar muestras en vivo", "enroll_file_samples": "En<PERSON>ar muestras por fichero", "selectImage": "Seleccionar imagen", "index": "<PERSON><PERSON><PERSON>", "sample_type": "Tipo de muestra", "UNKNOWN": "Desconocido", "LEFT_IRIS": "<PERSON>", "RIGHT_IRIS": "<PERSON> derecho", "LEFT_THUMB": "<PERSON><PERSON><PERSON>", "LEFT_INDEX_FINGER": "<PERSON><PERSON><PERSON>", "LEFT_MIDDLE_FINGER": "Medio izquierdo", "LEFT_RING_FINGER": "<PERSON><PERSON>", "LEFT_LITTLE_FINGER": "<PERSON><PERSON><PERSON>", "RIGHT_THUMB": "Pulgar derecho", "RIGHT_INDEX_FINGER": "<PERSON>ndice derecho", "RIGHT_MIDDLE_FINGER": "Medio derecho", "RIGHT_RING_FINGER": "Anular derecho", "RIGHT_LITTLE_FINGER": "<PERSON><PERSON><PERSON> derecho", "UP_LEFT_FACE": "Cara superior izquierda", "UP_FACE": "Cara superior", "UP_RIGHT_FACE": "Cara superior derecha", "RIGHT_FACE": "<PERSON> derecha", "DOWN_RIGHT_FACE": "Cara inferior derecha", "DOWN_FACE": "Cara inferior", "DOWN_LEFT_FACE": "Cara inferior izquierda", "LEFT_FACE": "Cara izquierda", "FRONTAL_FACE": "Cara frontal", "LEFT_UPPER_PALM": "Palma izquierda superior", "RIGHT_UPPER_PALM": "Palma derecha superior", "LEFT_LOWER_PALM": "Palma izquierda inferior", "RIGHT_LOWER_PALM": "Palma derecha inferior", "LEFT_LATERAL_PALM": "Palma izquierda lateral", "RIGHT_LATERAL_PALM": "Palma derecha lateral", "LEFT_INDEX_FINGER_ROLLED": "<PERSON><PERSON><PERSON> i<PERSON><PERSON>o rolado", "LEFT_MIDDLE_FINGER_ROLLED": "Medio izquierdo rolado", "LEFT_RING_FINGER_ROLLED": "<PERSON><PERSON><PERSON> rola<PERSON>", "LEFT_LITTLE_FINGER_ROLLED": "<PERSON><PERSON><PERSON>", "RIGHT_INDEX_FINGER_ROLLED": "Índice derecho rolado", "RIGHT_MIDDLE_FINGER_ROLLED": "Medio derecho rolado", "RIGHT_RING_FINGER_ROLLED": "Anular derecho rolado", "RIGHT_LITTLE_FINGER_ROLLED": "Meñ<PERSON> derecho rolado", "LEFT_THUMB_ROLLED": "<PERSON><PERSON><PERSON> i<PERSON><PERSON><PERSON><PERSON> rolado", "RIGHT_THUMB_ROLLED": "Pulgar derecho rolado", "enroll_by_file_mappings": "Mapeo de enrolamiento por archivo", "show_mappings": "Mostrar mapeo", "items": "Ítems", "finger": "<PERSON><PERSON>", "FINGER": "<PERSON><PERSON>", "new_mapping": "Nuevo mapeo", "add": "Agregar", "add_sample_mapping": "Agregar mapeo de muestra", "select_sample_mapping": "Seleccionar mapeo de muestra", "": ""}, "requiredActionStatus": {"completed_on_time": "Completado", "completed_late": "Completado tarde", "on_time": "Pendiente", "late": "Pendiente no completado"}, "reports": {"export": "Exportar PDF", "export2": "Exportar Excel", "tMain2": "de usuarios", "tMain3": "de sujetos", "tMain4": "de muestras", "tMain5": "Número", "tMain6": "Identificaciones por tecnología", "tMain7": "Verificaciones por tecnología", "tMain8": "Media", "tMain9": "de fotos", "tUsers1": "Lista de usuarios", "tUsers2": "Usuarios por perfil", "tUsers3": "Usuarios por ubicación", "tUsers4": "Usuarios por género", "tUsers5": "Usuarios y fotos", "tUsers6": "Identificaciones por tecnología", "tUsers7": "Verificaciones por tecnología", "tSub1": "Lista de sujetos", "tSub2": "Sujetos por perfil", "tSub3": "Sujetos por ubicación", "tSub4": "Sujetos por género", "tSub5": "Sujetos y fotos", "tSub6": "Identificaciones por tecnología", "tSub7": "Verificaciones por tecnología", "tV1": "Solicitudes de verificación por tecnología", "tV2": "Resultados de verificación por tecnología", "tV3": "Solicitudes de verificación por ubicación", "tV4": "Resultados de verificación por ubicación", "tI1": "Solicitudes 1:N por tecnología", "tI2": "Resultados 1:N por tecnología", "tI3": "Solicitudes 1:N por ubicación", "tI4": "Resultados 1:N por ubicación", "tR1": "Tiempo total por tecnología", "tR2": "Tiempo total por acción", "tR3": "Tiempos de servidor por tecnología", "tR4": "Tiempos de servidor desg<PERSON>ado", "tTo": "a", "tOf": "de", "tLocations": "Ubicaciones", "tActions1": "Lista de acciones", "tActions2": "Verificaciones", "tActions3": "Identificaciones", "tActions4": "Enrolamientos", "tActions5": "Eliminaciones", "tActions6": "Actualizaciones", "tActions7": "Muestras", "tActions8": "Muestras", "tActions9": "Fotos", "tActions10": "<PERSON><PERSON>", "tActions11": "Dispositivos", "tActions12": "verificaciones", "tActions13": "identificaciones", "tActions14": "sujetos enrolados", "tActions15": "sujetos eliminados", "tActions16": "sujetos actualizados", "tActions17": "nuevas muestras", "tActions18": "muestras eliminadas", "tActions19": "nuevas fotos", "tActions20": "logins con password", "tActions21": "dispositivos no detectados", "tActions22": "Tiempo total medio", "tActions23": "Tiempo de usuario medio", "tActions24": "Tiempo de red medio", "tActions25": "Tiempo de servidor medio", "tAudit1": "Acciones auditadas", "noData": "No hay datos disponibles", "emissionDate": "Fecha de emisión del reporte", "customer": "Cliente", "reportType": "Tipo de reporte", "initDate": "Fecha de inicio", "endDate": "<PERSON><PERSON> de fin", "actionType": "Tipo de acción", "reportTitle1": "Emisor y receptor del reporte", "reportTitle2": "Filtros aplicados", "reportMetadata": "Report generated using Verázial ID Reports", "reportColumn1": "Aplicación", "reportColumn2": "Ubicación", "reportColumn3": "Segmento", "reportColumn4": "Dispositivo", "reportColumn5": "Acción", "reportColumn6": "<PERSON><PERSON><PERSON><PERSON>", "reportColumn7": "Perfil", "reportColumn8": "Receptor", "reportColumn9": "Perfil", "reportColumn10": "Valor anterior", "reportColumn11": "Valor nuevo", "reportColumn12": "<PERSON><PERSON>", "reportColumn13": "<PERSON><PERSON><PERSON><PERSON>", "reportColumn14": "<PERSON><PERSON> extra", "reportNameAudit": "Audit trail de acciones", "reportIdentifier": "Identificador del reporte"}, "ms_errors": {"0": "<PERSON><PERSON>r inesperado", "404": "No encontrado", "500": "Error interno del servidor", "522": "E<PERSON>r de conexión", "561": "No autorizado", "1000": "Ha ocurrido un error en la base de datos", "1001": "La tabla no existe", "1002": "Los tenants no pueden ser migrados", "2000": "Este sujeto ya existe en la base de datos", "2001": "El sujeto no existe en el sistema", "2002": "Número de sesiones excedido", "2003": "Us<PERSON>rio blo<PERSON>", "2004": "Token expirado", "2005": "To<PERSON> in<PERSON>lid<PERSON>", "2006": "Token usado", "2007": "Token no disponible", "3000": "Este campo es requerido", "3001": "Existe un registro con la misma información", "3002": "No se puede eliminar", "3003": "Formato de versión inválido. Debe ser v{major}.{minor}.{path}, por ejemplo v1.0.0", "4001": "No hay licencias disponibles", "4002": "La licencia no está habilitada", "5001": "Tenant no encontrado", "5002": "Tenant no habilitado", "6000": "Error inesperado en el servicio externo", "6001": "Método de servicio externo no configurado", "6002": "Método de servicio externo no configurado correctamente"}, "mservices": {"v-mservice-server-monitoring": "Monitorización del servidor", "v-mservice-system-settings": "Manager", "v-mservice-tenant-biom-config": "Configuración biométrica de tenants", "v-mservice-subject": "Sujetos", "v-mservice-auth": "Autenticación", "v-mservice-tenant-db-config": "Configuración BBDD de tenants", "v-mservice-user": "Usuarios", "v-mservice-apigateway-grpc": "API Gateway gRPC", "v-mservice-biom-mngr": "Manager <PERSON><PERSON><PERSON><PERSON><PERSON>", "v-mservice-biom-neurotec13": "Facial y Palma", "v-mservice-cron": "<PERSON><PERSON><PERSON>", "v-mservice-actions": "Acciones de Auditoría", "v-mservice-tenant": "Tenant", "v-mservice-actionsV2": "Acciones de Auditoría V2", "v-mservice-storage": "Storage", "v-mservice-biom-neurotec": "<PERSON> y <PERSON>", "neurotechnology9": "Neurotechnology 9", "v-mservice-mail-dispatcher": "Env<PERSON>", "v-mservice-api-binder": "API Binder", "neurotechnology13": "Neurotechnology 13", "v-mservice-apigateway": "API Gateway", "grafana": "<PERSON><PERSON>", "loki": "<PERSON>", "fluent-bit": "Fluent Bit", "prometheus": "Prometheus", "tempo": "Tempo", "v-mservice-ext-credentials": "Credenciales Externas", "v-mservice-biographic": "<PERSON><PERSON><PERSON>", "v-mservice-discovery": "Discovery", "postgres": "Postgres"}, "status": {"created": "<PERSON><PERSON><PERSON>", "authorized": "Autorizado", "cancelled": "Cancelado", "in_progress": "En progreso", "completed": "Completado", "expired": "<PERSON><PERSON><PERSON>"}, "cron-services": {"roll-call-alerts": "Alertas de pase de lista"}, "prime_ng": {"accept": "Sí", "addRule": "Agregar regla", "am": "AM", "apply": "Aplicar", "cancel": "<PERSON><PERSON><PERSON>", "choose": "Escoger", "chooseDate": "<PERSON><PERSON> fecha", "chooseMonth": "Elige el mes", "chooseYear": "<PERSON><PERSON>", "clear": "Limpiar", "completed": "Terminado", "contains": "Contenga", "custom": "Personalizar", "dateAfter": "Fecha después de", "dateBefore": "<PERSON><PERSON> antes de", "dateFormat": "dd/mm/yy", "dateIs": "Fecha igual a", "dateIsNot": "<PERSON><PERSON> di<PERSON>ente a", "dayNames": ["Domingo", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Viernes", "Sábado"], "dayNamesMin": ["D", "L", "M", "X", "J", "V", "S"], "dayNamesShort": ["Dom", "<PERSON>n", "Mar", "<PERSON><PERSON>", "<PERSON><PERSON>", "Vie", "<PERSON><PERSON><PERSON>"], "emptyFilterMessage": "Sin opciones disponibles", "emptyMessage": "No se han encontrado resultados", "emptySearchMessage": "Sin opciones disponibles", "emptySelectionMessage": "<PERSON><PERSON><PERSON> artí<PERSON>lo <PERSON>", "endsWith": "Termine con", "equals": "Igual a", "fileSizeTypes": ["B", "KB", "MB", "GB", "TB", "PB", "EB", "ZB", "YB"], "filter": "Filtrar", "firstDayOfWeek": 1, "gt": "Mayor que", "gte": "Mayor o igual a", "lt": "<PERSON><PERSON> que", "lte": "<PERSON>or o igual a", "matchAll": "Coincidir todo", "matchAny": "Coincidir con cualquiera", "medium": "Medio", "monthNames": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Abril", "Mayo", "<PERSON><PERSON>", "<PERSON>", "Agosto", "Septiembre", "Octubre", "Noviembre", "Diciembre"], "monthNamesShort": ["Ene", "Feb", "Mar", "Abr", "May", "Jun", "Jul", "Ago", "Sep", "Oct", "Nov", "Dic"], "nextDecade": "Próxima década", "nextHour": "próxima hora", "nextMinute": "Siguiente minuto", "nextMonth": "Próximo mes", "nextSecond": "Siguient<PERSON> segundo", "nextYear": "El próximo año", "noFilter": "Sin filtro", "notContains": "No contenga", "notEquals": "Diferente a", "now": "<PERSON><PERSON>", "passwordPrompt": "Escriba una contraseña", "pending": "Pendiente", "pm": "PM", "prevDecade": "Década anterior", "prevHour": "Hora anterior", "prevMinute": "Minuto anterior", "prevMonth": "Mes anterior", "prevSecond": "<PERSON><PERSON><PERSON>", "prevYear": "<PERSON><PERSON> anterior", "reject": "No", "removeRule": "Eliminar regla", "searchMessage": "{0} resultados están disponibles", "selectionMessage": "{0} elementos seleccionados", "showMonthAfterYear": false, "startsWith": "Comience con", "strong": "<PERSON><PERSON>e", "today": "Hoy", "upload": "Subir", "weak": "<PERSON><PERSON><PERSON>", "weekHeader": "Se<PERSON>", "aria": {"cancelEdit": "<PERSON><PERSON><PERSON> editado", "close": "<PERSON><PERSON><PERSON>", "collapseLabel": "Colapsar", "collapseRow": "<PERSON><PERSON><PERSON>", "editRow": "<PERSON><PERSON> fila", "expandLabel": "Expandir", "expandRow": "Expandir <PERSON>", "falseLabel": "<PERSON><PERSON><PERSON>", "filterConstraint": "Restricción de filtro", "filterOperator": "Operador de filtro", "firstPageLabel": "Primera Página", "gridView": "Vista de cuadrícula", "hideFilterMenu": "Ocultar menú del filtro", "jumpToPageDropdownLabel": "Ir al menú desplegable de página", "jumpToPageInputLabel": "Ir a la entrada de página", "lastPageLabel": "Última Página", "listView": "Vista de lista", "moveAllToSource": "Mover todo al origen", "moveAllToTarget": "Mover todo al objetivo", "moveBottom": "Desp<PERSON><PERSON><PERSON> hacia abajo", "moveDown": "Bajar", "moveTop": "Mover a<PERSON>ba", "moveToSource": "Mover al origen", "moveToTarget": "Mover al objetivo", "moveUp": "Subir", "navigation": "Navegación", "next": "Siguient<PERSON>", "nextPageLabel": "Siguiente Página", "nullLabel": "No seleccionado", "otpLabel": "Introduzca el carácter de contraseña de un solo uso {0}", "pageLabel": "<PERSON><PERSON><PERSON><PERSON> {page}", "passwordHide": "Contraseña oculta", "passwordShow": "Mostrar contraseña", "previous": "Anterior", "previousPageLabel": "Página Anterior", "removeLabel": "Eliminar", "rotateLeft": "Girar a la izquierda", "rotateRight": "Girar a la derecha", "rowsPerPageLabel": "<PERSON>las por página", "saveEdit": "Guardar editado", "scrollTop": "Desp<PERSON>zar<PERSON> hacia arriba", "selectAll": "<PERSON><PERSON><PERSON><PERSON><PERSON> to<PERSON>", "selectLabel": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "selectRow": "Seleccionar fila", "showFilterMenu": "Mostrar menú del filtro", "slide": "<PERSON><PERSON><PERSON>", "slideNumber": "{slideNumber}", "star": "1 estrella", "stars": "{star} estrellas", "trueLabel": "Verdadero", "unselectAll": "Deselecciona<PERSON> todos", "unselectLabel": "Deseleccionar", "unselectRow": "<PERSON><PERSON><PERSON> fila", "zoomImage": "Ampliar imagen", "zoomIn": "Ampliar", "zoomOut": "Reducir"}}}