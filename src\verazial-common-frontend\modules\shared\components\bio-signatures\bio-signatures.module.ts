import {CommonModule} from "@angular/common";
import {NgModule} from "@angular/core";
import {FormsModule, ReactiveFormsModule} from "@angular/forms";
import {TranslateModule} from "@ngx-translate/core";
import {ButtonModule} from "primeng/button";
import {ConfirmDialogModule} from "primeng/confirmdialog";
import {DialogModule} from "primeng/dialog";
import {DropdownModule} from "primeng/dropdown";
import {FloatLabelModule} from "primeng/floatlabel";
import {IconFieldModule} from "primeng/iconfield";
import {InputIconModule} from "primeng/inputicon";
import {InputTextModule} from "primeng/inputtext";
import {ProgressSpinnerModule} from "primeng/progressspinner";
import {ToastModule} from "primeng/toast";
import {LoadingSpinnerModule} from "../loading-spinner/loading-spinner.module";
import {BioTechButtonsModule} from "../bio-tech-buttons/bio-tech-buttons.module";
import {WidgetSearchModule} from "../widget-search/widget-search.module";
import {BioSignaturesComponent} from "./bio-signatures/bio-signatures.component";
import {SkeletonModule} from 'primeng/skeleton';
import {TooltipModule} from "primeng/tooltip";
import {
    WidgetMatchModule
} from "src/verazial-common-frontend/modules/shared/components/widget-match/widget-match.module";

@NgModule({
    declarations: [
        BioSignaturesComponent,
    ],
    imports: [
        /* Angular Modules */
        CommonModule,
        /* Forms */
        ReactiveFormsModule,
        FormsModule,
        /* Translate */
        TranslateModule,
        /* PrimeNG Modules */
        ProgressSpinnerModule,
        DialogModule,
        ButtonModule,
        IconFieldModule,
        InputIconModule,
        InputTextModule,
        ConfirmDialogModule,
        DropdownModule,
        FloatLabelModule,
        ToastModule,
        SkeletonModule,
        TooltipModule,
        /* Custom Modules */
        LoadingSpinnerModule,
        BioTechButtonsModule,
        WidgetSearchModule,
        WidgetMatchModule,
    ],
    exports: [
        BioSignaturesComponent,
    ]
  })
  export class BioSignaturesModule { }