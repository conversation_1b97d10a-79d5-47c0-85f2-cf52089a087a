import {UseCaseGrpc} from "src/verazial-common-frontend/core/use-case-grpc";
import {
    CaseRepository
} from "src/verazial-common-frontend/core/general/criminalistic-cases/domain/repository/case.repository";
import {
    CaseEvidenceEntity
} from "src/verazial-common-frontend/core/general/criminalistic-cases/domain/entity/caseEvidence.entity";


export class UpdateCaseEvidenceUseCase implements UseCaseGrpc<CaseEvidenceEntity, CaseEvidenceEntity> {
    constructor(private caseRepository: CaseRepository) {
    }

    execute(params: CaseEvidenceEntity): Promise<CaseEvidenceEntity> {
        return this.caseRepository.updateEvidenceCase(params);
    }
}