# Verazial System Admin Frontend - Deployment Package

This deployment package provides a complete Docker-based deployment solution for the Verazial System Admin Frontend with configurable environment variables.

## Package Contents

- **Dockerfile**: Multi-stage Docker build with runtime environment variable support
- **docker-compose.yml**: Docker Compose configuration with environment variable mapping
- **.env**: Configuration file with all modifiable deployment variables
- **entrypoint.sh**: Runtime script that replaces environment variables in the built application
- **deploy.sh**: Automated deployment script
- **README.md**: This documentation file

## Quick Start

1. **Configure your environment**:
   ```bash
   # Edit the .env file with your specific configuration
   nano .env
   ```

2. **Deploy the application**:
   ```bash
   # Make the deployment script executable
   chmod +x deploy.sh
   
   # Run the deployment
   ./deploy.sh
   ```

3. **Access the application**:
   - Open your browser and navigate to `http://localhost:80` (or the port specified in HOST_PORT)

## Configuration

### Environment Variables

Edit the `.env` file to configure the following variables:

#### Host Configuration
- `HOST_PORT`: Port on the host machine (default: 80)

#### gRPC Microservices
- `GRPC_API_GATEWAY`: Main gateway URL for microservices
- `GRPC_TOKEN`: Authentication token for gRPC services
- `STATIC_API_GATEWAY_URL`: Static gateway URL flag (true/false)

#### Konektor API
- `KONEKTOR_API`: Konektor service endpoint

#### Credentials API
- `CREDENTIALS_API`: Verazial credentials service endpoint
- `CREDENTIAL_USER`: Username for credentials API
- `CREDENTIAL_PASSWORD`: Password for credentials API

### Environment-Specific Configurations

#### Development Environment
```bash
GRPC_API_GATEWAY=https://dev-gatewaygrpc.verazial.com
CREDENTIALS_API=https://dev-pass.verazial.com/api/v1/
```

#### Demo Environment
```bash
GRPC_API_GATEWAY=https://demo-gatewaygrpc.verazial.com
CREDENTIALS_API=https://demo-pass.verazial.com:10029/api/v1/
```

#### Production Environment
```bash
GRPC_API_GATEWAY=https://prod-gatewaygrpc.verazial.com
CREDENTIALS_API=https://prod-pass.verazial.com/api/v1/
```

## Manual Deployment

If you prefer to deploy manually:

1. **Build the Docker image**:
   ```bash
   docker-compose build
   ```

2. **Start the application**:
   ```bash
   docker-compose up -d
   ```

3. **Check the status**:
   ```bash
   docker-compose ps
   ```

## Management Commands

- **View logs**: `docker-compose logs -f`
- **Stop application**: `docker-compose down`
- **Restart application**: `docker-compose restart`
- **Update configuration**: Edit `.env` file and run `docker-compose up -d`

## Troubleshooting

### Common Issues

1. **Port already in use**:
   - Change `HOST_PORT` in `.env` file to an available port

2. **Network not found**:
   - Create the external network: `docker network create verazial-network`
   - Or modify the network configuration in `docker-compose.yml`

3. **Environment variables not applied**:
   - Ensure the `.env` file is in the same directory as `docker-compose.yml`
   - Rebuild the image: `docker-compose build --no-cache`

### Logs and Debugging

- **View application logs**: `docker-compose logs verazial-admin`
- **Access container shell**: `docker-compose exec verazial-admin sh`
- **Check environment variables**: `docker-compose exec verazial-admin env`

## Security Considerations

1. **Protect sensitive data**: Ensure `.env` file permissions are restricted
2. **Use secrets**: For production, consider using Docker secrets or external secret management
3. **Network security**: Configure appropriate firewall rules for the exposed port
4. **HTTPS**: Consider using a reverse proxy (nginx, traefik) for SSL termination

## Building for Different Architectures

To build for different architectures (e.g., ARM64 for Apple Silicon):

```bash
docker buildx build --platform linux/amd64,linux/arm64 -t verazial/admin:v2.0.16 .
```

## Support

For issues or questions regarding this deployment package, please refer to the main project documentation or contact the development team.
