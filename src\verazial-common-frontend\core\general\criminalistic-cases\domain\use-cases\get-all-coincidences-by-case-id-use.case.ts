import {UseCaseGrpc} from "src/verazial-common-frontend/core/use-case-grpc";
import {
    CaseRepository
} from "src/verazial-common-frontend/core/general/criminalistic-cases/domain/repository/case.repository";
import {
    GetCoincidenceByCaseIdRequestEntity
} from "src/verazial-common-frontend/core/general/criminalistic-cases/domain/entity/get-coincidence-by-case-id-request.entity";
import {
    CaseCoincidenceEntity
} from "src/verazial-common-frontend/core/general/criminalistic-cases/domain/entity/caseCoincidence.entity";

export class GetAllCoincidencesByCaseIdUseCase implements UseCaseGrpc<GetCoincidenceByCaseIdRequestEntity, CaseCoincidenceEntity[]> {
    constructor(private caseRepository: CaseRepository) {
    }

    execute(params: GetCoincidenceByCaseIdRequestEntity): Promise<CaseCoincidenceEntity[]> {
        return this.caseRepository.getAllCaseCoincidenceByCaseId(params);
    }
}