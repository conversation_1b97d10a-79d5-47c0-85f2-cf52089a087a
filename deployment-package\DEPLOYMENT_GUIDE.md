# Verazial System Admin Frontend - Complete Deployment Guide

This guide provides step-by-step instructions for deploying the Verazial System Admin Frontend on a Linux server with configurable environment variables.

## 📦 Package Overview

Your deployment package contains:

1. **verazial-admin-v2.0.16.tar** - Docker image file
2. **docker-compose.yml** - Container orchestration configuration
3. **.env** - Environment variables configuration file
4. **README.md** - Detailed documentation
5. **DEPLOYMENT_GUIDE.md** - This deployment guide

## 🚀 Quick Deployment (3 Steps)

### Step 1: Prepare Your Linux Server

```bash
# Install Docker (if not already installed)
curl -fsSL https://get.docker.com -o get-docker.sh
sudo sh get-docker.sh

# Install Docker Compose (if not already installed)
sudo curl -L "https://github.com/docker/compose/releases/download/v2.20.0/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
sudo chmod +x /usr/local/bin/docker-compose

# Start Docker service
sudo systemctl start docker
sudo systemctl enable docker
```

### Step 2: Load the Docker Image

```bash
# Transfer the deployment package to your server
# Then load the Docker image
docker load -i verazial-admin-v2.0.16.tar

# Verify the image is loaded
docker images | grep verazial/admin
```

### Step 3: Configure and Deploy

```bash
# Edit the environment configuration
nano .env

# Deploy the application
docker-compose up -d

# Check if it's running
docker-compose ps
```

## ⚙️ Configuration Details

### Environment Variables in .env file

```bash
# Host Configuration
HOST_PORT=80                    # Port where the app will be accessible

# gRPC Microservices Configuration
GRPC_API_GATEWAY=https://dev-gatewaygrpc.verazial.com
GRPC_TOKEN=                     # Leave empty if not needed
STATIC_API_GATEWAY_URL=true

# Konektor API Configuration
KONEKTOR_API=https://localhost:8443

# Credentials API Configuration
CREDENTIALS_API=https://dev-pass.verazial.com/api/v1/
CREDENTIAL_USER=verazial
CREDENTIAL_PASSWORD=@4S9w&JMhg27
```

### Environment-Specific Examples

#### For Development Environment:
```bash
GRPC_API_GATEWAY=https://dev-gatewaygrpc.verazial.com
CREDENTIALS_API=https://dev-pass.verazial.com/api/v1/
HOST_PORT=80
```

#### For Demo Environment:
```bash
GRPC_API_GATEWAY=https://demo-gatewaygrpc.verazial.com
CREDENTIALS_API=https://demo-pass.verazial.com:10029/api/v1/
HOST_PORT=9093
```

#### For Production Environment:
```bash
GRPC_API_GATEWAY=https://prod-gatewaygrpc.verazial.com
CREDENTIALS_API=https://prod-pass.verazial.com/api/v1/
HOST_PORT=80
```

## 🔧 Management Commands

### Basic Operations
```bash
# Start the application
docker-compose up -d

# Stop the application
docker-compose down

# Restart the application
docker-compose restart

# View logs
docker-compose logs -f

# Check status
docker-compose ps
```

### Configuration Updates
```bash
# After modifying .env file
docker-compose down
docker-compose up -d
```

### Troubleshooting
```bash
# View detailed logs
docker-compose logs verazial-admin

# Access container shell
docker-compose exec verazial-admin sh

# Check environment variables inside container
docker-compose exec verazial-admin env | grep -E "(GRPC|KONEKTOR|CREDENTIALS)"
```

## 🌐 Network Configuration

### Default Network Setup
The application uses an external Docker network called `verazial-network`. 

#### Create the network (if it doesn't exist):
```bash
docker network create verazial-network
```

#### Alternative: Use default bridge network
If you don't need a custom network, modify `docker-compose.yml`:
```yaml
# Remove or comment out the networks section
# networks:
#   verazial-network:
#     external: true
```

## 🔒 Security Considerations

### File Permissions
```bash
# Secure the .env file
chmod 600 .env
chown root:root .env
```

### Firewall Configuration
```bash
# Allow access to the application port (example for port 80)
sudo ufw allow 80/tcp

# For custom ports, replace 80 with your HOST_PORT value
sudo ufw allow [YOUR_HOST_PORT]/tcp
```

### HTTPS Setup (Recommended for Production)
Consider using a reverse proxy like nginx or traefik for SSL termination:

```bash
# Example nginx configuration
server {
    listen 443 ssl;
    server_name your-domain.com;
    
    ssl_certificate /path/to/certificate.crt;
    ssl_certificate_key /path/to/private.key;
    
    location / {
        proxy_pass http://localhost:80;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
}
```

## 📊 Monitoring and Maintenance

### Health Checks
```bash
# Check if the application is responding
curl -I http://localhost:80

# Check Docker container health
docker-compose ps
docker stats $(docker-compose ps -q)
```

### Log Management
```bash
# Rotate logs to prevent disk space issues
docker-compose logs --tail=1000 > app.log
docker-compose restart
```

### Backup and Updates
```bash
# Backup current configuration
cp .env .env.backup

# Update to new version
docker load -i verazial-admin-v2.0.17.tar  # New version
docker-compose down
docker-compose up -d
```

## 🆘 Troubleshooting Guide

### Common Issues and Solutions

#### 1. Port Already in Use
```bash
# Error: Port 80 is already in use
# Solution: Change HOST_PORT in .env file
echo "HOST_PORT=8080" >> .env
docker-compose up -d
```

#### 2. Network Not Found
```bash
# Error: network verazial-network not found
# Solution: Create the network
docker network create verazial-network
```

#### 3. Environment Variables Not Applied
```bash
# Solution: Rebuild and restart
docker-compose down
docker-compose build --no-cache
docker-compose up -d
```

#### 4. Application Not Accessible
```bash
# Check if container is running
docker-compose ps

# Check logs for errors
docker-compose logs verazial-admin

# Check if port is accessible
netstat -tlnp | grep :80
```

### Getting Help

1. **Check logs**: Always start with `docker-compose logs -f`
2. **Verify configuration**: Ensure all URLs in `.env` are correct and accessible
3. **Network connectivity**: Test if your server can reach the configured APIs
4. **Resource usage**: Check if the server has sufficient CPU and memory

## 📞 Support

For additional support or questions:
- Check the main README.md file for detailed documentation
- Review the application logs for specific error messages
- Ensure all external services (gRPC gateway, credentials API) are accessible from your server
