<p-toast />
<p-dialog appendTo="body" [(visible)]="showVerifyDialog" [modal]="true" [style]="{ background: '#FFFFFF', 'border-radius': '6px' }" [draggable]="false" [resizable]="false">
    <ng-template pTemplate="headless">
        <div class="closeWidgetButtonDiv">
            <button pButton pRipple icon="pi pi-times" class="buttonCloseBk closeWidgetButton" (click)="closeVerifyDialog()"></button>
        </div>
        <div *ngIf="!waitSelectTech else widgetVerifySplashScreen" [id]="widgetElementId + 'Div'" [class]="widgetElementId + 'Div'">
            <ng-container *ngTemplateOutlet="widget"></ng-container>
        </div>
        <ng-template #widgetVerifySplashScreen>
            <div id="selectTechnology" [class]="widgetElementId + 'Div'">
                <div [class]="widgetElementId + ' selectTechnology'">
                    <div *ngIf="waitSelectTech else loadingWidget" [class]="widgetElementId + ' selectTechnology'">
                        <label *ngIf="!isLoggedUserVerification" class="selectTechnologyLabel my-5">{{ 'messages.selectTechnology' | translate }}</label>
                        <img class="subjectProfilePicture mb-2" [src]="subject?.pic != '' && subject?.pic != undefined && subject?.pic != null ? 'data:image/jpeg;base64,' + subject?.pic?.replace('data:image/jpeg;base64,', '') : imagePlaceholder"/>
                        <label class="fullNameLabel">{{ subjectFullName }}</label>
                        <label class="subjectNumIdLabel">{{ numId }}</label>
                        <label *ngIf="isLoggedUserVerification" class="selectTechnologyLabel mt-6">{{ 'messages.selectTechnologyUserVerification' | translate }}</label>
                    </div>
                    <ng-template #loadingWidget>
                        <p-progressSpinner styleClass="w-5rem h-5rem" strokeWidth="8" fill="var(--surface-ground)" animationDuration=".5s" ariaLabel="loading" />
                    </ng-template>
                </div>
                <div class="flex flex-column align-items-center mb-2" [style]="{'width':'100%'}" pTooltip="{{ tenantMessage | translate}}" tooltipPosition="top">
                    <ngx-verazial-ui-verify
                        [verifyActive]="false"
                        [fingerprintActive]="fingerprintActive && isTenantValid()"
                        [facialActive]="facialActive && isTenantValid()"
                        [irisActive]="irisActive && isTenantValid()"
                        class="" name="{{ 'loginForm.loginOptions' | translate }}"
                        (technology)="verify($event)"
                        srcFingerPrint="verazial-common-frontend/assets/images/bio-tech-icons/md/FingerprintMain.svg"
                        srcFacial="verazial-common-frontend/assets/images/bio-tech-icons/md/FacialMain.svg"
                        srcIris="verazial-common-frontend/assets/images/bio-tech-icons/md/IrisMain.svg"
                        srcFingerPrintWhite="verazial-common-frontend/assets/images/bio-tech-icons/md/FingerprintWhite.svg"
                        srcFacialWhite="verazial-common-frontend/assets/images/bio-tech-icons/md/FacialWhite.svg"
                        srcIrisWhite="verazial-common-frontend/assets/images/bio-tech-icons/md/IrisWhite.svg"
                        srcFingerPrintDisabled="verazial-common-frontend/assets/images/bio-tech-icons/md/FingerPrintDisabled.svg"
                        srcFacialDisabled="verazial-common-frontend/assets/images/bio-tech-icons/md/FacialDisabled.svg"
                        srcIrisDisabled="verazial-common-frontend/assets/images/bio-tech-icons/md/IrisDisabled.svg">
                    </ngx-verazial-ui-verify>
                </div>
            </div>
        </ng-template>
    </ng-template>
</p-dialog>

<ng-template #widget>
    <div *ngIf="showWidget && ready else widgetSkelly">
        <iframe
            [id]="widgetElementId"
            [class]="widgetElementId"
            [src]="this.pageWidgetURL"
            frameBorder="0"
            allow="camera">
        </iframe>
    </div>
</ng-template>

<ng-template #widgetSkelly>
    <div [class]="widgetElementId">
        <p-skeleton width="100%" height="100%"></p-skeleton>
    </div>
</ng-template>