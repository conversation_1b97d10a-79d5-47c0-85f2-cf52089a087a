import {Component, EventEmitter, Input, OnChanges, OnInit, Output} from "@angular/core";
import {FormControl, FormGroup, Validators} from "@angular/forms";
import {TranslateService} from "@ngx-translate/core";
import {MessageService} from "primeng/api";
import {
    KonektorPropertiesEntity
} from "src/verazial-common-frontend/core/general/konektor/domain/entity/konektor-properties.entity";
import {GeneralSettings} from "src/verazial-common-frontend/core/general/manager/common/models/general-settings.model";
import {SubjectEntity} from "src/verazial-common-frontend/core/general/subject/domain/entity/subject.entity";
import {GenericKeyValue} from "src/verazial-common-frontend/core/models/key-value.interface";
import {WidgetResult} from "../../../models/widget-response.model";
import {AuditTrailFields} from "src/verazial-common-frontend/core/models/audit-trail-fields.enum";
import {AuditTrailService} from "src/verazial-common-frontend/core/services/audit-trail.service";
import {AuditTrailActions} from "src/verazial-common-frontend/core/models/audit-trail-actions.enum";
import {
    GetSubjectByNumIdUseCase
} from "src/verazial-common-frontend/core/general/subject/domain/use-cases/get-subject-by-num-id.use-case";
import {ConsoleLoggerService} from "src/verazial-common-frontend/core/services/console-logger.service";
import {LocalStorageService} from "src/verazial-common-frontend/core/services/local-storage.service";
import {
    LanguageRecordModel,
    TranslationGroup,
    TranslationModel
} from "src/verazial-common-frontend/core/general/manager/common/models/translation.model";

export class BioSignatureResult {
    id?: string;
    numId?: string;
    tech?: string;
    date?: Date;
    reason?: string;
    observation?: string;
}

@Component({
    selector: 'app-bio-signatures',
    templateUrl: './bio-signatures.component.html',
    styleUrl: './bio-signatures.component.css'
})
export class BioSignaturesComponent implements OnInit, OnChanges {

    // Inputs
    @Input() showSignatureButton: boolean = false; // Flag to show the biometric signature button and form
    @Input() showExtraFormFields: boolean = true; // Flag to show extra form controls (Reason and Observations)
    @Input() reasonOptionsParameter = 'generic-reasons'; // Parameter to get the reasons for the Reason dropdown
    @Input() showSignatureInfo: boolean = false; // Flag to show the signature info
    @Input() showFixedSign: boolean = false; // Flag to show the fixed user/subject to sign
    @Input() signatureTitle: string = 'content.signature'; // Title for the signature info
    @Input() signatureInputLabel: string = 'content.signature'; // Label for the signature input
    @Input() reasonsDropdownLabel: string = 'content.reason'; // Label for the reasons dropdown
    @Input() observationInputLabel: string = 'content.observation'; // Label for the observation input
    @Input() konektorProperties?: KonektorPropertiesEntity; // Konektor Properties
    @Input() managerSettings?: GeneralSettings; // Manager Settings
    @Input() signatureData?: BioSignatureResult; // Signature Data if already exists
    @Input() restrictSubjectRoles: string[] = []; // Restrict the roles that can sign
    @Input() subjectRoleSegmentedSearch: string = ''; // Subject Role to use for segmented search
    @Input() userIsVerified: boolean = false; // Flag to check if the user is verified
    @Input() userNumId: string = ''; // User NumId
    @Input() userName: string = ''; // User Name
    @Input() userSubject: SubjectEntity | undefined;
    // Outputs
    @Output() outputResult = new EventEmitter<BioSignatureResult>(); // Output the signature result

    // Result
    signatureResult?: BioSignatureResult;

    // Subject Data
    subjectImage: string = '';
    subjectData?: SubjectEntity;
    imagePlaceholder: string = "verazial-common-frontend/assets/images/all/UserPic.svg";

    // Extra Form
    selectReason?: GenericKeyValue;
    reasonOptions: GenericKeyValue[] = [];
    observation: string = '';

    // Widget Functions
    widgetUrl: string = '';
    segmentedSearchAttributes: { name: string, value: string, secondSearch?: string }[] = [];
    verifyReady: boolean = false;
    searchReady: boolean = false;
    subjectNumId: string = '';
    tech: string = '';
    // Enter NumId (1:1 Verification)
    showEnterNumId: boolean = false;
    formNumId: FormGroup = new FormGroup({
        numId: new FormControl('', Validators.required)
    });

    isLoading: boolean = false;

    isDisabled: boolean = false;

    constructor(
        private translateService: TranslateService,
        private messageService: MessageService,
        private auditTrailService: AuditTrailService,
        private loggerService: ConsoleLoggerService,
        private getSubjectByNumIdUseCase: GetSubjectByNumIdUseCase,
        private localStorageService: LocalStorageService,
    ) { }

    ngOnInit(): void {
        // this.loggerService.debug(this.signatureData);
        if(this.userNumId)
        {
            this.subjectNumId = this.userNumId;
            this.isDisabled = true;
        }
        if (this.signatureData && this.signatureData.numId) {
            this.getSignatureSubjectByNumId(this.signatureData.numId);
        }
        let options = this.managerSettings?.catalogs?.find((catalog) => catalog.parameter === this.reasonOptionsParameter)?.options;
            this.reasonOptions = options ? (JSON.parse(options) as string[]).map((option) => { return { key: option, value: this.getLabel(option, this.reasonOptionsParameter) } }) : [{ key: this.translateService.instant('content.other'), value: this.translateService.instant('content.other') }];
    }

    ngOnChanges(): void {
        if (this.subjectData) {
            this.subjectImage = (this.subjectData.pic == this.imagePlaceholder || this.subjectData.pic == "" || this.subjectData.pic == undefined || this.subjectData.pic == null) ? this.imagePlaceholder : this.subjectData.pic.includes('data:image/jpeg;base64,') ? this.subjectData?.pic! : 'data:image/jpeg;base64,' + this.subjectData?.pic!;
        }
    }

    getSignatureSubjectByNumId(numId: string) {
        this.isLoading = true;
        if (numId) {
            this.getSubjectByNumIdUseCase.execute({ numId: numId }).then(
                (data) => {
                    this.subjectData = data;
                    if (this.restrictSubjectRoles.length > 0 && (!this.signatureData && !this.signatureResult?.id)) {
                        let hasRole = false;
                        for (let role of this.restrictSubjectRoles) {
                            for (let subjectRole of data?.roles!) {
                                if (role == subjectRole.id?.toString()) {
                                    hasRole = true;
                                    break;
                                }
                            }
                        }
                        if (!hasRole) {
                            this.messageService.add({
                                severity: 'error',
                                summary: this.translateService.instant('titles.access_denied'),
                                detail: this.translateService.instant('messages.error_subject_not_allowed_to_sign'),
                                life: (this.managerSettings?.timeoutNotification ?? 5) * 1000
                            });
                            this.isLoading = false;
                            this.signatureResult = undefined;
                            return;
                        }
                    }
                    if (!this.signatureData && this.signatureResult) {
                        this.signatureResult!.id = data.id!;
                        this.loggerService.debug("signatureResult");
                        this.loggerService.debug(this.signatureResult);
                        this.outputResult.emit(this.signatureResult);
                    }
                },
                (e) => {
                    this.loggerService.error(e);
                }
            )
            .finally(() => {
                this.ngOnChanges();
                this.isLoading = false;
            });
        }
        else {
            this.signatureResult = undefined;
            this.isLoading = false;
        }
    }

    // Widget

    widgetSearch(tech: string) {
        if (this.showExtraFormFields) {
            if (!this.selectReason) {
                this.messageService.add({
                    severity: 'error',
                    summary: this.translateService.instant('titles.important'),
                    detail: this.translateService.instant('messages.error_must_select_cancel_reason'),
                    life: (this.managerSettings?.timeoutNotification ?? 5) * 1000
                })
                return;
            }
        }
        if (this.subjectRoleSegmentedSearch != '') {
            this.segmentedSearchAttributes.push({ name: 'PROFILE', value: this.subjectRoleSegmentedSearch, secondSearch: 'false' });
        }
        this.widgetUrl = this.managerSettings?.widgetConfig?.url || "";
        this.tech = tech;
        if (this.konektorProperties?.verificationEnabled) {
            if (this.subjectNumId) {
                this.startSearch();
            } else if (this.konektorProperties?.verificationSubjectId) {
                this.subjectNumId = this.konektorProperties.verificationSubjectId;
                this.startSearch();
            }
            else {
                this.showEnterNumId = true;
                return;
            }
        } else {
            this.startSearch();
        }
    }

    widgetVerify() {
        if (this.showExtraFormFields) {
            if (!this.selectReason) {
                this.messageService.add({
                    severity: 'error',
                    summary: this.translateService.instant('titles.important'),
                    detail: this.translateService.instant('messages.error_must_select_cancel_reason'),
                    life: (this.managerSettings?.timeoutNotification ?? 5) * 1000
                })
                return;
            }
        }
        if (this.subjectRoleSegmentedSearch != '') {
            this.segmentedSearchAttributes.push({
                name: 'PROFILE',
                value: this.subjectRoleSegmentedSearch,
                secondSearch: 'false'
            });
        }
        this.widgetUrl = this.managerSettings?.widgetConfig?.url || "";
        this.isLoading = false;
        this.updateModified(true);
        this.subjectNumId = this.userSubject?.numId!;
        this.verifyReady = true;
    }

    startSearch() {
        let allowWidget = false;
        switch (this.tech) {
            case 'fingerprint':
                allowWidget = this.managerSettings?.payedTechnology?.dactilar == true && this.konektorProperties?.enabledTech?.dactilar == true && (this.subjectNumId == '' ? this.managerSettings?.allowSearch?.dactilar == true : this.managerSettings?.allowVerify?.dactilar == true);
                break;
            case 'facial':
                allowWidget = this.managerSettings?.payedTechnology?.facial == true && this.konektorProperties?.enabledTech?.facial == true && (this.subjectNumId == '' ? this.managerSettings?.allowSearch?.facial == true : this.managerSettings?.allowVerify?.facial == true);
                break;
            case 'iris':
                allowWidget = this.managerSettings?.payedTechnology?.iris == true && this.konektorProperties?.enabledTech?.iris == true && (this.subjectNumId == '' ? this.managerSettings?.allowSearch?.iris == true : this.managerSettings?.allowVerify?.iris == true);
                break;
        }
        if (allowWidget) {
            this.searchReady = true;
        } else {
            this.messageService.add({
                severity: 'error',
                summary: this.translateService.instant('titles.access_denied'),
                detail: this.translateService.instant('messages.error_technology_not_allowed'),
                life: (this.managerSettings?.timeoutNotification ?? 5) * 1000
            });
        }
    }

    onWidgetSearchResult(event: WidgetResult) {
        if (!this.searchReady) {
            return;
        }
        this.loggerService.debug(event);
        switch (event.action) {
            case "verify":
                this.searchReady = false;
                if (event.result == "success") {
                    if (event.data.isMatched) {
                        this.signatureResult = {
                            id: undefined,
                            numId: this.subjectNumId,
                            tech: event.data.tech,
                            date: new Date(),
                            reason: this.showExtraFormFields ? this.selectReason?.value.toString() ?? undefined : undefined,
                            observation: this.showExtraFormFields ? this.observation ?? undefined : undefined,
                        }
                        this.getSignatureSubjectByNumId(this.subjectNumId);
                        const at_attributes = [
                            { name: AuditTrailFields.REGISTRATION_CODE, value: 'VER_BIO' },
                        ]
                        this.auditTrailService.registerAuditTrailAction(this.subjectNumId, AuditTrailActions.SUBJECT_VERIFY, 0, 'SUCCESS', event.data.tech, at_attributes);
                    }
                }
                break;
            case 'search':
                const responseData = event.data.nId;
                if (responseData) {
                    this.signatureResult = {
                        id: undefined,
                        numId: responseData,
                        tech: this.tech,
                        date: new Date(),
                        reason: this.showExtraFormFields ? this.selectReason?.value.toString() ?? undefined : undefined,
                        observation: this.showExtraFormFields ? this.observation ?? undefined : undefined,
                    }
                    this.getSignatureSubjectByNumId(responseData);
                    const at_attributes = [
                        { name: AuditTrailFields.REGISTRATION_CODE, value: 'IDN_BIO' },
                    ]
                    this.auditTrailService.registerAuditTrailAction(this.subjectNumId, AuditTrailActions.SUBJECT_SEARCH, 0, 'SUCCESS', this.tech, at_attributes);
                }
                this.searchReady = false;
                break;
            case "process":
                break;
            case "close_search":
            case "error":
                this.updateModified(false);
                this.searchReady = false;
                if(!this.isDisabled)
                    this.subjectNumId = '';
                this.tech = '';
                break;
        }
    }

    onWidgetMatchResult(event: WidgetResult) {
        if (!this.verifyReady) {
            return;
        }
        switch (event.action) {
            case "verify":
                if (event.result == "success") {
                    if (event.data.isMatched) {
                        this.signatureResult = {
                            id: undefined,
                            numId: this.userNumId,
                            tech: event.data.tech,
                            date: new Date(),
                            reason: this.showExtraFormFields ? this.selectReason?.value.toString() ?? undefined : undefined,
                            observation: this.showExtraFormFields ? this.observation ?? undefined : undefined,
                        }
                        this.getSignatureSubjectByNumId(this.userNumId);
                        const at_attributes = [
                            { name: AuditTrailFields.REGISTRATION_CODE, value: 'VER_BIO' },
                        ]
                        this.auditTrailService.registerAuditTrailAction(this.subjectNumId, AuditTrailActions.SUBJECT_VERIFY, 0, 'SUCCESS', event.data.tech, at_attributes);
                    }
                }
                this.subjectNumId = '';
                this.verifyReady = false;
                this.updateModified(false)
                break;
            case "process":
                this.updateModified(event.result.toLowerCase().includes("started"))
                break;
            case "close_verify":
            case "error":
                this.updateModified(false);
                this.subjectNumId = '';
                this.verifyReady = false;
                break;
        }
    }

    closeNumIdDialog() {
        this.formNumId.reset();
        this.showEnterNumId = false;
    }

    onNumIdDialogSubmit() {
        this.formNumId.markAllAsTouched();
        const numId = this.formNumId.get('numId')?.value;
        if (numId && numId != '') {
            this.formNumId.reset();
            this.showEnterNumId = false;
            this.subjectNumId = numId;
            this.startSearch();
        }
        else {
            this.messageService.add({
                severity: 'error',
                summary: this.translateService.instant("titles.important"),
                detail: this.translateService.instant("messages.ERROR_INVALID_NUMID"),
                life: (this.managerSettings?.timeoutNotification ?? 5) * 1000
            })
        }
    }

    getTechIcon(tech: string): string {
        let fingerprintIcon: string = "verazial-common-frontend/assets/images/bio-tech-icons/sm/fingerprint.svg";
        let facialIcon: string = "verazial-common-frontend/assets/images/bio-tech-icons/sm/facial.svg";
        let irisIcon: string = "verazial-common-frontend/assets/images/bio-tech-icons/sm/iris.svg";
        switch (tech) {
            case 'fingerprint':
                return fingerprintIcon;
            case 'facial':
                return facialIcon;
            case 'iris':
                return irisIcon;
            default:
                return '';
        }
    }

    updateModified(modified: boolean) {
        this.localStorageService.setLockMenu(modified);
    }

    getLabel(key: string, catalogParameter: string): string {
        let reasonTranslations = this.managerSettings?.continued1?.translations?.find((t: TranslationGroup) => t.id === 'catalogs-' + catalogParameter)?.translations || [];
        let translations: LanguageRecordModel[] = reasonTranslations.find((t: TranslationModel) => t.key === key)?.translations || [];
        let translation = translations.find(t => t.languageCode == this.translateService.currentLang);
        if (translation && translation.value) {
            return translation.value
        }
        return key;
    }
}