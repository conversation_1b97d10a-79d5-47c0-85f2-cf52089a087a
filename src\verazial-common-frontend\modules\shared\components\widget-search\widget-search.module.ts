import {NgModule} from "@angular/core";
import {CommonModule} from "@angular/common";
import {TranslateModule} from "@ngx-translate/core";
import {ToastModule} from "primeng/toast";
import {ProgressSpinnerModule} from "primeng/progressspinner";
import {SkeletonModule} from 'primeng/skeleton';
import {DialogModule} from "primeng/dialog";
import {ButtonModule} from "primeng/button";
import {UiVerifyModule} from "ngx-verazial-ui-lib";
import {WidgetSearchComponent} from "./widget-search/widget-search.component";
import {TooltipModule} from "primeng/tooltip";
import {Ripple} from "primeng/ripple";

@NgModule({
  declarations: [
    WidgetSearchComponent,
  ],
    imports: [
        /* Angular Modules */
        CommonModule,
        /* Translate */
        TranslateModule,
        /* Prime NG Modules */
        ToastModule,
        ProgressSpinnerModule,
        DialogModule,
        ButtonModule,
        SkeletonModule,
        TooltipModule,
        /* Custom Modules */
        UiVerifyModule,
        Ripple,
    ],
  exports: [
    WidgetSearchComponent,
  ]
})
export class WidgetSearchModule { }
