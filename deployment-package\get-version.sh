#!/bin/bash

# Shared function to get version from environment.ts or use default
# Usage: source get-version.sh

get_app_version() {
    local env_file_path="$1"
    local default_version="${2:-v2.0.15}"

    if [ -f "$env_file_path" ]; then
        local app_version=$(grep "version:" "$env_file_path" | head -1 | sed "s/.*version: *['\"]//g" | sed "s/['\"].*//g")
        if [ ! -z "$app_version" ]; then
            echo "v${app_version}"
            return 0
        else
            echo "Could not extract version from $env_file_path, using default: $default_version" >&2
            echo "$default_version"
            return 1
        fi
    else
        echo "$env_file_path not found, using default version: $default_version" >&2
        echo "$default_version"
        return 1
    fi
}

# If script is run directly (not sourced), show the version
if [ "${BASH_SOURCE[0]}" == "${0}" ]; then
    if [ "$1" ]; then
        VERSION=$(get_app_version "$1" "$2")
    else
        VERSION=$(get_app_version "../src/environments/environment.ts")
    fi
    echo "$VERSION"
fi
