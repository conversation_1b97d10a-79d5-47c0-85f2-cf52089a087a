syntax = "proto3";

import "google/protobuf/timestamp.proto";
import "google/protobuf/empty.proto";
import "failure.proto";
import "util.proto";

option java_multiple_files = true;
option java_package = "com.verazial.microservice.admin.features.criminalistics.domain.grpc";
option java_outer_classname = "CriminalisticsGrpcApi";

message CaseGrpcModel{
  optional string id = 1;
  optional string name = 2;
  optional string number = 3;
  optional string description = 4;
  optional string status = 5;
  optional string createdBy = 6;
  optional string updatedBy = 7;
  optional google.protobuf.Timestamp crimeDate = 8;
  optional google.protobuf.Timestamp openDate = 9;
  optional google.protobuf.Timestamp closeDate = 10;
  optional ArrayOfCaseDetailGrpcModel caseDetail = 11;
  optional ArrayOfRelatedCaseGrpcModel relatedCases = 12;
  optional ArrayOfSubjectRelatedCaseGrpcModel relatedSubjects = 13;
  optional ArrayOfCaseLocationGrpcModel locations = 14;
  optional google.protobuf.Timestamp createdAt = 15;
  optional google.protobuf.Timestamp updatedAt = 16;
}

message ArrayOfCase{
  repeated CaseGrpcModel element = 1;
}

message CaseGeneralInfoGrpcModel{
  optional string id = 1;
  optional string name = 2;
  optional string number = 3;
  optional string description = 4;
  optional string status = 5;
  optional string createdBy = 6;
  optional string updatedBy = 7;
  optional google.protobuf.Timestamp crimeDate = 8;
  optional google.protobuf.Timestamp openDate = 9;
  optional google.protobuf.Timestamp closeDate = 10;
  optional google.protobuf.Timestamp createdAt = 11;
  optional google.protobuf.Timestamp updatedAt = 12;
}

message CriminalisticCaseResponse{
  oneof response{
    FailureGrpc failure = 1;
    CaseGrpcModel case = 2;
  }
}

message CriminalisticCaseGeneralInfoResponse{
  oneof response{
    FailureGrpc failure = 1;
    CaseGeneralInfoGrpcModel case = 2;
  }
}

message CaseLocationGrpcModel{
  optional string id = 1;
  optional string caseId = 2;
  optional string location = 3;
  optional string name = 4;
  optional string type = 5;
  optional string comment = 6;
  optional string description = 7;
  optional string latitude = 8;
  optional string longitude = 9;
  optional google.protobuf.Timestamp createdAt = 10;
  optional google.protobuf.Timestamp updatedAt = 11;
}

message ArrayOfCaseLocationGrpcModel{
  repeated CaseLocationGrpcModel element = 1;
}

message ResponseCaseLocation{
  oneof response{
    FailureGrpc failure = 1;
    CaseLocationGrpcModel caseLocation = 2;
  }
}

message RelatedCaseGrpcModel{
  optional string caseId = 1;
  optional string relatedCaseId = 2;
  optional string relation = 3;
  optional google.protobuf.Timestamp createdAt = 4;
  optional google.protobuf.Timestamp updatedAt = 5;
}

message ArrayOfRelatedCaseGrpcModel{
  repeated RelatedCaseGrpcModel element = 1;
}

message CaseDetailGrpcModel{
  optional string id = 1;
  optional string caseId = 2;
  optional string key = 3;
  optional string value = 4;
  optional string createdBy = 5;
  optional string updatedBy = 6;
  optional google.protobuf.Timestamp createdAt = 7;
  optional google.protobuf.Timestamp updatedAt = 8;
}

message ArrayOfCaseDetailGrpcModel{
  repeated CaseDetailGrpcModel element = 1;
}

message ResponseRelatedCase{
  oneof response{
    FailureGrpc failure = 1;
    RelatedCaseGrpcModel relatedCase = 2;
  }
}

message SubjectRelatedCaseGrpcModel{
  optional string caseId = 1;
  optional string relatedSubjectId = 2;
  optional string relation = 3;
  optional google.protobuf.Timestamp createdAt = 4;
  optional google.protobuf.Timestamp updatedAt = 5;
}

message ArrayOfSubjectRelatedCaseGrpcModel{
  repeated SubjectRelatedCaseGrpcModel element = 1;
}

message ResponseSubjectRelatedCase{
  oneof response{
    FailureGrpc failure = 1;
    SubjectRelatedCaseGrpcModel subjectRelatedCase = 2;
  }
}

message CaseEvidenceGrpcModel{
  optional string id = 1;
  optional string caseId = 2;
  optional string rawId = 3;
  optional string name = 4;
  optional string type = 5;
  optional string location = 6;
  optional string status = 7;
  bytes content = 8;
  optional string mimeType = 9;
  optional google.protobuf.Timestamp obtainedAt = 10;
  optional google.protobuf.Timestamp createdAt = 11;
  optional google.protobuf.Timestamp updatedAt = 12;
}

message CaseCommentGrpcModel{
  optional string id = 1;
  optional string caseId = 2;
  optional string comment = 3;
  optional string createdBy = 4;
  optional google.protobuf.Timestamp createdAt = 5;
}

message CaseCoincidenceGrpcModel{
  optional string id = 1;
  optional string caseId = 2;
  optional string evidenceId = 3;
  optional string coincidenceSubjectId = 4;
  optional string type = 5;
  optional string status = 6;
  optional int32 score = 7;
  optional string rawId = 8;
  optional string searchFilters = 9;
  optional string authUserId = 10;
  optional string authUserNumId = 11;
  optional string authUserSignatureTech = 12;
  optional google.protobuf.Timestamp authUserSignatureDate = 13;
  bytes content = 14;
  optional string mimeType = 15;
  optional string createdBy = 16;
  optional string updatedBy = 17;
  optional google.protobuf.Timestamp createdAt = 18;
  optional google.protobuf.Timestamp updatedAt = 19;
}

message CriminalisticCaseCommentResponse{
  oneof response{
    FailureGrpc failure = 1;
    CaseCommentGrpcModel caseComment = 2;
  }
}

message CriminalisticCaseEvidenceResponse{
  oneof response{
    FailureGrpc failure = 1;
    CaseEvidenceGrpcModel caseEvidence = 2;
  }
}

message CriminalisticCaseDetailResponse{
  oneof response{
    FailureGrpc failure = 1;
    ArrayOfCaseDetailGrpcModel caseDetails = 2;
  }
}

message CriminalisticRelatedCasesResponse{
  oneof response{
    FailureGrpc failure = 1;
    ArrayOfRelatedCaseGrpcModel relatedCases = 2;
  }
}

message CriminalisticSubjectRelatedCasesResponse{
  oneof response{
    FailureGrpc failure = 1;
    ArrayOfSubjectRelatedCaseGrpcModel caseRelatedSubjects = 2;
  }
}

message CriminalisticLocationCasesResponse{
  oneof response{
    FailureGrpc failure = 1;
    ArrayOfCaseLocationGrpcModel locationCases = 2;
  }
}

message NumberResponse{
  oneof response{
    FailureGrpc failure = 1;
    int32 value = 2;
  }
}

message CriminalisticCaseCoincidenceResponse{
  oneof response{
    FailureGrpc failure = 1;
    CaseCoincidenceGrpcModel caseCoincidence = 2;
  }
}

service CoreCriminalisticsService {
  rpc createCase(CaseGrpcModel) returns (CriminalisticCaseResponse){};
  rpc updateCase(CaseGrpcModel) returns (CriminalisticCaseResponse){};
  rpc getAllCases(OffsetLimit) returns (stream CaseGrpcModel){};
  rpc getNumberOfCases(google.protobuf.Empty) returns (NumberResponse){};
  rpc getCaseById(StringParam) returns (CriminalisticCaseResponse){};
  rpc createEvidence(CaseEvidenceGrpcModel) returns (CriminalisticCaseEvidenceResponse){};
  rpc updateEvidence(CaseEvidenceGrpcModel) returns (CriminalisticCaseEvidenceResponse){};
  rpc getEvidenceById(StringParam) returns (CriminalisticCaseEvidenceResponse){};
  rpc getEvidenceByCaseId(StringParam) returns (stream CaseEvidenceGrpcModel){};
  rpc deleteEvidenceById(StringParam) returns (FailSuccessResponse){};
  rpc deleteCaseById(StringParam) returns (FailSuccessResponse){};
  rpc deleteAllCaseLocationsById(StringParam) returns (FailSuccessResponse){};
  rpc deleteAllRelatedCasesById(StringParam) returns (FailSuccessResponse){};
  rpc deleteAllRelatedSubjectsById(StringParam) returns (FailSuccessResponse){};
  rpc updateCaseGeneralInfo(CaseGeneralInfoGrpcModel) returns (CriminalisticCaseGeneralInfoResponse){};
  rpc updateCaseDetails(ArrayOfCaseDetailGrpcModel) returns (CriminalisticCaseDetailResponse){};
  rpc updateRelatedCases(ArrayOfRelatedCaseGrpcModel) returns (CriminalisticRelatedCasesResponse){};
  rpc updateRelatedSubjects(ArrayOfSubjectRelatedCaseGrpcModel) returns (CriminalisticSubjectRelatedCasesResponse){};
  rpc updateCaseLocations(ArrayOfCaseLocationGrpcModel) returns (CriminalisticLocationCasesResponse){};
  rpc createComment(CaseCommentGrpcModel) returns (CriminalisticCaseCommentResponse){};
  rpc getAllCommentsByCaseId(StringParam) returns (stream CaseCommentGrpcModel){};
  rpc createCoincidence(CaseCoincidenceGrpcModel) returns (CriminalisticCaseCoincidenceResponse){};
  rpc updateCoincidence(CaseCoincidenceGrpcModel) returns (CriminalisticCaseCoincidenceResponse){};
  rpc deleteCoincidenceById(StringParam) returns (FailSuccessResponse){};
  rpc getAllCoincidencesByCaseId(StringParam) returns (stream CaseCoincidenceGrpcModel){};
  rpc getCoincidenceById(StringParam) returns (CriminalisticCaseCoincidenceResponse){};
}
