import {UseCaseGrpc} from "src/verazial-common-frontend/core/use-case-grpc";
import {
    CaseRepository
} from "src/verazial-common-frontend/core/general/criminalistic-cases/domain/repository/case.repository";
import {
    GetCaseByIdRequestEntity
} from "src/verazial-common-frontend/core/general/criminalistic-cases/domain/entity/get-case-by-id-request.entity";
import {SuccessResponse} from "src/verazial-common-frontend/core/models/success-response.interface";

export class DeleteCoincidenceByIdUseCase implements UseCaseGrpc<GetCaseByIdRequestEntity, SuccessResponse> {
    constructor(private caseRepository: CaseRepository) {
    }

    execute(params: GetCaseByIdRequestEntity): Promise<SuccessResponse> {
        return this.caseRepository.deleteCaseCoincidenceById(params);
    }
}