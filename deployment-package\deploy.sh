#!/bin/bash

# Verazial System Admin Frontend - Deployment Script
# This script builds and deploys the Verazial Admin application

set -e

echo "=========================================="
echo "Verazial Admin Frontend Deployment"
echo "=========================================="

# Check if Docker is running
if ! docker info > /dev/null 2>&1; then
    echo "Error: Docker is not running. Please start Docker and try again."
    exit 1
fi

# Check if docker-compose is available
if ! command -v docker-compose &> /dev/null; then
    echo "Error: docker-compose is not installed. Please install docker-compose and try again."
    exit 1
fi

# Navigate to the deployment directory
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
cd "$SCRIPT_DIR"

echo "Current directory: $(pwd)"

# Check if .env file exists
if [ ! -f ".env" ]; then
    echo "Error: .env file not found. Please ensure the .env file exists in the deployment-package directory."
    exit 1
fi

echo "Loading environment configuration from .env file..."

# Read configuration values for display (without sourcing to avoid shell interpretation issues)
ADMIN_HOST_PORT=$(grep "^ADMIN_HOST_PORT=" .env | cut -d'=' -f2 | tr -d '"')
HOST_PORT=$(grep "^HOST_PORT=" .env | cut -d'=' -f2 | tr -d '"')
GRPC_API_GATEWAY=$(grep "^GRPC_API_GATEWAY=" .env | cut -d'=' -f2 | tr -d '"')
KONEKTOR_API=$(grep "^KONEKTOR_API=" .env | cut -d'=' -f2 | tr -d '"')
CREDENTIALS_API=$(grep "^CREDENTIALS_API=" .env | cut -d'=' -f2 | tr -d '"')
CREDENTIAL_USER=$(grep "^CREDENTIAL_USER=" .env | cut -d'=' -f2 | tr -d '"')

echo "Configuration loaded:"
# Use ADMIN_HOST_PORT if available, otherwise fall back to HOST_PORT
DISPLAY_PORT=${ADMIN_HOST_PORT:-${HOST_PORT:-80}}
echo "  - Host Port: ${DISPLAY_PORT}"
echo "  - gRPC API Gateway: ${GRPC_API_GATEWAY}"
echo "  - Konektor API: ${KONEKTOR_API}"
echo "  - Credentials API: ${CREDENTIALS_API}"
echo "  - Credential User: ${CREDENTIAL_USER}"

# Ask for confirmation
echo ""
read -p "Do you want to proceed with the deployment? (y/N): " -n 1 -r
echo
if [[ ! $REPLY =~ ^[Yy]$ ]]; then
    echo "Deployment cancelled."
    exit 0
fi

echo ""
echo "Building Docker image..."
docker-compose build

echo ""
echo "Starting the application..."
docker-compose up -d

echo ""
echo "=========================================="
echo "Deployment completed successfully!"
echo "=========================================="
echo ""
echo "Application is now running at: http://localhost:${DISPLAY_PORT}"
echo ""
echo "To check the status: docker-compose ps"
echo "To view logs: docker-compose logs -f"
echo "To stop the application: docker-compose down"
echo ""
echo "Configuration used:"
echo "  - gRPC API Gateway: ${GRPC_API_GATEWAY}"
echo "  - Konektor API: ${KONEKTOR_API}"
echo "  - Credentials API: ${CREDENTIALS_API}"
echo ""
