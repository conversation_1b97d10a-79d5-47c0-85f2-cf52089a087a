<p-dialog [(visible)]="showUploadDialog" styleClass="p-fluid" [modal]="true" [closable]="true" [draggable]="false"
          [resizable]="false" [style]="{'width': '40vw'}" (onHide)="onCancelDialog()">
    <ng-template pTemplate="header">
        <div>
            <label for="" [style]="{'color':'#204887', 'font-weight':'700', 'font-size':'20px'}">
                {{ 'titles.evidence' | translate }}
            </label>
        </div>
    </ng-template>
    <ng-template pTemplate="content">
        <div>
            <form [formGroup]="form">
                <div class="grid form-fields">
                    <div *ngIf="showForm" class="lg:col-12 sm:col-12 width100"
                         style="padding-bottom: 0rem; padding-top: 0rem;">
                        <label class="label-form" for="name">{{ 'content.name' | translate }} <span
                                *ngIf="isRequiredField('name')" class="requiredStar">*</span></label>
                        <input type="text" pInputText class="w-full" formControlName="name"/>
                        <small *ngIf="!isValid('name') && form.controls['name'].touched"
                               [style]="{'color': 'red'}">{{ 'messages.error_isRequiredField' | translate }}</small>
                    </div>
                    <div *ngIf="showForm" class="lg:col-6 sm:col-12 width100"
                         style="padding-bottom: 0rem; padding-top: 0rem;">
                        <label class="label-form" for="location">{{ 'content.location' | translate }}<span
                                *ngIf="isRequiredField('location')" class="requiredStar">*</span></label>
                        <input type="text" pInputText class="w-full" formControlName="location"/>
                        <small *ngIf="!isValid('location') && form.controls['location'].touched"
                               [style]="{'color': 'red'}">{{ 'messages.error_isRequiredField' | translate }}</small>
                    </div>
                    <div *ngIf="showForm" class="lg:col-6 sm:col-12 width100"
                         style="padding-bottom: 0rem; padding-top: 0rem;">
                        <label class="label-form" for="obtainedDate">{{ 'content.obtainedAt' | translate }} <span
                                *ngIf="isRequiredField('obtainedDate')" class="requiredStar">*</span></label>
                        <p-calendar appendTo="body" formControlName="obtainedDate"
                                    [maxDate]="maxDate" [iconDisplay]="'input'"
                                    [showIcon]="true" inputId="obtainedDate"
                                    dateFormat="{{ 'dateFormat' | translate }}"
                                    styleClass="w-full"/>
                        <small *ngIf="!isValid('obtainedDate') && form.controls['obtainedDate'].touched"
                               [style]="{'color': 'red'}">{{ 'messages.error_isRequiredField' | translate }}</small>
                    </div>
                    <div class="col-12 width100" style="padding-bottom: 0rem; padding-top: 0rem;">
                        <label class="label-form">{{ 'content.file' | translate }}</label>
                        <p-fileUpload
                                name="myfile[]"
                                accept="{{ acceptedFiles }}"
                                maxFileSize="{{ maxFileSize }}"
                                invalidFileSizeMessageSummary="{{ 'messages.file_size_exceeded' | translate }}"
                                invalidFileSizeMessageDetail="{{ 'messages.file_size_exceeded_detail' | translate }}"
                                invalidFileTypeMessageSummary="{{ 'messages.invalid_file_type' | translate }}"
                                invalidFileTypeMessageDetail="{{ 'messages.invalid_file_type_detail' | translate }}"
                                invalidFileLimitMessageDetail="{{ 'messages.invalid_file_limit_detail' | translate }}"
                                invalidFileLimitMessageSummary="{{ 'messages.invalid_file_limit' | translate }}"
                                uploadStyleClass="p-button-success"
                                cancelStyleClass="p-button-danger"
                                cancelLabel="{{ 'content.remove' | translate }}"
                                [customUpload]="true"
                                (uploadHandler)="onUploadFiles($event)"
                                (onSelect)="onSelectedFiles($event)"
                        >
                            <ng-template pTemplate="content" let-files let-removeFileCallback="removeFileCallback">
                                <div *ngIf="files?.length > 0"
                                     class="flex flex-column flex-justify-content-center align-items-center">
                                    <div class="flex flex-wrap p-0 sm:p-5 gap-5">
                                        <div *ngFor="let file of files"
                                             class="card m-0 p-3 border-1 surface-border">
                                            <div class="flex column justify-content-between w-full">
                                                <span class="font-semibold">{{ file.name }}</span>
                                                <i class="pi pi-times ml-3"
                                                   style="font-size: 1rem; color: var(--danger); cursor: pointer;"
                                                   pTooltip="{{ 'content.remove' | translate }}" tooltipPosition="top"
                                                   ></i>
                                            </div>
                                            <div class="flex mt-3 flex-column align-items-center gap-3 px-6">
                                                <img *ngIf="file.type.includes('image') else noImage"
                                                     role="presentation" [alt]="file.name" [src]="file.objectURL"
                                                     width="100"/>
                                                <ng-template #noImage>
                                                    <i class="pi pi-file" style="font-size: 2rem;"></i>
                                                </ng-template>
                                                <div>{{ formatSize(file.size) }}</div>
                                                <p-badge value="{{ 'content.pending' | translate }}"
                                                         severity="warning"/>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </ng-template>
                            <ng-template pTemplate="file"></ng-template>
                            <ng-template pTemplate="empty">
                                <div class="flex align-items-center justify-content-center flex-column">
                                    <img src="verazial-common-frontend/assets/images/all/uploadFile.svg" alt="empty"
                                         width="100" height="100">
                                    <p class="mt-4 mb-0">{{ 'content.dragAndDropFilesHereToUpload' | translate }}</p>
                                </div>
                            </ng-template>
                        </p-fileUpload>
                    </div>
                </div>
            </form>
        </div>
    </ng-template>
    <ng-template pTemplate="footer">
        <div class="flex flex-row justify-content-end">
            <button pButton pRipple label="{{ 'cancel' | translate }}" class="p-button-text"
                    (click)="onCancelDialog()"></button>
        </div>
    </ng-template>
</p-dialog>