#!/bin/bash
# Script to load the Verazial Admin Docker image

echo "Loading Verazial Admin Docker image..."
docker load -i verazial-admin-v2.0.16.tar

echo "Image loaded successfully!"
echo "Available images:"
docker images | grep verazial/admin

echo ""
echo "Next steps:"
echo "1. Edit the .env file with your configuration"
echo "2. Run: docker-compose up -d"
echo "3. Access the application at http://localhost:[HOST_PORT]"
